#pragma once

#include <QObject>
#include <QString>
#include <QVariantMap>
#include <memory>

namespace Config {

/**
 * @brief 抽象配置数据接口
 *
 * 所有配置数据类都必须实现此接口，以支持：
 * - 类型识别和动态创建
 * - 序列化和反序列化
 * - 数据验证
 * - 默认值设置
 *
 * 设计原则：
 * - 开闭原则：新增配置类型无需修改接口
 * - 依赖倒置：依赖抽象接口，不依赖具体实现
 * - 单一职责：只负责配置数据的基本操作
 */
class IConfigData {
  public:
    virtual ~IConfigData() = default;

    /**
     * @brief 获取配置类型名称
     * @return 配置类型的唯一标识符
     */
    virtual QString getTypeName() const = 0;

    /**
     * @brief 获取配置版本
     * @return 配置数据的版本号，用于兼容性检查
     */
    virtual QString getVersion() const {
        return "1.0.0";
    }

    /**
     * @brief 将配置数据转换为QVariantMap
     * @return 包含所有配置字段的映射表
     */
    virtual QVariantMap toVariantMap() const = 0;

    /**
     * @brief 从QVariantMap加载配置数据
     * @param data 包含配置数据的映射表
     * @return 加载是否成功
     */
    virtual bool fromVariantMap(const QVariantMap &data) = 0;

    /**
     * @brief 验证配置数据的有效性
     * @return 验证是否通过
     */
    virtual bool validate() const = 0;

    /**
     * @brief 设置默认配置值
     */
    virtual void setDefaults() = 0;

    /**
     * @brief 克隆配置对象
     * @return 配置对象的深拷贝
     */
    virtual std::unique_ptr<IConfigData> clone() const = 0;

    /**
     * @brief 获取配置描述信息
     * @return 配置的描述文本
     */
    virtual QString getDescription() const {
        return QString("Configuration data for %1").arg(getTypeName());
    }

    /**
     * @brief 获取所有配置字段的名称
     * @return 配置字段名称列表
     */
    virtual QStringList getFieldNames() const = 0;

    /**
     * @brief 获取指定字段的类型信息
     * @param fieldName 字段名称
     * @return 字段类型描述
     */
    virtual QString getFieldType(const QString &fieldName) const = 0;

    /**
     * @brief 获取指定字段的描述信息
     * @param fieldName 字段名称
     * @return 字段描述文本
     */
    virtual QString getFieldDescription(const QString &fieldName) const = 0;

    /**
     * @brief 检查是否包含指定字段
     * @param fieldName 字段名称
     * @return 是否包含该字段
     */
    virtual bool hasField(const QString &fieldName) const = 0;

    /**
     * @brief 获取字段值
     * @param fieldName 字段名称
     * @param defaultValue 默认值
     * @return 字段值
     */
    virtual QVariant getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const = 0;

    /**
     * @brief 设置字段值
     * @param fieldName 字段名称
     * @param value 字段值
     * @return 设置是否成功
     */
    virtual bool setFieldValue(const QString &fieldName, const QVariant &value) = 0;

    /**
     * @brief 重置指定字段为默认值
     * @param fieldName 字段名称
     * @return 重置是否成功
     */
    virtual bool resetField(const QString &fieldName) = 0;

    /**
     * @brief 比较两个配置对象是否相等
     * @param other 另一个配置对象
     * @return 是否相等
     */
    virtual bool equals(const IConfigData &other) const {
        if (getTypeName() != other.getTypeName()) {
            return false;
        }
        return toVariantMap() == other.toVariantMap();
    }

    /**
     * @brief 获取配置数据的哈希值
     * @return 哈希值
     */
    virtual uint hash() const {
        // 为QVariantMap实现自定义哈希函数
        QVariantMap map  = toVariantMap();
        uint        hash = 0;
        for (auto it = map.begin(); it != map.end(); ++it) {
            hash ^= qHash(it.key()) ^ qHash(it.value().toString());
        }
        return hash;
    }

    /**
     * @brief 获取配置数据的JSON字符串表示
     * @return JSON字符串
     */
    virtual QString toJsonString() const;

    /**
     * @brief 从JSON字符串加载配置数据
     * @param jsonString JSON字符串
     * @return 加载是否成功
     */
    virtual bool fromJsonString(const QString &jsonString);

  protected:
    /**
     * @brief 记录日志信息
     * @param message 日志消息
     */
    void logInfo(const QString &message) const;

    /**
     * @brief 记录错误信息
     * @param message 错误消息
     */
    void logError(const QString &message) const;

    /**
     * @brief 记录警告信息
     * @param message 警告消息
     */
    void logWarning(const QString &message) const;
};

/**
 * @brief 配置数据基类模板
 *
 * 提供IConfigData接口的部分默认实现，简化具体配置类的开发
 */
template <typename T> class BaseConfigData : public IConfigData {
  public:
    std::unique_ptr<IConfigData> clone() const override {
        auto cloned = std::make_unique<T>();
        cloned->fromVariantMap(this->toVariantMap());
        return std::move(cloned);
    }

    QString getTypeName() const override {
        return T::staticTypeName();
    }

  protected:
    /**
     * @brief 验证字段值的范围
     * @param value 字段值
     * @param min 最小值
     * @param max 最大值
     * @return 是否在有效范围内
     */
    template <typename ValueType> bool validateRange(const ValueType &value, const ValueType &min, const ValueType &max) const {
        return value >= min && value <= max;
    }

    /**
     * @brief 验证字符串字段是否为空
     * @param value 字符串值
     * @return 是否非空
     */
    bool validateNotEmpty(const QString &value) const {
        return !value.trimmed().isEmpty();
    }

    /**
     * @brief 验证枚举值是否有效
     * @param value 枚举值
     * @param validValues 有效值列表
     * @return 是否有效
     */
    template <typename ValueType> bool validateEnum(const ValueType &value, const QList<ValueType> &validValues) const {
        return validValues.contains(value);
    }
};

}  // namespace Config
