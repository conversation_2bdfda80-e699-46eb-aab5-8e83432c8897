#include "JsonLoader.h"
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QDebug>
#include <QJsonParseError>

namespace SaveLoad {

JsonLoader::JsonLoader() 
    : m_lastErrorType(DataErrorType::None) {
    // 设置默认选项
    m_options["indented"] = true;
    m_options["compact"] = false;
    m_options["encoding"] = "UTF-8";
}

JsonLoader::~JsonLoader() {
}

// 基本文件操作
DataResult JsonLoader::load(const QString& filePath) {
    clearError();
    
    if (!QFile::exists(filePath)) {
        return setLastError(DataErrorType::FileNotFound, 
                           QString("File not found: %1").arg(filePath));
    }
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return setLastError(DataErrorType::PermissionError, 
                           QString("Cannot open file for reading: %1").arg(filePath),
                           file.errorString());
    }
    
    QByteArray content = file.readAll();
    file.close();
    
    return parseJsonContent(content);
}

DataResult JsonLoader::save(const QString& filePath, const DataContainer& data) {
    clearError();
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return setLastError(DataErrorType::PermissionError, 
                           QString("Cannot open file for writing: %1").arg(filePath),
                           file.errorString());
    }
    
    QByteArray content = formatJsonOutput(data);
    qint64 bytesWritten = file.write(content);
    file.close();
    
    if (bytesWritten != content.size()) {
        return setLastError(DataErrorType::PermissionError, 
                           QString("Failed to write complete data to file: %1").arg(filePath));
    }
    
    return DataResult::Success();
}

DataResult JsonLoader::loadFromString(const QString& content) {
    clearError();
    return parseJsonContent(content.toUtf8());
}

QString JsonLoader::saveToString(const DataContainer& data) const {
    return QString::fromUtf8(formatJsonOutput(data));
}

// 格式支持查询
QStringList JsonLoader::supportedFormats() const {
    return QStringList() << "json";
}

bool JsonLoader::canHandle(const QString& filePath) const {
    return isJsonFile(filePath);
}

QString JsonLoader::getFormatName() const {
    return "JSON";
}

// 数据访问
QVariant JsonLoader::getValue(const QString& key, const QVariant& defaultValue) const {
    return m_data.getVariant(key, defaultValue);
}

DataResult JsonLoader::setValue(const QString& key, const QVariant& value) {
    m_data.setVariant(key, value);
    return DataResult::Success();
}

bool JsonLoader::hasKey(const QString& key) const {
    return m_data.contains(key);
}

QStringList JsonLoader::keys() const {
    return m_data.keys();
}

DataContainer JsonLoader::getDataContainer() const {
    return m_data;
}

DataResult JsonLoader::setDataContainer(const DataContainer& data) {
    m_data = data;
    return DataResult::Success();
}

// 验证功能
ValidationResult JsonLoader::validate() const {
    ValidationResult result;
    
    if (m_data.isEmpty()) {
        result.addWarning("Data container is empty");
    }
    
    // 基本JSON结构验证
    QString jsonStr = m_data.toJsonString();
    QString errorMsg;
    if (!validateJsonSyntax(jsonStr, &errorMsg)) {
        result.addError("Invalid JSON structure: " + errorMsg);
    }
    
    return result;
}

ValidationResult JsonLoader::validateWithSchema(const QString& schemaPath) const {
    ValidationResult result;
    
    if (!QFile::exists(schemaPath)) {
        result.addError(QString("Schema file not found: %1").arg(schemaPath));
        return result;
    }
    
    QFile schemaFile(schemaPath);
    if (!schemaFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        result.addError(QString("Cannot open schema file: %1").arg(schemaPath));
        return result;
    }
    
    QString schemaContent = schemaFile.readAll();
    schemaFile.close();
    
    return validateWithJsonSchema(schemaContent);
}

// 配置选项
void JsonLoader::setOption(const QString& key, const QVariant& value) {
    m_options[key] = value;
}

QVariant JsonLoader::getOption(const QString& key, const QVariant& defaultValue) const {
    return m_options.value(key, defaultValue);
}

// 错误处理
QString JsonLoader::getLastError() const {
    return m_lastError;
}

void JsonLoader::clearError() {
    m_lastError.clear();
    m_lastErrorType = DataErrorType::None;
}

DataResult JsonLoader::setLastError(DataErrorType type, const QString& message, const QString& details) {
    m_lastErrorType = type;
    m_lastError = message;
    
    QString fullMessage = message;
    if (!details.isEmpty()) {
        fullMessage += QString(" (%1)").arg(details);
    }
    
    logError("JsonLoader", fullMessage);
    
    return DataResult(type, message, details);
}

// JSON特有功能
DataResult JsonLoader::loadFromByteArray(const QByteArray& data) {
    clearError();
    return parseJsonContent(data);
}

QByteArray JsonLoader::saveToByteArray(const DataContainer& data) const {
    return formatJsonOutput(data);
}

DataResult JsonLoader::loadPartial(const QString& filePath, const QStringList& keys) {
    // 先加载完整文件
    DataResult result = load(filePath);
    if (!result.success) {
        return result;
    }
    
    // 创建只包含指定键的新容器
    DataContainer partialData;
    for (const QString& key : keys) {
        if (m_data.contains(key)) {
            partialData.setVariant(key, m_data.getVariant(key));
        }
    }
    
    m_data = partialData;
    return DataResult::Success();
}

DataResult JsonLoader::saveStream(const QString& filePath, const DataContainer& data) {
    // 对于JSON，流式保存与普通保存相同
    // 在未来可以实现真正的流式处理
    return save(filePath, data);
}

// 内部工具方法
bool JsonLoader::isJsonFile(const QString& filePath) const {
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();
    return suffix == "json";
}

DataResult JsonLoader::parseJsonContent(const QByteArray& content) {
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(content, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        return setLastError(DataErrorType::ParseError, 
                           "JSON parse error", 
                           formatParseError(parseError));
    }
    
    if (!doc.isObject()) {
        return setLastError(DataErrorType::ParseError, 
                           "JSON document is not an object");
    }
    
    m_data = DataContainer::fromJsonDocument(doc);
    return DataResult::Success();
}

QByteArray JsonLoader::formatJsonOutput(const DataContainer& data) const {
    QJsonDocument doc = data.toJsonDocument();
    
    bool compact = getOption("compact", false).toBool();
    bool indented = getOption("indented", true).toBool();
    
    if (compact) {
        return doc.toJson(QJsonDocument::Compact);
    } else if (indented) {
        return doc.toJson(QJsonDocument::Indented);
    } else {
        return doc.toJson();
    }
}

bool JsonLoader::validateJsonSyntax(const QString& content, QString* errorMsg) const {
    QJsonParseError parseError;
    QJsonDocument::fromJson(content.toUtf8(), &parseError);
    
    if (errorMsg) {
        *errorMsg = parseError.errorString();
    }
    
    return parseError.error == QJsonParseError::NoError;
}

ValidationResult JsonLoader::validateWithJsonSchema(const QString& schemaContent) const {
    ValidationResult result;
    
    // 简单的Schema验证实现
    // 在实际项目中，可以使用专门的JSON Schema验证库
    Q_UNUSED(schemaContent);
    
    result.addWarning("JSON Schema validation not fully implemented");
    return result;
}

void JsonLoader::logError(const QString& operation, const QString& details) const {
    qWarning() << QString("[%1] %2").arg(operation, details);
}

QString JsonLoader::formatParseError(const QJsonParseError& error) const {
    return QString("Parse error at offset %1: %2")
           .arg(error.offset)
           .arg(error.errorString());
}

// JsonLoaderFactory实现
std::unique_ptr<IDataHandler> JsonLoaderFactory::createHandler() const {
    return std::make_unique<JsonLoader>();
}

QStringList JsonLoaderFactory::supportedFormats() const {
    return QStringList() << "json";
}

QString JsonLoaderFactory::getFormatName() const {
    return "JSON";
}

bool JsonLoaderFactory::canHandle(const QString& filePath) const {
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();
    return suffix == "json";
}

} // namespace SaveLoad
