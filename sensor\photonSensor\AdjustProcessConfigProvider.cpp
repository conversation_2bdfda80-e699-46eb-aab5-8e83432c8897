#include "AdjustProcessConfigProvider.h"
#include <QApplication>
#include <QDebug>

namespace AdjustProcess {

// 静态常量定义
const QString AdjustProcessConfigProvider::KEY_FACULA_OK_TIMES  = "facula_ok_times";
const QString AdjustProcessConfigProvider::KEY_SOLID_TIME       = "solid_time";
const QString AdjustProcessConfigProvider::KEY_FACULA_NG_HANDLE = "facula_ng_handle";

const uint8_t  AdjustProcessConfigProvider::DEFAULT_FACULA_OK_TIMES  = 3;
const uint32_t AdjustProcessConfigProvider::DEFAULT_SOLID_TIME       = 0;
const uint8_t  AdjustProcessConfigProvider::DEFAULT_FACULA_NG_HANDLE = 1;

const uint8_t  AdjustProcessConfigProvider::MIN_FACULA_OK_TIMES  = 1;
const uint8_t  AdjustProcessConfigProvider::MAX_FACULA_OK_TIMES  = 10;
const uint32_t AdjustProcessConfigProvider::MAX_SOLID_TIME       = 10000;
const uint8_t  AdjustProcessConfigProvider::MAX_FACULA_NG_HANDLE = 2;

AdjustProcessConfigProvider::AdjustProcessConfigProvider(QObject *parent) : BaseConfigProvider(parent) {
    // 在子类构造函数中调用加载默认参数
    loadDefaultParameters();
    logInfo("AdjustProcessConfigProvider initialized");
}

QString AdjustProcessConfigProvider::getConfigFilePath() const {
    return QApplication::applicationDirPath() + "/config/modules/adjust_process/adjust_process_config.ini";
}

void AdjustProcessConfigProvider::loadDefaultParameters() {
    // 设置默认参数值
    setParameterDefault(KEY_FACULA_OK_TIMES, DEFAULT_FACULA_OK_TIMES);
    setParameterDefault(KEY_SOLID_TIME, DEFAULT_SOLID_TIME);
    setParameterDefault(KEY_FACULA_NG_HANDLE, DEFAULT_FACULA_NG_HANDLE);

    // 设置参数信息
    setParameterInfo(
        KEY_FACULA_OK_TIMES, "光斑判定次数，连续多少次判定为OK才认为调节成功", "uint8_t", QString("%1-%2").arg(MIN_FACULA_OK_TIMES).arg(MAX_FACULA_OK_TIMES));

    setParameterInfo(KEY_SOLID_TIME, "固化时间，调节完成后的等待时间(毫秒)", "uint32_t", QString("0-%1").arg(MAX_SOLID_TIME));

    setParameterInfo(KEY_FACULA_NG_HANDLE, "异常处理方式：0-停止，1-继续，2-重试", "uint8_t", QString("0-%1").arg(MAX_FACULA_NG_HANDLE));

    logInfo("Default adjust process parameters loaded");
}

Config::ConfigResult AdjustProcessConfigProvider::validateParameter(const QString &key, const QVariant &value) const {
    // 先调用基类验证
    Config::ConfigResult baseResult = BaseConfigProvider::validateParameter(key, value);
    if (!baseResult.success) {
        return baseResult;
    }

    // 调节流程特定的验证
    if (key == KEY_FACULA_OK_TIMES) {
        uint8_t times = value.toUInt();
        if (!isValidFaculaOkTimes(times)) {
            return Config::ConfigResult(false,
                                        Config::ErrorType::ValidationError,
                                        QString("Facula OK times %1 out of range [%2-%3]").arg(times).arg(MIN_FACULA_OK_TIMES).arg(MAX_FACULA_OK_TIMES));
        }
    } else if (key == KEY_SOLID_TIME) {
        uint32_t time = value.toUInt();
        if (!isValidSolidTime(time)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("Solid time %1ms out of range [0-%2]").arg(time).arg(MAX_SOLID_TIME));
        }
    } else if (key == KEY_FACULA_NG_HANDLE) {
        uint8_t handle = value.toUInt();
        if (!isValidFaculaNgHandle(handle)) {
            return Config::ConfigResult(
                false, Config::ErrorType::ValidationError, QString("NG handle type %1 out of range [0-%2]").arg(handle).arg(MAX_FACULA_NG_HANDLE));
        }
    }

    return Config::ConfigResult(true);
}

QString AdjustProcessConfigProvider::getParameterRange(const QString &key) const {
    if (key == KEY_FACULA_OK_TIMES) {
        return QString("%1-%2").arg(MIN_FACULA_OK_TIMES).arg(MAX_FACULA_OK_TIMES);
    } else if (key == KEY_SOLID_TIME) {
        return QString("0-%1").arg(MAX_SOLID_TIME);
    } else if (key == KEY_FACULA_NG_HANDLE) {
        return QString("0-%1 (0-停止，1-继续，2-重试)").arg(MAX_FACULA_NG_HANDLE);
    }

    return BaseConfigProvider::getParameterRange(key);
}

void AdjustProcessConfigProvider::onParameterChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue) {
    BaseConfigProvider::onParameterChanged(key, oldValue, newValue);

    if (key == KEY_FACULA_OK_TIMES) {
        logInfo(QString("Facula OK times updated: %1 -> %2").arg(oldValue.toUInt()).arg(newValue.toUInt()));
    } else if (key == KEY_SOLID_TIME) {
        logInfo(QString("Solid time updated: %1ms -> %2ms").arg(oldValue.toUInt()).arg(newValue.toUInt()));
    } else if (key == KEY_FACULA_NG_HANDLE) {
        uint8_t oldHandle = oldValue.toUInt();
        uint8_t newHandle = newValue.toUInt();
        logInfo(QString("NG handle updated: %1(%2) -> %3(%4)")
                    .arg(oldHandle)
                    .arg(getFaculaNgHandleDescription(oldHandle))
                    .arg(newHandle)
                    .arg(getFaculaNgHandleDescription(newHandle)));
    }
}

uint8_t AdjustProcessConfigProvider::getFaculaOkTimes() const {
    return getParameter(KEY_FACULA_OK_TIMES, DEFAULT_FACULA_OK_TIMES).toUInt();
}

void AdjustProcessConfigProvider::setFaculaOkTimes(uint8_t times) {
    setParameter(KEY_FACULA_OK_TIMES, times);
}

uint32_t AdjustProcessConfigProvider::getSolidTime() const {
    return getParameter(KEY_SOLID_TIME, DEFAULT_SOLID_TIME).toUInt();
}

void AdjustProcessConfigProvider::setSolidTime(uint32_t time) {
    setParameter(KEY_SOLID_TIME, time);
}

uint8_t AdjustProcessConfigProvider::getFaculaNgHandle() const {
    return getParameter(KEY_FACULA_NG_HANDLE, DEFAULT_FACULA_NG_HANDLE).toUInt();
}

void AdjustProcessConfigProvider::setFaculaNgHandle(uint8_t handle) {
    setParameter(KEY_FACULA_NG_HANDLE, handle);
}

QString AdjustProcessConfigProvider::getFaculaNgHandleDescription(uint8_t handle) const {
    switch (static_cast<NgHandleType>(handle)) {
    case NgHandleType::Stop:
        return "停止";
    case NgHandleType::Continue:
        return "继续";
    case NgHandleType::Retry:
        return "重试";
    default:
        return "未知";
    }
}

QString AdjustProcessConfigProvider::getCurrentFaculaNgHandleDescription() const {
    return getFaculaNgHandleDescription(getFaculaNgHandle());
}

bool AdjustProcessConfigProvider::isValidFaculaOkTimes(uint8_t times) const {
    return times >= MIN_FACULA_OK_TIMES && times <= MAX_FACULA_OK_TIMES;
}

bool AdjustProcessConfigProvider::isValidSolidTime(uint32_t time) const {
    return time <= MAX_SOLID_TIME;
}

bool AdjustProcessConfigProvider::isValidFaculaNgHandle(uint8_t handle) const {
    return handle <= MAX_FACULA_NG_HANDLE;
}

}  // namespace AdjustProcess
