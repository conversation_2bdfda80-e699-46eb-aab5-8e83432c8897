#pragma once

#include "IConfigProvider.h"
#include "ConfigTypes.h"
#include <QSettings>
#include <QFileSystemWatcher>
#include <QVariantMap>
#include <QMutex>

namespace Config {

/**
 * @brief 基础配置提供者实现
 * 
 * 为具体的配置提供者提供通用功能实现：
 * - 配置文件的读写操作
 * - 文件监控和自动重载
 * - 参数验证和类型转换
 * - 线程安全的参数访问
 * 
 * 子类只需要：
 * - 实现 getModuleName()
 * - 实现 getConfigFilePath()
 * - 实现 loadDefaultParameters()
 * - 可选择重写验证逻辑
 */
class BaseConfigProvider : public IConfigProvider {
    Q_OBJECT

public:
    explicit BaseConfigProvider(QObject *parent = nullptr);
    ~BaseConfigProvider() override;

    // IConfigProvider 接口实现
    ConfigResult loadConfig() override;
    ConfigResult saveConfig() override;
    ConfigResult validateConfig() const override;
    QVariant getParameter(const QString &key, const QVariant &defaultValue = QVariant()) const override;
    bool setParameter(const QString &key, const QVariant &value) override;
    QVariantMap getAllParameters() const override;
    ConfigResult resetToDefault() override;
    bool hasParameter(const QString &key) const override;
    QString getParameterType(const QString &key) const override;
    QString getParameterDescription(const QString &key) const override;

protected:
    /**
     * @brief 加载默认参数
     * 子类必须实现此方法来定义默认配置
     */
    virtual void loadDefaultParameters() = 0;

    /**
     * @brief 验证单个参数
     * @param key 参数名
     * @param value 参数值
     * @return 验证结果
     */
    virtual ConfigResult validateParameter(const QString &key, const QVariant &value) const;

    /**
     * @brief 获取参数的有效范围
     * @param key 参数名
     * @return 有效范围描述
     */
    virtual QString getParameterRange(const QString &key) const;

    /**
     * @brief 参数变更前的处理
     * @param key 参数名
     * @param oldValue 旧值
     * @param newValue 新值
     * @return 是否允许变更
     */
    virtual bool onParameterChanging(const QString &key, const QVariant &oldValue, const QVariant &newValue);

    /**
     * @brief 参数变更后的处理
     * @param key 参数名
     * @param oldValue 旧值
     * @param newValue 新值
     */
    virtual void onParameterChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue);

    /**
     * @brief 设置参数描述信息
     * @param key 参数名
     * @param description 描述信息
     * @param type 参数类型
     * @param range 有效范围
     */
    void setParameterInfo(const QString &key, const QString &description, const QString &type = "QVariant", const QString &range = "");

    /**
     * @brief 设置参数默认值
     * @param key 参数名
     * @param defaultValue 默认值
     */
    void setParameterDefault(const QString &key, const QVariant &defaultValue);

    /**
     * @brief 批量设置参数
     * @param parameters 参数映射表
     */
    void setParameters(const QVariantMap &parameters);

private Q_SLOTS:
    /**
     * @brief 配置文件变更处理
     * @param path 文件路径
     */
    void onConfigFileChanged(const QString &path);

private:
    /**
     * @brief 初始化文件监控
     */
    void initializeFileWatcher();

    /**
     * @brief 清理文件监控
     */
    void cleanupFileWatcher();

    /**
     * @brief 确保配置目录存在
     * @return 是否成功
     */
    bool ensureConfigDirectory();

    /**
     * @brief 从配置文件加载参数
     * @return 加载结果
     */
    ConfigResult loadFromFile();

    /**
     * @brief 保存参数到配置文件
     * @return 保存结果
     */
    ConfigResult saveToFile();

    /**
     * @brief 发出配置变更信号
     * @param key 参数名
     * @param oldValue 旧值
     * @param newValue 新值
     */
    void emitConfigChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue);

protected:
    QVariantMap m_parameters;           // 配置参数
    QVariantMap m_defaultParameters;    // 默认参数
    
private:
    struct ParameterInfo {
        QString description;
        QString type;
        QString range;
    };
    
    QMap<QString, ParameterInfo> m_parameterInfo;  // 参数信息
    QFileSystemWatcher *m_fileWatcher;             // 文件监控器
    mutable QMutex m_mutex;                        // 线程安全锁
    bool m_fileWatcherEnabled;                     // 文件监控是否启用
    QString m_lastConfigFilePath;                  // 上次的配置文件路径
};

} // namespace Config
