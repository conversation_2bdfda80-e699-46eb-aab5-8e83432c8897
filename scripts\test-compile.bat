@echo off
REM 编译测试脚本
REM 用于测试配置模块重构后的编译情况

echo ========================================
echo 配置模块编译测试
echo ========================================

REM 设置编码为UTF-8
chcp 65001 > nul

REM 检查必要的工具
echo 1. 检查编译环境...

REM 检查CMake
cmake --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ CMake未找到，请安装CMake
    pause
    exit /b 1
) else (
    echo ✅ CMake已安装
)

REM 检查Ninja
ninja --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Ninja未找到，将使用默认生成器
) else (
    echo ✅ Ninja已安装
)

echo.
echo 2. 清理构建目录...
if exist "build" (
    rmdir /s /q "build"
    echo ✅ 已清理构建目录
)

echo.
echo 3. 创建构建目录...
mkdir build
cd build

echo.
echo 4. 配置CMake...
echo 尝试使用MinGW工具链配置...

REM 尝试不同的配置方式
cmake -G "Ninja" -DCMAKE_BUILD_TYPE=Debug -DENABLE_CONFIG_TESTS=ON .. 2>cmake_error.log
if %errorlevel% neq 0 (
    echo ❌ Ninja配置失败，尝试使用MinGW Makefiles...
    cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug -DENABLE_CONFIG_TESTS=ON .. 2>cmake_error.log
    if %errorlevel% neq 0 (
        echo ❌ MinGW Makefiles配置失败，尝试使用默认生成器...
        cmake -DCMAKE_BUILD_TYPE=Debug -DENABLE_CONFIG_TESTS=ON .. 2>cmake_error.log
        if %errorlevel% neq 0 (
            echo ❌ CMake配置失败！
            echo 错误信息：
            type cmake_error.log
            echo.
            echo 可能的解决方案：
            echo 1. 检查Qt安装路径是否正确
            echo 2. 检查MinGW编译器是否安装
            echo 3. 更新cmake/mingw-toolchain.cmake中的路径
            cd ..
            pause
            exit /b 1
        )
    )
)

echo ✅ CMake配置成功

echo.
echo 5. 开始编译...
cmake --build . --config Debug 2>build_error.log
if %errorlevel% neq 0 (
    echo ❌ 编译失败！
    echo 错误信息：
    type build_error.log
    echo.
    echo 可能的解决方案：
    echo 1. 检查头文件包含路径
    echo 2. 检查源文件语法错误
    echo 3. 检查链接库配置
    cd ..
    pause
    exit /b 1
) else (
    echo ✅ 编译成功！
)

echo.
echo 6. 检查生成的可执行文件...
if exist "LIDAR_IA.exe" (
    echo ✅ 可执行文件生成成功: LIDAR_IA.exe
    
    echo.
    echo 7. 运行配置模块测试...
    echo 启动程序进行配置模块测试...
    echo 注意：程序将在2秒后自动开始配置模块测试
    echo.
    
    REM 运行程序（后台运行，避免阻塞）
    start "" "LIDAR_IA.exe"
    
    echo 程序已启动，请查看控制台输出中的配置模块测试结果
    echo 测试内容包括：
    echo - 配置类型注册测试
    echo - 动态配置管理器测试
    echo - 配置序列化测试
    echo - 所有模块配置测试
    
) else (
    echo ❌ 可执行文件未生成
)

cd ..

echo.
echo ========================================
echo 编译测试完成
echo ========================================
echo.
echo 如果遇到问题，请检查：
echo 1. cmake/mingw-toolchain.cmake 中的路径配置
echo 2. .vscode/c_cpp_properties.json 中的Qt路径
echo 3. 系统环境变量中的编译器路径
echo.

pause
