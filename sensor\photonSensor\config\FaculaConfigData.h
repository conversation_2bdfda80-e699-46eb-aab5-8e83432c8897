#pragma once

#include "../../../components/configModule/ConfigTypeRegistry.h"
#include "../../../components/configModule/IConfigData.h"
#include <QPoint>
#include <QString>
#include <QStringList>
#include <QVariantMap>
#include <QVector>

namespace PhotonSensor {

/**
 * @brief 光斑配置数据
 *
 * 管理光斑检测和处理的所有配置参数：
 * - 多通道光斑中心配置
 * - 光斑检测阈值参数
 * - 光斑处理类型设置
 * - 兼容性配置参数
 *
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在photonSensor模块中定义和管理
 * - 专门用于光斑检测和处理
 * - 支持多通道和单点模式
 */
class FaculaConfigData : public Config::BaseConfigData<FaculaConfigData> {
  public:
    FaculaConfigData();
    ~FaculaConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() {
        return "Facula";
    }

    // IConfigData接口实现
    QString getTypeName() const override {
        return staticTypeName();
    }
    QString getVersion() const override {
        return "1.0.0";
    }
    QString getDescription() const override {
        return "光斑检测配置，包含多通道光斑中心和处理参数";
    }

    QVariantMap toVariantMap() const override;
    bool        fromVariantMap(const QVariantMap &data) override;
    bool        validate() const override;
    void        setDefaults() override;

    QStringList getFieldNames() const override;
    QString     getFieldType(const QString &fieldName) const override;
    QString     getFieldDescription(const QString &fieldName) const override;
    bool        hasField(const QString &fieldName) const override;
    QVariant    getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool        setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool        resetField(const QString &fieldName) override;

    // 多通道配置访问接口
    /**
     * @brief 获取多通道配置字符串
     * @return 多通道配置字符串 (格式: "x1,y1;x2,y2;...")
     */
    const QString &getFaculaCenterChannels() const {
        return facula_center_channels;
    }

    /**
     * @brief 设置多通道配置字符串
     * @param channels 多通道配置字符串
     */
    void setFaculaCenterChannels(const QString &channels);

    /**
     * @brief 获取解析后的通道坐标列表
     * @return 通道坐标列表
     */
    const QVector<QPoint> &getFaculaCenterPoints() const {
        return facula_center_points;
    }

    /**
     * @brief 设置通道坐标列表
     * @param points 通道坐标列表
     */
    void setFaculaCenterPoints(const QVector<QPoint> &points);

    /**
     * @brief 获取多通道模式下的peak阈值
     * @return peak阈值
     */
    uint32_t getFaculaCenterPeakThreshold() const {
        return facula_center_peak_threshold;
    }

    /**
     * @brief 设置多通道模式下的peak阈值
     * @param threshold peak阈值 (100-2000)
     */
    void setFaculaCenterPeakThreshold(uint32_t threshold) {
        facula_center_peak_threshold = threshold;
    }

    // 兼容性配置访问接口
    /**
     * @brief 获取单点X坐标
     * @return 单点X坐标
     */
    uint8_t getFaculaCenterLocX() const {
        return facula_center_loc_x;
    }

    /**
     * @brief 设置单点X坐标
     * @param x 单点X坐标 (0-255)
     */
    void setFaculaCenterLocX(uint8_t x) {
        facula_center_loc_x = x;
    }

    /**
     * @brief 获取单点Y坐标
     * @return 单点Y坐标
     */
    uint8_t getFaculaCenterLocY() const {
        return facula_center_loc_y;
    }

    /**
     * @brief 设置单点Y坐标
     * @param y 单点Y坐标 (0-255)
     */
    void setFaculaCenterLocY(uint8_t y) {
        facula_center_loc_y = y;
    }

    // 光斑处理类型访问接口
    /**
     * @brief 获取光斑处理类型
     * @return 处理类型：0-基础，1-增强，2-高精度
     */
    uint8_t getFaculaHandleType() const {
        return facula_handle_type;
    }

    /**
     * @brief 设置光斑处理类型
     * @param type 处理类型：0-基础，1-增强，2-高精度
     */
    void setFaculaHandleType(uint8_t type) {
        facula_handle_type = type;
    }

    /**
     * @brief 获取光斑处理类型的描述
     * @return 处理类型描述
     */
    QString getFaculaHandleTypeDescription() const;

    // 便利方法
    /**
     * @brief 解析通道配置字符串为坐标列表
     * @param channels 通道配置字符串
     * @return 解析后的坐标列表
     */
    static QVector<QPoint> parseChannelString(const QString &channels);

    /**
     * @brief 将坐标列表转换为通道配置字符串
     * @param points 坐标列表
     * @return 通道配置字符串
     */
    static QString pointsToChannelString(const QVector<QPoint> &points);

    /**
     * @brief 添加通道点
     * @param point 通道坐标
     */
    void addChannelPoint(const QPoint &point);

    /**
     * @brief 移除通道点
     * @param index 通道索引
     * @return 是否成功移除
     */
    bool removeChannelPoint(int index);

    /**
     * @brief 清空所有通道点
     */
    void clearChannelPoints();

    /**
     * @brief 获取通道数量
     * @return 通道数量
     */
    int getChannelCount() const {
        return facula_center_points.size();
    }

    /**
     * @brief 检查配置是否为默认值
     * @return 是否为默认值
     */
    bool isDefaultConfig() const;

    /**
     * @brief 获取配置摘要信息
     * @return 配置摘要字符串
     */
    QString getConfigSummary() const;

  public:
    // 配置数据成员
    QString         facula_center_channels;        // 多通道配置字符串
    QVector<QPoint> facula_center_points;          // 解析后的通道坐标列表
    uint32_t        facula_center_peak_threshold;  // 多通道模式下的peak阈值

    // 兼容性配置
    uint8_t facula_center_loc_x;  // 单点X坐标
    uint8_t facula_center_loc_y;  // 单点Y坐标

    // 光斑处理类型
    uint8_t facula_handle_type;  // 处理类型：0-基础，1-增强，2-高精度

  private:
    /**
     * @brief 验证通道配置字符串
     * @param channels 通道配置字符串
     * @return 是否有效
     */
    bool validateChannelString(const QString &channels) const;

    /**
     * @brief 验证peak阈值
     * @param threshold peak阈值
     * @return 是否有效
     */
    bool validatePeakThreshold(uint32_t threshold) const;

    /**
     * @brief 验证处理类型
     * @param type 处理类型
     * @return 是否有效
     */
    bool validateHandleType(uint8_t type) const;

    /**
     * @brief 同步通道字符串和坐标列表
     */
    void syncChannelData();

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;
    static const QStringList            s_handleTypeDescriptions;

    // 参数范围定义
    static const uint32_t MIN_PEAK_THRESHOLD = 100;
    static const uint32_t MAX_PEAK_THRESHOLD = 2000;
    static const uint8_t  MAX_COORDINATE     = 255;
    static const uint8_t  MAX_HANDLE_TYPE    = 2;
};

}  // namespace PhotonSensor
