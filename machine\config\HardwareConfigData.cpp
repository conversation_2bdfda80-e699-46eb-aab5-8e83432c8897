#include "HardwareConfigData.h"
#include <QDebug>

namespace Machine {

HardwareConfigData::HardwareConfigData()
    : x_limit_min(-100)
    , x_limit_max(100)
    , y_limit_min(-100)
    , y_limit_max(100)
    , z_limit_min(0)
    , z_limit_max(200)
    , motor_speed_default(50)
    , motor_acceleration_default(10)
    , home_position_x(0)
    , home_position_y(0)
    , home_position_z(0)
    , enable_safety_limits(true)
    , enable_position_feedback(true)
    , communication_timeout(5000)
    , retry_count(3)
{
    setDefaults();
}

QVariantMap HardwareConfigData::toVariantMap() const {
    QVariantMap map;
    
    // 运动限制
    QVariantMap limits;
    limits["x_limit_min"] = x_limit_min;
    limits["x_limit_max"] = x_limit_max;
    limits["y_limit_min"] = y_limit_min;
    limits["y_limit_max"] = y_limit_max;
    limits["z_limit_min"] = z_limit_min;
    limits["z_limit_max"] = z_limit_max;
    map["limits"] = limits;
    
    // 电机参数
    QVariantMap motor;
    motor["motor_speed_default"] = motor_speed_default;
    motor["motor_acceleration_default"] = motor_acceleration_default;
    map["motor"] = motor;
    
    // 原点位置
    QVariantMap home;
    home["home_position_x"] = home_position_x;
    home["home_position_y"] = home_position_y;
    home["home_position_z"] = home_position_z;
    map["home"] = home;
    
    // 安全设置
    QVariantMap safety;
    safety["enable_safety_limits"] = enable_safety_limits;
    safety["enable_position_feedback"] = enable_position_feedback;
    map["safety"] = safety;
    
    // 通信设置
    QVariantMap communication;
    communication["communication_timeout"] = communication_timeout;
    communication["retry_count"] = retry_count;
    map["communication"] = communication;
    
    // 添加元信息
    map["_type"] = getTypeName();
    map["_version"] = getVersion();
    
    return map;
}

bool HardwareConfigData::fromVariantMap(const QVariantMap &data) {
    try {
        // 读取运动限制
        if (data.contains("limits")) {
            QVariantMap limits = data["limits"].toMap();
            x_limit_min = limits.value("x_limit_min", -100).toInt();
            x_limit_max = limits.value("x_limit_max", 100).toInt();
            y_limit_min = limits.value("y_limit_min", -100).toInt();
            y_limit_max = limits.value("y_limit_max", 100).toInt();
            z_limit_min = limits.value("z_limit_min", 0).toInt();
            z_limit_max = limits.value("z_limit_max", 200).toInt();
        }
        
        // 读取电机参数
        if (data.contains("motor")) {
            QVariantMap motor = data["motor"].toMap();
            motor_speed_default = motor.value("motor_speed_default", 50).toInt();
            motor_acceleration_default = motor.value("motor_acceleration_default", 10).toInt();
        }
        
        // 读取原点位置
        if (data.contains("home")) {
            QVariantMap home = data["home"].toMap();
            home_position_x = home.value("home_position_x", 0).toInt();
            home_position_y = home.value("home_position_y", 0).toInt();
            home_position_z = home.value("home_position_z", 0).toInt();
        }
        
        // 读取安全设置
        if (data.contains("safety")) {
            QVariantMap safety = data["safety"].toMap();
            enable_safety_limits = safety.value("enable_safety_limits", true).toBool();
            enable_position_feedback = safety.value("enable_position_feedback", true).toBool();
        }
        
        // 读取通信设置
        if (data.contains("communication")) {
            QVariantMap communication = data["communication"].toMap();
            communication_timeout = communication.value("communication_timeout", 5000).toInt();
            retry_count = communication.value("retry_count", 3).toInt();
        }
        
        logInfo("Hardware config loaded successfully");
        return true;
        
    } catch (const std::exception &e) {
        logError(QString("Failed to load hardware config: %1").arg(e.what()));
        return false;
    }
}

bool HardwareConfigData::validate() const {
    // 验证运动限制
    if (x_limit_min >= x_limit_max) {
        logError("Invalid X limits: min >= max");
        return false;
    }
    
    if (y_limit_min >= y_limit_max) {
        logError("Invalid Y limits: min >= max");
        return false;
    }
    
    if (z_limit_min >= z_limit_max) {
        logError("Invalid Z limits: min >= max");
        return false;
    }
    
    // 验证电机参数
    if (motor_speed_default <= 0 || motor_speed_default > 100) {
        logError("Invalid motor speed: must be 1-100");
        return false;
    }
    
    if (motor_acceleration_default <= 0 || motor_acceleration_default > 100) {
        logError("Invalid motor acceleration: must be 1-100");
        return false;
    }
    
    // 验证原点位置在限制范围内
    if (!isPositionInXYLimit(home_position_x, home_position_y)) {
        logError("Home position is outside XY limits");
        return false;
    }
    
    if (home_position_z < z_limit_min || home_position_z > z_limit_max) {
        logError("Home Z position is outside Z limits");
        return false;
    }
    
    // 验证通信参数
    if (communication_timeout <= 0) {
        logError("Invalid communication timeout: must be > 0");
        return false;
    }
    
    if (retry_count < 0) {
        logError("Invalid retry count: must be >= 0");
        return false;
    }
    
    return true;
}

void HardwareConfigData::setDefaults() {
    x_limit_min = -100;
    x_limit_max = 100;
    y_limit_min = -100;
    y_limit_max = 100;
    z_limit_min = 0;
    z_limit_max = 200;
    motor_speed_default = 50;
    motor_acceleration_default = 10;
    home_position_x = 0;
    home_position_y = 0;
    home_position_z = 0;
    enable_safety_limits = true;
    enable_position_feedback = true;
    communication_timeout = 5000;
    retry_count = 3;
    
    logInfo("Set hardware config to default values");
}

bool HardwareConfigData::isPositionInXYLimit(int x, int y) const {
    return (x >= x_limit_min && x <= x_limit_max && 
            y >= y_limit_min && y <= y_limit_max);
}

bool HardwareConfigData::isPositionInZLimit(int z) const {
    return (z >= z_limit_min && z <= z_limit_max);
}

bool HardwareConfigData::isPositionInLimit(int x, int y, int z) const {
    return isPositionInXYLimit(x, y) && isPositionInZLimit(z);
}

QStringList HardwareConfigData::getFieldNames() const {
    return QStringList() << "x_limit_min" << "x_limit_max" 
                        << "y_limit_min" << "y_limit_max"
                        << "z_limit_min" << "z_limit_max"
                        << "motor_speed_default" << "motor_acceleration_default"
                        << "home_position_x" << "home_position_y" << "home_position_z"
                        << "enable_safety_limits" << "enable_position_feedback"
                        << "communication_timeout" << "retry_count";
}

QString HardwareConfigData::getFieldType(const QString &fieldName) const {
    if (fieldName.contains("enable_")) {
        return "bool";
    } else {
        return "int";
    }
}

QString HardwareConfigData::getFieldDescription(const QString &fieldName) const {
    static QMap<QString, QString> descriptions = {
        {"x_limit_min", "X轴最小限制位置"},
        {"x_limit_max", "X轴最大限制位置"},
        {"y_limit_min", "Y轴最小限制位置"},
        {"y_limit_max", "Y轴最大限制位置"},
        {"z_limit_min", "Z轴最小限制位置"},
        {"z_limit_max", "Z轴最大限制位置"},
        {"motor_speed_default", "电机默认速度 (1-100)"},
        {"motor_acceleration_default", "电机默认加速度 (1-100)"},
        {"home_position_x", "原点X坐标"},
        {"home_position_y", "原点Y坐标"},
        {"home_position_z", "原点Z坐标"},
        {"enable_safety_limits", "启用安全限制"},
        {"enable_position_feedback", "启用位置反馈"},
        {"communication_timeout", "通信超时时间 (毫秒)"},
        {"retry_count", "重试次数"}
    };
    
    return descriptions.value(fieldName, "No description available");
}

bool HardwareConfigData::hasField(const QString &fieldName) const {
    return getFieldNames().contains(fieldName);
}

QVariant HardwareConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    if (fieldName == "x_limit_min") return x_limit_min;
    if (fieldName == "x_limit_max") return x_limit_max;
    if (fieldName == "y_limit_min") return y_limit_min;
    if (fieldName == "y_limit_max") return y_limit_max;
    if (fieldName == "z_limit_min") return z_limit_min;
    if (fieldName == "z_limit_max") return z_limit_max;
    if (fieldName == "motor_speed_default") return motor_speed_default;
    if (fieldName == "motor_acceleration_default") return motor_acceleration_default;
    if (fieldName == "home_position_x") return home_position_x;
    if (fieldName == "home_position_y") return home_position_y;
    if (fieldName == "home_position_z") return home_position_z;
    if (fieldName == "enable_safety_limits") return enable_safety_limits;
    if (fieldName == "enable_position_feedback") return enable_position_feedback;
    if (fieldName == "communication_timeout") return communication_timeout;
    if (fieldName == "retry_count") return retry_count;
    
    return defaultValue;
}

bool HardwareConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    if (fieldName == "x_limit_min") { x_limit_min = value.toInt(); return true; }
    if (fieldName == "x_limit_max") { x_limit_max = value.toInt(); return true; }
    if (fieldName == "y_limit_min") { y_limit_min = value.toInt(); return true; }
    if (fieldName == "y_limit_max") { y_limit_max = value.toInt(); return true; }
    if (fieldName == "z_limit_min") { z_limit_min = value.toInt(); return true; }
    if (fieldName == "z_limit_max") { z_limit_max = value.toInt(); return true; }
    if (fieldName == "motor_speed_default") { motor_speed_default = value.toInt(); return true; }
    if (fieldName == "motor_acceleration_default") { motor_acceleration_default = value.toInt(); return true; }
    if (fieldName == "home_position_x") { home_position_x = value.toInt(); return true; }
    if (fieldName == "home_position_y") { home_position_y = value.toInt(); return true; }
    if (fieldName == "home_position_z") { home_position_z = value.toInt(); return true; }
    if (fieldName == "enable_safety_limits") { enable_safety_limits = value.toBool(); return true; }
    if (fieldName == "enable_position_feedback") { enable_position_feedback = value.toBool(); return true; }
    if (fieldName == "communication_timeout") { communication_timeout = value.toInt(); return true; }
    if (fieldName == "retry_count") { retry_count = value.toInt(); return true; }
    
    return false;
}

bool HardwareConfigData::resetField(const QString &fieldName) {
    // 重置为默认值
    if (fieldName == "x_limit_min") { x_limit_min = -100; return true; }
    if (fieldName == "x_limit_max") { x_limit_max = 100; return true; }
    if (fieldName == "y_limit_min") { y_limit_min = -100; return true; }
    if (fieldName == "y_limit_max") { y_limit_max = 100; return true; }
    if (fieldName == "z_limit_min") { z_limit_min = 0; return true; }
    if (fieldName == "z_limit_max") { z_limit_max = 200; return true; }
    if (fieldName == "motor_speed_default") { motor_speed_default = 50; return true; }
    if (fieldName == "motor_acceleration_default") { motor_acceleration_default = 10; return true; }
    if (fieldName == "home_position_x") { home_position_x = 0; return true; }
    if (fieldName == "home_position_y") { home_position_y = 0; return true; }
    if (fieldName == "home_position_z") { home_position_z = 0; return true; }
    if (fieldName == "enable_safety_limits") { enable_safety_limits = true; return true; }
    if (fieldName == "enable_position_feedback") { enable_position_feedback = true; return true; }
    if (fieldName == "communication_timeout") { communication_timeout = 5000; return true; }
    if (fieldName == "retry_count") { retry_count = 3; return true; }
    
    return false;
}

}  // namespace Machine

// 自动注册硬件配置类型
REGISTER_CONFIG_TYPE(Machine::HardwareConfigData, "Hardware", "1.0.0", "硬件配置，包含运动限制、电机参数、原点位置和安全设置")
