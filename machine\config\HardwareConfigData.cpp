#include "HardwareConfigData.h"
#include <QDebug>

namespace Machine {

HardwareConfigData::HardwareConfigData() : xy_radius_limit(1000), z_radius_limit(1000), x_step_dist(10), y_step_dist(10), z_step_dist(10) {
    setDefaults();
}

QVariantMap HardwareConfigData::toVariantMap() const {
    QVariantMap map;
    map["xy_radius_limit"] = xy_radius_limit;
    map["z_radius_limit"]  = z_radius_limit;
    map["x_step_dist"]     = x_step_dist;
    map["y_step_dist"]     = y_step_dist;
    map["z_step_dist"]     = z_step_dist;
    map["_type"]           = getTypeName();
    map["_version"]        = getVersion();
    return map;
}

bool HardwareConfigData::fromVariantMap(const QVariantMap &data) {
    xy_radius_limit = data.value("xy_radius_limit", 1000).toUInt();
    z_radius_limit  = data.value("z_radius_limit", 1000).toUInt();
    x_step_dist     = data.value("x_step_dist", 10).toUInt();
    y_step_dist     = data.value("y_step_dist", 10).toUInt();
    z_step_dist     = data.value("z_step_dist", 10).toUInt();
    return true;
}

bool HardwareConfigData::validate() const {
    return validateRadiusLimit(xy_radius_limit) && validateRadiusLimit(z_radius_limit) && validateStepDist(x_step_dist) && validateStepDist(y_step_dist) &&
           validateStepDist(z_step_dist);
}

void HardwareConfigData::setDefaults() {
    xy_radius_limit = 1000;
    z_radius_limit  = 1000;
    x_step_dist     = 10;
    y_step_dist     = 10;
    z_step_dist     = 10;
    logInfo("Set hardware config to default values");
}

bool HardwareConfigData::isPositionInXYLimit(int x, int y) const {
    int distance_squared = x * x + y * y;
    int limit_squared    = xy_radius_limit * xy_radius_limit;
    return distance_squared <= limit_squared;
}

bool HardwareConfigData::isZPositionInLimit(int z) const {
    return (z >= 0 && z <= static_cast<int>(z_radius_limit));
}

QString HardwareConfigData::getConfigSummary() const {
    return QString("XY限位: %1, Z限位: %2, 步进距离: X=%3, Y=%4, Z=%5")
        .arg(xy_radius_limit)
        .arg(z_radius_limit)
        .arg(x_step_dist)
        .arg(y_step_dist)
        .arg(z_step_dist);
}

bool HardwareConfigData::isDefaultConfig() const {
    return (xy_radius_limit == 1000 && z_radius_limit == 1000 && x_step_dist == 10 && y_step_dist == 10 && z_step_dist == 10);
}

bool HardwareConfigData::validateRadiusLimit(uint32_t limit) const {
    return (limit >= MIN_RADIUS_LIMIT && limit <= MAX_RADIUS_LIMIT);
}

bool HardwareConfigData::validateStepDist(uint8_t dist) const {
    return (dist >= MIN_STEP_DIST && dist <= MAX_STEP_DIST);
}

QStringList HardwareConfigData::getFieldNames() const {
    return QStringList() << "xy_radius_limit"
                         << "z_radius_limit"
                         << "x_step_dist"
                         << "y_step_dist"
                         << "z_step_dist";
}

QString HardwareConfigData::getFieldType(const QString &fieldName) const {
    if (fieldName.contains("step_dist"))
        return "uint8_t";
    return "uint32_t";
}

QString HardwareConfigData::getFieldDescription(const QString &fieldName) const {
    if (fieldName == "xy_radius_limit")
        return "XY轴限位半径 (100-3000)";
    if (fieldName == "z_radius_limit")
        return "Z轴限位 (100-3000)";
    if (fieldName == "x_step_dist")
        return "X轴单脉冲移动距离 (1-100)";
    if (fieldName == "y_step_dist")
        return "Y轴单脉冲移动距离 (1-100)";
    if (fieldName == "z_step_dist")
        return "Z轴单脉冲移动距离 (1-100)";
    return "Unknown field";
}

bool HardwareConfigData::hasField(const QString &fieldName) const {
    return getFieldNames().contains(fieldName);
}

QVariant HardwareConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    if (fieldName == "xy_radius_limit")
        return xy_radius_limit;
    if (fieldName == "z_radius_limit")
        return z_radius_limit;
    if (fieldName == "x_step_dist")
        return x_step_dist;
    if (fieldName == "y_step_dist")
        return y_step_dist;
    if (fieldName == "z_step_dist")
        return z_step_dist;
    return defaultValue;
}

bool HardwareConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    if (fieldName == "xy_radius_limit") {
        xy_radius_limit = value.toUInt();
        return true;
    }
    if (fieldName == "z_radius_limit") {
        z_radius_limit = value.toUInt();
        return true;
    }
    if (fieldName == "x_step_dist") {
        x_step_dist = value.toUInt();
        return true;
    }
    if (fieldName == "y_step_dist") {
        y_step_dist = value.toUInt();
        return true;
    }
    if (fieldName == "z_step_dist") {
        z_step_dist = value.toUInt();
        return true;
    }
    return false;
}

bool HardwareConfigData::resetField(const QString &fieldName) {
    if (fieldName == "xy_radius_limit") {
        xy_radius_limit = 1000;
        return true;
    }
    if (fieldName == "z_radius_limit") {
        z_radius_limit = 1000;
        return true;
    }
    if (fieldName == "x_step_dist") {
        x_step_dist = 10;
        return true;
    }
    if (fieldName == "y_step_dist") {
        y_step_dist = 10;
        return true;
    }
    if (fieldName == "z_step_dist") {
        z_step_dist = 10;
        return true;
    }
    return false;
}

}  // namespace Machine

// 自动注册硬件配置类型
REGISTER_CONFIG_TYPE(Machine::HardwareConfigData, "Hardware", "1.0.0", "硬件配置，包含运动限制、电机参数、原点位置和安全设置")