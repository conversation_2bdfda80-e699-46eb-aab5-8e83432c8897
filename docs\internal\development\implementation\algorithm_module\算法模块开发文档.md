# 算法模块开发文档

## 模块概述

算法模块是激光工具系统的核心计算引擎，负责图像处理、光斑检测、数值计算等关键算法功能。

### 模块职责

1. **图像处理算法** - 插值、滤波、增强等图像处理功能
2. **光斑检测算法** - 光斑位置检测、特征提取、质量评估
3. **数值计算算法** - 最值查找、排序、反馈控制等数值计算
4. **算法参数管理** - 统一管理所有算法相关的配置参数
5. **性能优化** - 算法性能监控和优化

### 设计原则

1. **模块化设计** - 各算法功能独立封装
2. **配置驱动** - 算法行为通过配置参数控制
3. **性能优先** - 优化算法执行效率
4. **可扩展性** - 支持新算法的动态添加
5. **数据安全** - 确保算法数据的完整性

## 架构设计

### 模块结构

```
algorithm/
├── config/
│   ├── AlgorithmConfigData.h/.cpp     # 算法配置数据类
│   └── AlgorithmModule.h/.cpp         # 算法模块初始化
├── imageProcessing/                   # 图像处理算法
│   ├── filters/                       # 滤波器算法
│   ├── interpolation/                 # 插值算法
│   └── adapters/                      # 算法适配器
├── maxFind/                          # 最值查找算法
├── feedbackControl/                  # 反馈控制算法
├── sort/                            # 排序算法
└── opencv/                          # OpenCV集成
```

### 配置管理架构

```mermaid
graph TB
    subgraph "Algorithm模块"
        ACD[AlgorithmConfigData<br/>算法配置数据]
        AM[AlgorithmModule<br/>模块管理器]
        IP[ImageProcessing<br/>图像处理]
        FD[FaculaDetection<br/>光斑检测]
        NC[NumericalComputation<br/>数值计算]
    end
    
    subgraph "Config服务"
        CS[ConfigService<br/>配置服务]
        DCM[DynamicConfigManager<br/>动态管理器]
    end
    
    ACD --> CS
    AM --> ACD
    IP --> ACD
    FD --> ACD
    NC --> ACD
    
    CS --> DCM
    
    style ACD fill:#e1f5fe
    style AM fill:#f3e5f5
```

## 配置数据设计

### AlgorithmConfigData类

```cpp
namespace Algorithm {
    class AlgorithmConfigData : public Config::BaseConfigData<AlgorithmConfigData> {
    public:
        // 传统算法参数
        QMap<QString, int> parameters;
        
        // 图像处理参数
        uint8_t interpolation_type;
        QString filter_types;
        float filter_strength;
        
        // 光斑检测参数
        QString facula_center_channels;
        QVector<QPoint> facula_center_points;
        uint32_t facula_center_peak_threshold;
        uint8_t facula_handle_type;
        
        // 接口方法
        static QString staticTypeName() { return "Algorithm"; }
        QString getTypeName() const override;
        QVariantMap toVariantMap() const override;
        bool fromVariantMap(const QVariantMap &data) override;
        bool validate() const override;
        void setDefaults() override;
    };
}
```

### 配置参数分类

#### 1. 传统算法参数 (60+个)
- **初始位置参数**: initial_x_dist, initial_y_dist, initial_z_dist
- **查找算法参数**: find_origin_raduis, find_angle_step, find_radius_step
- **阈值参数**: peak_ok_threshold, peak_threshold, peak_max_threshold
- **功能测试参数**: FT_LRmp_adjust_peak, FT_UDmp_adjust_peak等

#### 2. 图像处理参数
- **插值参数**: interpolation_type, interpolation_offset
- **滤波参数**: filter_types, filter_strength
- **卷积参数**: convolution_kernel_size, convolution_preset
- **高斯滤波**: gaussian_sigma, gaussian_kernel_size
- **双边滤波**: bilateral_sigma_color, bilateral_sigma_space

#### 3. 光斑检测参数
- **多通道配置**: facula_center_channels, facula_center_points
- **阈值设置**: facula_center_peak_threshold
- **处理类型**: facula_handle_type (0-基础，1-增强，2-高精度)
- **兼容性参数**: facula_center_loc_x, facula_center_loc_y

## 模块初始化

### 配置注册

```cpp
// algorithm/config/AlgorithmModule.cpp
namespace Algorithm {
    void AlgorithmModule::initialize() {
        // 配置会通过REGISTER_CONFIG_TYPE宏自动注册
        qDebug() << "Algorithm module initialized with config registration";
        
        // 验证配置注册
        auto &registry = Config::ConfigTypeRegistry::getInstance();
        if (registry.isRegistered("Algorithm")) {
            qDebug() << "✅ Algorithm config registered successfully";
        } else {
            qCritical() << "❌ Algorithm config registration failed";
        }
    }
}
```

### 自动注册机制

```cpp
// 在AlgorithmConfigData.h文件末尾
REGISTER_CONFIG_TYPE(Algorithm::AlgorithmConfigData, "Algorithm", "2.1.0", 
                    "算法模块配置参数，包含传统算法、图像处理和光斑检测参数");
```

## 使用指南

### 获取算法配置

```cpp
// 在其他模块中访问算法配置
auto* algorithmConfig = CONFIG_SERVICE().getConfig<Algorithm::AlgorithmConfigData>("Algorithm");

if (algorithmConfig) {
    // 读取传统算法参数
    int threshold = algorithmConfig->getParameter("peak_ok_threshold");
    
    // 读取图像处理参数
    uint8_t interpType = algorithmConfig->getInterpolationType();
    float filterStrength = algorithmConfig->getFilterStrength();
    
    // 读取光斑检测参数
    QString channels = algorithmConfig->getFaculaCenterChannels();
    uint32_t peakThreshold = algorithmConfig->getFaculaCenterPeakThreshold();
}
```

### 修改算法配置

```cpp
// 修改算法参数
algorithmConfig->setParameter("peak_ok_threshold", 600);
algorithmConfig->setInterpolationType(2); // 双三次插值
algorithmConfig->setFilterStrength(2.5f);
algorithmConfig->setFaculaHandleType(1); // 增强模式

// 保存配置
CONFIG_SERVICE().saveConfig("Algorithm");
```

### 配置验证

```cpp
// 验证配置有效性
if (algorithmConfig->validate()) {
    qDebug() << "算法配置验证通过";
} else {
    qWarning() << "算法配置验证失败";
}
```

## 配置文件格式

### JSON配置文件结构

```json
{
    "_type": "Algorithm",
    "_version": "2.1.0",
    "parameters": {
        "initial_x_dist": 0,
        "initial_y_dist": 0,
        "peak_ok_threshold": 550,
        "find_origin_raduis": 140,
        // ... 其他60+个参数
    },
    "interpolation_type": 0,
    "filter_types": "6",
    "filter_strength": 1.0,
    "facula_center_channels": "2,2",
    "facula_center_peak_threshold": 800,
    "facula_handle_type": 1
}
```

### 配置文件位置

- **默认路径**: `config/modules/algorithm/algorithm_config.json`
- **备份路径**: `config/backup/algorithm/`
- **模板路径**: `config/templates/algorithm_template.json`

## 性能考虑

1. **参数缓存** - 频繁访问的参数进行缓存
2. **延迟加载** - 配置对象按需创建
3. **批量更新** - 支持批量参数修改
4. **内存优化** - 合理管理配置对象生命周期

## 扩展指南

### 添加新算法参数

1. **在AlgorithmConfigData中添加成员变量**
2. **更新toVariantMap和fromVariantMap方法**
3. **添加参数验证逻辑**
4. **更新默认值设置**
5. **添加访问接口方法**

### 集成新算法模块

1. **创建算法类继承基础接口**
2. **在算法类中访问配置参数**
3. **实现算法逻辑**
4. **添加性能监控**
5. **编写单元测试**

## 测试方案

### 配置测试

```cpp
#ifdef ENABLE_CONFIG_TESTS
void testAlgorithmConfig() {
    auto* config = CONFIG_SERVICE().getConfig<Algorithm::AlgorithmConfigData>("Algorithm");
    
    // 测试参数设置
    config->setParameter("peak_ok_threshold", 600);
    QCOMPARE(config->getParameter("peak_ok_threshold"), 600);
    
    // 测试配置验证
    QVERIFY(config->validate());
    
    // 测试序列化
    QVariantMap data = config->toVariantMap();
    QVERIFY(!data.isEmpty());
}
#endif
```

### 性能测试

```cpp
void performanceTest() {
    QElapsedTimer timer;
    timer.start();
    
    // 测试配置访问性能
    for (int i = 0; i < 10000; ++i) {
        auto* config = CONFIG_SERVICE().getConfig<Algorithm::AlgorithmConfigData>("Algorithm");
        int value = config->getParameter("peak_ok_threshold");
    }
    
    qint64 elapsed = timer.elapsed();
    qDebug() << "配置访问性能测试:" << elapsed << "ms";
}
```

## 维护指南

1. **定期备份配置文件**
2. **监控配置变更日志**
3. **验证配置文件完整性**
4. **更新配置文档**
5. **性能监控和优化**

算法模块通过模块化的配置管理，为系统提供了强大而灵活的算法计算能力。
