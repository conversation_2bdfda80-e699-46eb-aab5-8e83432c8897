#include "faculaCircle.h"
#include "../../components/configModule/ConfigService.h"
#include "../../components/configModule/DynamicConfigManager.h"
#include "config/FaculaConfigData.h"
#include <QApplication>
#include <math.h>

#define Z_PEAK_HEAD_DELTA     5
#define Z_PEAK_REVERSE_DELTA  20
#define Z_PEAK_DELTA_JUDGE    50
#define RANDOM_PEAK_THRESHOLD 100

#define PI                    3.14
#define PI_ANGLE              180

const QMap<CFaculaCircle::EAdjustStep, QString> CFaculaCircle::mm_adjust_step_describe = {
    {CFaculaCircle::EAdjustStep::eDEFAULT_MOVE_STEP, "fixed move"},
    {CFaculaCircle::EAdjustStep::eRANDOM_FIND_STEP, "random move"},
    {CFaculaCircle::EAdjustStep::eCENTRAL_STEP, "center move"},
    {CFaculaCircle::EAdjustStep::eSYMMETRY_STEP, "symmetry move"},
    {CFaculaCircle::EAdjustStep::ePEAK_STEP, "peak move"},
};

CFaculaCircle::CFaculaCircle(const IFaculaAdjust::StMapInfo &st_map_info)
    : m_adjust_len_step(EAdjustStep::eDEFAULT_MOVE_STEP), m_z_function_select(EZFindFunction::eMAX_PEAK) {
    m_find_radius = m_xml_param["find_origin_raduis"];

    targetFaculaMoveInfo(st_map_info, &mst_target_info);

    mst_target_info.sensor_direction = st_map_info.sensor_direction;
    mst_target_info.symm_type        = st_map_info.symm_type;

    // 调节超时
    m_adjust_timeout_num = 20000 / 5;  // 超时配置时间 20000ms / 定时器周期 5ms

    // 多通道配置将在运行时从ConfigManager获取

    switch (st_map_info.facula_form) {
    case IPhotonSensor::eFOCUS:
        m_z_function_select             = EZFindFunction::eMAX_PEAK;
        mu_facula_detect_items.dds_info = 0;

        mu_facula_detect_items.all_info.facula_target    = true;
        mu_facula_detect_items.all_info.facula_symmetry  = true;
        mu_facula_detect_items.all_info.center_peak_down = true;
        mu_facula_detect_items.all_info.CR_peak_delta    = true;
        break;
    case IPhotonSensor::eDISPERSE:
        m_z_function_select             = EZFindFunction::eDISPERSE;
        mu_facula_detect_items.dds_info = 0;

        mu_facula_detect_items.all_info.facula_target    = true;
        mu_facula_detect_items.all_info.facula_symmetry  = true;
        mu_facula_detect_items.all_info.facula_round_sym = true;
        mu_facula_detect_items.all_info.center_peak_down = true;
        mu_facula_detect_items.all_info.round_peak_down  = true;
        mu_facula_detect_items.all_info.CR_peak_delta    = true;
        break;
    default:
        break;
    }
}

CFaculaCircle::~CFaculaCircle() {
}

/**
 * @brief CFaculaCircle::variblesInit 初始化
 */
void CFaculaCircle::variblesInit() {
    mu_facula_adjust.adjust_step = 0x00;
    m_adjust_len_step            = EAdjustStep::eDEFAULT_MOVE_STEP;
    m_adjust_timeout_cnt         = 0;

    m_last_max_peak     = 0;
    m_z_peak_stable_cnt = 0;
}

/**
 * @brief: 目标光斑区域解析-二维坐标轴, 横轴X，纵轴Y，左上角（0，0）
 * @param:
 */
bool CFaculaCircle::targetFaculaMoveInfo(const IFaculaAdjust::StMapInfo &map_info, StTargetInfo *target_info_) {
    uint8_t xlens = map_info.xlens;
    uint8_t ylens = map_info.ylens;
    if (xlens < 3 || ylens < 2) {
        return false;
    }
    target_info_->map_xlen = xlens;
    target_info_->map_ylen = ylens;

    //* 边缘目标点: 先移动到中心->边缘点
    if ((map_info.target_tf.ax == (xlens - 1)) || (map_info.target_tf.ax == 0) || (map_info.target_tf.ay == (ylens - 1) || (map_info.target_tf.ay == 0))) {
        target_info_->is_center_to_target = true;

        uint8_t ax = ((xlens + 1) >> 1) - 1;  //中心点 TF
        uint8_t ay = ((ylens + 1) >> 1) - 1;  //

        target_info_->center_round.center.ax = ax;  //中心点 TF
        target_info_->center_round.center.ay = ay;  //
        target_info_->center_round.left.ax   = ax - 1;
        target_info_->center_round.left.ay   = ay;
        target_info_->center_round.right.ax  = ax + 1;
        target_info_->center_round.right.ay  = ay;
        target_info_->center_round.up.ax     = ax;
        target_info_->center_round.up.ay     = ay - 1;
        target_info_->center_round.down.ax   = ax;
        target_info_->center_round.down.ay   = ay + 1;
    } else {  //直接移动到目标点
        target_info_->is_center_to_target = false;
    }
    target_info_->target_round.center   = map_info.target_tf;
    target_info_->target_round.left.ax  = target_info_->target_round.center.ax - 1;
    target_info_->target_round.left.ay  = target_info_->target_round.center.ay;
    target_info_->target_round.right.ax = target_info_->target_round.center.ax + 1;
    target_info_->target_round.right.ay = target_info_->target_round.center.ay;
    target_info_->target_round.down.ax  = target_info_->target_round.center.ax;
    target_info_->target_round.down.ay  = target_info_->target_round.center.ay + 1;
    target_info_->target_round.up.ax    = target_info_->target_round.center.ax;
    target_info_->target_round.up.ay    = target_info_->target_round.center.ay - 1;
    //    qDebug() << "-i facula circle/" << "center x:" << target_info_->center_tf.ax << "center y:" << target_info_->center_tf.ay\
    //             << "target x:" << target_info_->target_tf.ax << "target y:" << target_info_->target_tf.ay;

    return true;
}

// void CFaculaCircle::targetMapUpdate(const StMapTargetInfo &target_map) {
//    //    mst_target_info = target_map;
//}

/**
 * @brief CFaculaCircle::defaultFaculaMove 初始移动距离 um
 * @param move_3d_ 移动步进：移动距离/单步进移动距离
 * @return
 */
IFaculaAdjust::EFaculaStatus CFaculaCircle::defaultFaculaMove(C3dHandMachine::St3D<int16_t> *move_3d_) {
    move_3d_->x = m_xml_param["initial_x_dist"] / 7;
    move_3d_->y = m_xml_param["initial_y_dist"] / 7;
    move_3d_->z = m_xml_param["initial_z_dist"] / 8;

    // COMP_LOG_INFO(m_logger,
    //              MyLogger::LogType::PROCESS_DATA,
    //               mm_adjust_step_describe[eDEFAULT_MOVE_STEP] + "x distance: " + QString::number(m_xml_param["initial_x_dist"]) + "y distance" +
    //                   QString::number(m_xml_param["initial_y_dist"]) + "z distance" +
    //                   QString::number(m_xml_param["initial_z_dist"]);

    return EFaculaStatus::eOK;
}

/**
 * @brief CFaculaCircle::randomFaculaFind 随机寻找光斑
 * @param random_find_
 * @param move_delta_step
 * @param move_3d_
 * @return
 */
IFaculaAdjust::EFaculaStatus CFaculaCircle::randomFaculaFind(StRandomFaculaInfo *                 random_find_,
                                                             const C3dHandMachine::St3D<int16_t> &move_delta_step,
                                                             C3dHandMachine::St3D<int16_t> *      move_3d_) {
    int16_t x_tmp, y_tmp;
    double  radian;

    /*1. 认为无光斑，涡轮移动*/
    if (random_find_->theta > 360) {  //报错
        if (random_find_->find_facula_cnt > (m_xml_param["find_times"] - 2)) {
            return EFaculaStatus::eFATAL;
        }
        random_find_->theta = 0;
        random_find_->find_radius += m_xml_param["find_radius_step"];
        random_find_->find_facula_cnt++;
    }
    radian = random_find_->theta * PI / PI_ANGLE;
    x_tmp  = qRound(random_find_->find_radius * cos(radian) / 7);  //
    y_tmp  = qRound(random_find_->find_radius * sin(radian) / 7);  //

    int8_t actual_move_dir = 1;
    if (mst_target_info.sensor_direction)
        actual_move_dir = -1;

    move_3d_->x = x_tmp - actual_move_dir * move_delta_step.x;  //
    move_3d_->y = y_tmp - actual_move_dir * move_delta_step.y;
    random_find_->theta += m_xml_param["find_angle_step"];  //


    LOG_INFO(MyLogger::LogType::PROCESS_DATA,
             QString("%1/ x_radian: %2, x_step: %3, y_radian: %4, y_step: %5")
                 .arg(mm_adjust_step_describe[EAdjustStep::eRANDOM_FIND_STEP])
                 .arg(x_tmp)
                 .arg(move_delta_step.x)
                 .arg(y_tmp)
                 .arg(move_delta_step.y));

    return EFaculaStatus::eOK;
}

/**
 * @brief CFaculaCircle::centralSymmetryMove
 * @return
 */
// EFaculaStatus CFaculaCircle::centralMove(const StSingleMp &central_mp, const StSingleMp &target_mp, C3dHandMachine::St3D<int16_t> *move_3d_) {
////    static uint8_t max_peak_overlap_cnt = 0;
//    C3dHandMachine::St3D<int16_t> central_mp_delta;

//    central_mp_delta.x = central_mp.ax - target_mp.ax;
//    central_mp_delta.y = central_mp.ay - target_mp.ay;
//    central_mp_delta.z = central_mp.peak - target_mp.peak;

//    if((abs(central_mp_delta.x) > 0) || (abs(central_mp_delta.y) > 0)) {
////        if(mp_data_s == max_tmp) {
////            if(++max_peak_overlap_cnt > 3) return EFaculaStatus::eERROR; //相同最大值，不移动
////            else return EFaculaStatus::eWAIT;

////        }
//        move_3d_->y = -central_mp_delta.x;
//        move_3d_->x = -central_mp_delta.y;

//        return EFaculaStatus::eWAIT;
//    }
//    else {
//        return EFaculaStatus::eOK;
//    }
//}
/**
 * @brief CFaculaCircle::centralMove
 * @param central_mp_delta
 * @param move_3d_
 * @return
 */
IFaculaAdjust::EFaculaStatus CFaculaCircle::centralMove(const C3dHandMachine::St3D<int16_t> &central_mp_delta, C3dHandMachine::St3D<int16_t> *move_3d_) {
    //*反向 4300
    //    if(!mst_target_info.sensor_direction) {
    move_3d_->y = central_mp_delta.x;
    move_3d_->x = central_mp_delta.y;
    //    }
    //    else { //4302
    //        move_3d_->y = -central_mp_delta.x;
    //        move_3d_->x = -central_mp_delta.y;
    //    }
    return EFaculaStatus::eWAIT;
}

IFaculaAdjust::EFaculaStatus
CFaculaCircle::symmetryMove(const C3dHandMachine::St3D<int16_t> &symmetry_mp_delta, const int16_t &xy_peak_delta_tor, C3dHandMachine::St3D<int16_t> *move_3d_) {
    int16_t x_peak_move = 0, y_peak_move = 0;

    if (symmetry_mp_delta.x != 0) {
        x_peak_move = (symmetry_mp_delta.x / xy_peak_delta_tor);  // xy_peak_delta_tor; //右边大，正
        if (x_peak_move > 3) {
            x_peak_move = 3;
        } else if (x_peak_move < -3) {
            x_peak_move = -3;
        } else if (x_peak_move == 2) {
            x_peak_move = 1;
        } else if (x_peak_move == -2) {
            x_peak_move = -1;
        }
    }
    if (symmetry_mp_delta.y != 0) {
        y_peak_move = (symmetry_mp_delta.y / xy_peak_delta_tor);  // symmetry_mp_delta.y/XY_PEAK_DELTA_LIMIT; //上大， 正
        if (y_peak_move > 3) {
            y_peak_move = 3;
        } else if (y_peak_move < -3) {
            y_peak_move = -3;
        } else if (y_peak_move == 2) {  // D4可能更高移动过快，出现震荡现象
            y_peak_move = 1;
        } else if (y_peak_move == -2) {  //
            y_peak_move = -1;
        }
    }

    //*反向 4300
    move_3d_->y = x_peak_move;
    move_3d_->x = y_peak_move;

    return EFaculaStatus::eWAIT;
}

/**
 * @brief 最强peak寻找 max_tmp < map_data->mp_adjust.max_peak_limit
 * @return
 */
IFaculaAdjust::EFaculaStatus CFaculaCircle::peakMove(int8_t &                             z_direction,
                                                     const StZFaculaInfo &                z_facula_info,
                                                     const EZFindFunction &               find_function,
                                                     C3dHandMachine::St3D<int16_t> *      move_3d_,
                                                     const C3dHandMachine::St3D<int16_t> &move_delta_step) {
    StZDisperseFacula disperse_info;

    disperse_info.max_around_peak = z_facula_info.max_peak - z_facula_info.cross_aver_peak;
    disperse_info.edge_corner     = z_facula_info.edge_corner;
    disperse_info.max_peak        = z_facula_info.max_peak;

    switch (find_function) {
    case EZFindFunction::eMAX_PEAK:
        //* peak 强度符合，直接退出
        if ((z_facula_info.max_peak > (uint32_t)m_xml_param["peak_ok_threshold"]) && (disperse_info.max_peak > (z_facula_info.cross_aver_peak * 1.02))) {
            m_last_max_peak = z_facula_info.max_peak;
            return EFaculaStatus::eOK;
        }
        return zMaxPeakSingleStep(z_direction, z_facula_info, move_delta_step, move_3d_);
        break;

    case EZFindFunction::eDISPERSE:
        return zFindDisperseFacula(disperse_info, move_3d_);
        break;

    default:
        break;
    }
}

bool CFaculaCircle::faculaMp(StMapData *map_data_) {
}

// EFaculaStatus CFaculaCircle::targetMove(bool target_move, ) {
//    C3dHandMachine::St3D<int16_t> central_mp_delta;

//    if((abs(central_mp_delta.x) > 0) || (abs(central_mp_delta.y) > 0)) {
//        centralMove(central_mp_delta, move_dis_);//中心
//    }
//    else if((abs(symmetry_mp_delta.x) > xy_peak_delta_tor) || (abs(symmetry_mp_delta.y) > xy_peak_delta_tor)) {
//        symmetryMove(symmetry_mp_delta, xy_peak_delta_tor, move_dis_);//对称
//    }
//    else { //peak
////                qDebug() << "-i facula circle/ symmetry peak:" << symmetry_mp_delta.x << xy_peak_delta_tor << symmetry_mp_delta.y;

//        StZFaculaInfo z_facula_info;
//        z_facula_info.max_peak = max_tmp;
//        z_facula_info.cross_aver_peak = peak_aver;

//        if(!mst_target_info.is_center_to_target && (m_z_function_select == EZFindFunction::eDISPERSE)) {
//            z_facula_info.edge_corner.clear();
//            if(map_data_->map_matrix.size() >= 3 && map_data_->map_matrix.at(0).size() >= 3) {
//                z_facula_info.edge_corner.append(map_data_->map_matrix.at(mst_target_round_->up.ay).at(mst_target_round_->left.ax)); //顺时针
//                z_facula_info.edge_corner.append(map_data_->map_matrix.at(mst_target_round_->up.ay).at(mst_target_round_->right.ax)); //顺时针
//                z_facula_info.edge_corner.append(map_data_->map_matrix.at(mst_target_round_->down.ay).at(mst_target_round_->right.ax)); //顺时针
//                z_facula_info.edge_corner.append(map_data_->map_matrix.at(mst_target_round_->down.ay).at(mst_target_round_->left.ax)); //顺时针
//            }
//        }

//        if(z_finded_cnt == 0) {
//            if(peakMove(s_z_direction, z_facula_info, m_z_function_select, move_dis_, move_delta_step) == EFaculaStatus::eOK) {
////                        mu_facula_adjust.all_step.peak_adjust = true;
//                z_finded_cnt++;
//            }
//        }
//        else {
//            ++z_finded_cnt;

//            if(z_finded_cnt == 4) {
//                mu_facula_adjust.all_step.retest = true; //完毕
//            }
//            else if(z_finded_cnt == 2) {
//                if(!mst_target_info.is_center_to_target) mu_facula_adjust.all_step.retest = true; //完毕
//                else {
//                    target_move = true;
//                    m_left_center_peak = left_peak;
//                }
//            }
//            else {;}
//        }
//    }
//};

/**
 * @brief CFaculaCircle::getSymmetryPeakThreshold
 * @param center_peak
 * @param mode
 * @return
 */
C3dHandMachine::St3D<int16_t> CFaculaCircle::getSymmetryPeakThreshold(const uint32_t &center_peak, const EFaculaJudgeMode &mode) {
    C3dHandMachine::St3D<int16_t> symmetry_peak_threshold;

    //* default
    // symmetry_peak_threshold.x = m_xml_param["Uaround_peak_threshold"];
    // symmetry_peak_threshold.y = m_xml_param["Uaround_peak_threshold"];

    if (mst_target_info.symm_type == eDESIGNATE_MP) {
        switch (mode) {
        case eFACULA_ADJUST:
            // symmetry_peak_threshold.x = m_xml_param["ARR_peak_delta"];
            break;

        case eFACULA_ADJUST_TEST:
            // symmetry_peak_threshold.x = center_peak * m_xml_param["FT_adjust_peak_radio_threshold"] / 100;
            break;

        case eFACULA_SOLID_TEST:
            // symmetry_peak_threshold.x = center_peak * m_xml_param["FT_solid_peak_radio_threshold"] / 100;
            break;

        case eFACUAL_DEFLATE_TEST:
            // symmetry_peak_threshold.x = center_peak * m_xml_param["FT_deflate_peak_radio_threshold"] / 100;
            break;

        default:
            // symmetry_peak_threshold.x = center_peak * m_xml_param["FT_adjust_peak_radio_threshold"] / 100;
            break;
        }
    }

    return symmetry_peak_threshold;
};

/**
 * @brief CFaculaCircle::updateRoundMpPeak 目标光斑十字通道数据
 * @param round_mp_
 * @param map_matrix
 * @param has_adjusted
 * @param mode
 * @param is_move_center
 * @return
 */
void CFaculaCircle::updateRoundMpPeak(StRoundJudgeMP *round_mp_, const QVector<QVector<uint32_t>> &map_matrix, const bool &has_adjusted) {
    bool left_right_flag = false, up_down_flag = 0;

    if (round_mp_->left.ax < 0) {
        if (has_adjusted)
            round_mp_->left.peak = mst_target_info.center_round.left.peak;  // 调节->取中间位置时的左值
        else
            round_mp_->left.peak = map_matrix.at(round_mp_->right.ay).at(round_mp_->right.ax);  //

        left_right_flag = true;
    } else
        round_mp_->left.peak = map_matrix.at(round_mp_->left.ay).at(round_mp_->left.ax);

    if (round_mp_->right.ax >= mst_target_info.map_xlen) {
        if (has_adjusted)
            round_mp_->right.peak = mst_target_info.center_round.right.peak;
        else
            round_mp_->right.peak = round_mp_->left.peak;
        left_right_flag = true;
    } else
        round_mp_->right.peak = map_matrix.at(round_mp_->right.ay).at(round_mp_->right.ax);

    if (round_mp_->up.ay < 0) {
        if (has_adjusted)
            round_mp_->up.peak = mst_target_info.center_round.up.peak;
        else
            round_mp_->up.peak = map_matrix.at(round_mp_->down.ay).at(round_mp_->down.ax);
        up_down_flag = true;
    } else
        round_mp_->up.peak = map_matrix.at(round_mp_->up.ay).at(round_mp_->up.ax);

    if (round_mp_->down.ay >= mst_target_info.map_ylen) {
        if (has_adjusted)
            round_mp_->down.peak = mst_target_info.center_round.down.peak;
        else
            round_mp_->down.peak = round_mp_->up.peak;
        up_down_flag = true;
    } else
        round_mp_->down.peak = map_matrix.at(round_mp_->down.ay).at(round_mp_->down.ax);
}

/**
 * @brief getSymmetryDelta 获取对称调节peak 差值
 * @return
 */
CFaculaCircle::StSymmetryInfo CFaculaCircle::getSymmetryDelta(StRoundJudgeMP *                  round_mp_,
                                                              const QVector<QVector<uint32_t>> &map_matrix,
                                                              const StSymmetryInfo &            symmetry_param,
                                                              const bool &                      is_move_center) {
    StSymmetryInfo symmetry_mp_delta;

    if (is_move_center) {
        symmetry_mp_delta.peak_value.x = round_mp_->right.peak - round_mp_->left.peak;  //左 - 右
        symmetry_mp_delta.peak_value.y = round_mp_->down.peak - round_mp_->up.peak;     //上
    } else {
        uint16_t target_mp_peak = 0;  //目标通道peak值（中心peak * ratio）

        switch ((m_xml_param["Amp_select"] >> 6)) {  // bit6-7 表示固定通道值的计算方式
        case eMP_CENTER_RATIO:
            target_mp_peak = map_matrix.at(round_mp_->center.ay).at(round_mp_->center.ax) * symmetry_param.peak_value.x / 100;
            symmetry_mp_delta.peak_threshold_value.x =
                map_matrix.at(round_mp_->center.ay).at(round_mp_->center.ax) * symmetry_param.peak_threshold_value.x / 100;
            symmetry_mp_delta.peak_threshold_value.y =
                map_matrix.at(round_mp_->center.ay).at(round_mp_->center.ax) * symmetry_param.peak_threshold_value.y / 100;
            break;

        case eSYMMETRY_MP_RATIO:
            //                target_mp_peak = round_mp_->center.peak * m_xml_param["Apeak_offset_radio"];
            break;

        case eMP_DESIGNATE_VALUE:
            target_mp_peak = symmetry_param.peak_value.x;  //左 - 右

            symmetry_mp_delta.peak_threshold_value.x = symmetry_param.peak_threshold_value.x;
            symmetry_mp_delta.peak_threshold_value.y = symmetry_param.peak_threshold_value.y;
            break;

        case eMP_DESIGNATE_DELTA_VALUE:  //固定差值
                                         //            target_mp_peak = symmetry_param.peak_value.x;  //左 - 右
            symmetry_mp_delta.peak_threshold_value.x = symmetry_param.peak_threshold_value.x;
            symmetry_mp_delta.peak_threshold_value.y = symmetry_param.peak_threshold_value.y;
            break;

        default:
            symmetry_mp_delta.peak_threshold_value.x = symmetry_param.peak_threshold_value.x;
            symmetry_mp_delta.peak_threshold_value.y = symmetry_param.peak_threshold_value.y;
            break;
        }

        uint8_t mp_select = m_xml_param["Amp_select"] & 0x3F;
        if ((mp_select & (eMP_LEFT | eMP_RIGHT)) == (eMP_LEFT | eMP_RIGHT)) {
            symmetry_mp_delta.peak_value.x = round_mp_->right.peak - (round_mp_->left.peak + symmetry_param.peak_value.x);  //左 - 右
        } else if ((mp_select & eMP_LEFT) == eMP_LEFT) {
            symmetry_mp_delta.peak_value.x = round_mp_->left.peak - target_mp_peak;  //左 - 右
        } else if ((mp_select & eMP_RIGHT) == eMP_RIGHT) {
            symmetry_mp_delta.peak_value.x = round_mp_->right.peak - target_mp_peak;  //左 - 右
        } else {
            symmetry_mp_delta.peak_value.x = round_mp_->right.peak - round_mp_->left.peak;  //左 - 右

            symmetry_mp_delta.peak_threshold_value.x = symmetry_param.peak_threshold_value.x;
        }

        if ((mp_select & (eMP_UP | eMP_DOWN)) == (eMP_UP | eMP_DOWN)) {
            symmetry_mp_delta.peak_value.y = round_mp_->down.peak - (round_mp_->up.peak + symmetry_param.peak_value.y);
        } else if ((mp_select & eMP_UP) == eMP_UP) {
            symmetry_mp_delta.peak_value.y = round_mp_->up.peak - target_mp_peak;  //上
        } else if ((mp_select & eMP_DOWN) == eMP_DOWN) {
            symmetry_mp_delta.peak_value.y = round_mp_->down.peak - target_mp_peak;
        } else {
            symmetry_mp_delta.peak_value.y = round_mp_->down.peak - round_mp_->up.peak;  //上

            symmetry_mp_delta.peak_threshold_value.y = symmetry_param.peak_threshold_value.y;
        }
    }

    LOG_INFO(MyLogger::LogType::PROCESS_DATA,
             QString("right_peak: %1, left_peak: %2, ALR_peak_offset: %3, AUD_peak_offset: %4, ALR_peak_threshold: %5, AUD_peak_threshold: %6")
                 .arg(round_mp_->right.peak)
                 .arg(round_mp_->left.peak)
                 .arg(symmetry_param.peak_value.x)
                 .arg(symmetry_param.peak_value.y)
                 .arg(symmetry_param.peak_threshold_value.x)
                 .arg(symmetry_param.peak_threshold_value.y));

    return symmetry_mp_delta;
};


/**
 * @brief CFaculaCircle::faculaAdjust
 * @param map_data_
 * @param move_delta_step
 * @param move_dis_
 * @return
 */
uint8_t CFaculaCircle::faculaAdjust(StMapData *map_data_, const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_) {
    //* variables init
    static StRandomFaculaInfo mst_random_find;
    static uint8_t            z_finded_cnt = 0, symmetry_cnt = 0;
    static int8_t             s_z_direction = m_xml_param["default_z_direct"];
    static bool               target_move   = false;  // center_moved = false;

    uint32_t max_tmp = map_data_->max_peak, mp_data_s = map_data_->peak_second;
    uint8_t  data_tmp          = max_tmp / 10;
    int16_t  xy_peak_delta_tor = (data_tmp > m_xml_param["ARR_peak_delta"]) ? data_tmp : m_xml_param["ARR_peak_delta"];
    xy_peak_delta_tor          = xy_peak_delta_tor < 40 ? 40 : xy_peak_delta_tor;

    StSymmetryInfo symmetry_mp_delta;

    move_dis_->x = 0;
    move_dis_->y = 0;
    move_dis_->z = 0;

    //*
    C3dHandMachine::St3D<int16_t> central_mp_delta;
    StRoundJudgeMP *              round_mp_ = nullptr;
    uint16_t                      peak_aver = 0;
    StZFaculaInfo                 z_facula_info;

    // 超时检测
    if (++m_adjust_timeout_cnt > m_adjust_timeout_num) {
        mu_facula_adjust.all_step.facula_adjust_timeout = true;

        return mu_facula_adjust.adjust_step;
    }


    if (mst_target_info.is_center_to_target && (!target_move)) {  //
        round_mp_ = &mst_target_info.center_round;
    } else {  //目标区域
        round_mp_ = &mst_target_info.target_round;
    }

    switch (m_adjust_len_step) {
    case EAdjustStep::eDEFAULT_MOVE_STEP:
        defaultFaculaMove(move_dis_);

        mst_random_find.theta           = 0;
        mst_random_find.find_radius     = m_xml_param["find_origin_raduis"];
        mst_random_find.find_facula_cnt = 0;

        z_finded_cnt  = 0;
        symmetry_cnt  = 0;
        s_z_direction = m_xml_param["default_z_direct"];
        target_move   = false;

        m_adjust_len_step = EAdjustStep::eRANDOM_FIND_STEP;
        break;

    case EAdjustStep::eRANDOM_FIND_STEP:
        if (max_tmp < RANDOM_PEAK_THRESHOLD) {
            if (randomFaculaFind(&mst_random_find, move_delta_step, move_dis_) == EFaculaStatus::eFATAL) {
                mu_facula_adjust.all_step.facula_find = true;
            }
        } else
            m_adjust_len_step = EAdjustStep::eCENTRAL_STEP;
        break;

    case EAdjustStep::eCENTRAL_STEP:
        //        if(mst_target_info.is_center_to_target && (!target_move)) { //
        //            round_mp_ = &mst_target_info.center_round;
        //        }
        //        else { //目标区域
        //            round_mp_ = &mst_target_info.target_round;
        //        }
        //* mp 有效性判断
        central_mp_delta.x = map_data_->max_mp.ax - round_mp_->center.ax;
        central_mp_delta.y = map_data_->max_mp.ay - round_mp_->center.ay;
        central_mp_delta.z = map_data_->max_mp.peak - round_mp_->center.peak;

        LOG_INFO(MyLogger::LogType::PROCESS_DATA,
                 QString("%1peakDelta_x: %2 peakDelta_y: %3 peakDelta_z: %4")
                     .arg(mm_adjust_step_describe[EAdjustStep::eCENTRAL_STEP])
                     .arg(central_mp_delta.x)
                     .arg(central_mp_delta.y)
                     .arg(central_mp_delta.z));
        //* 1. 中心move
        if ((abs(central_mp_delta.x) > 0) || (abs(central_mp_delta.y) > 0)) {
            centralMove(central_mp_delta, move_dis_);
        } else
            m_adjust_len_step = EAdjustStep::eSYMMETRY_STEP;
        break;

    case EAdjustStep::eSYMMETRY_STEP:
        //* mp 有效性判断
        updateRoundMpPeak(round_mp_, map_data_->map_matrix, true);
        symmetry_mp_delta =
            getSymmetryDelta(round_mp_, map_data_->map_matrix, mm_symmetry_mp_param[eFACULA_ADJUST], mst_target_info.is_center_to_target && (!target_move));

        LOG_INFO(MyLogger::LogType::PROCESS_DATA,
                 QString("%1, symmetryDelta x: %2 y: %3 peakThreshold: %4")
                     .arg(mm_adjust_step_describe[m_adjust_len_step])
                     .arg(symmetry_mp_delta.peak_value.x)
                     .arg(symmetry_mp_delta.peak_value.y)
                     .arg(xy_peak_delta_tor));


        //* 1. 中心move
        if ((abs(symmetry_mp_delta.peak_value.x) > xy_peak_delta_tor) || (abs(symmetry_mp_delta.peak_value.y) > xy_peak_delta_tor)) {
            symmetryMove(symmetry_mp_delta.peak_value, xy_peak_delta_tor, move_dis_);

            symmetry_cnt = 0;
        } else {
            if (++symmetry_cnt > 1) {
                symmetry_cnt = 0;

                if (target_move) {
                    mu_facula_adjust.all_step.retest = true;  //完毕
                } else if (z_finded_cnt == 1) {               // z轴调节完毕
                    if (mst_target_info.is_center_to_target) {
                        m_adjust_len_step = EAdjustStep::eCENTRAL_STEP;
                        target_move       = true;
                    } else {
                        mu_facula_adjust.all_step.retest = true;  //完毕
                    }
                } else
                    m_adjust_len_step = EAdjustStep::ePEAK_STEP;
            }
        }
        break;

    case EAdjustStep::ePEAK_STEP:
        //* 2. 对称
        peak_aver = (round_mp_->right.peak + round_mp_->left.peak + round_mp_->down.peak + round_mp_->up.peak) >> 2;

        z_facula_info.max_peak        = max_tmp;
        z_facula_info.cross_aver_peak = peak_aver;

        if (m_z_function_select == EZFindFunction::eDISPERSE) {  //! mst_target_info.is_center_to_target && (
            z_facula_info.edge_corner.clear();

            if ((round_mp_->up.ay > 0) && (round_mp_->up.ay < mst_target_info.map_ylen)) {
                if (round_mp_->left.ax > 0)
                    z_facula_info.edge_corner.append(map_data_->map_matrix.at(round_mp_->up.ay).at(round_mp_->left.ax));
                if (round_mp_->right.ax < mst_target_info.map_xlen)
                    z_facula_info.edge_corner.append(map_data_->map_matrix.at(round_mp_->up.ay).at(round_mp_->right.ax));
            }
            if ((round_mp_->down.ay > 0) && (round_mp_->down.ay < mst_target_info.map_ylen)) {
                if (round_mp_->right.ax < mst_target_info.map_xlen)
                    z_facula_info.edge_corner.append(map_data_->map_matrix.at(round_mp_->down.ay).at(round_mp_->right.ax));
                if (round_mp_->left.ax > 0)
                    z_facula_info.edge_corner.append(map_data_->map_matrix.at(round_mp_->down.ay).at(round_mp_->left.ax));
            }
        }

        if (z_finded_cnt == 0) {
            if (peakMove(s_z_direction, z_facula_info, m_z_function_select, move_dis_, move_delta_step) == EFaculaStatus::eOK) {
                z_finded_cnt++;  //
            }
        }

        m_adjust_len_step = EAdjustStep::eSYMMETRY_STEP;
        break;
    default:
        break;
    }

    LOG_INFO(MyLogger::LogType::PROCESS_DATA, QString("facula cal move x: %1, move y: %2, move z: %3").arg(move_dis_->x).arg(move_dis_->y).arg(move_dis_->z));

    return mu_facula_adjust.adjust_step;
}

#if 0
/**
 * @brief: 光斑规则与判定
 * 光斑数据，目标区域 ->移动距离
 *
 * 判定条件：
 * 1. 最强peak
 * 2. 比周围值大
 *
 * @return move_step
 */
int8_t CFaculaCircle::faculaAdjust(StMapData *map_data_, const C3dHandMachine::St3D<int16_t> &move_delta_step, C3dHandMachine::St3D<int16_t> *move_dis_)
{
    //* initial
    //  static uint8_t    adjust_step = 0;
    //  static uint16_t   theta = 0;
    //  static uint8_t    find_facula_cnt = 0;
    //  static uint16_t   find_radius = m_xml_param["find_origin_raduis"];

    static uint8_t    peak_limit_cnt = 0; //peak过小次数
    //*
    uint32_t max_tmp = map_data_->max_peak, mp_data_s = map_data_->peak_second;

    move_dis_->x = 0;
    move_dis_->y = 0;
    move_dis_->z = 0;

    qDebug() << "-i facula adjust/ step: " << m_adjust_step;

    if(m_adjust_step == 0) {
        move_dis_->z = m_xml_param["initial_z_dist"] / 8;
        m_adjust_step = 1;
    }
    else if(m_adjust_step == 1) {//寻光斑步骤
        if(max_tmp < 100) {
            int16_t x_tmp, y_tmp;
            double radian;

            /*1. 认为无光斑，风车型移动*/
            if(m_theta > 360) { //报错
                if(m_find_facula_cnt > (m_xml_param["find_times"] - 2)) {
                    qDebug() << "-clen find facula false: " << m_theta;
                    return -2;
                }
                m_theta = 0;
                m_find_radius += m_xml_param["find_radius_step"];
                m_find_facula_cnt ++;
            }
            radian = m_theta * 3.14 / 180;
            x_tmp = qRound(m_find_radius * cos(radian) / 7); //
            y_tmp = qRound(m_find_radius * sin(radian) / 7); //

            move_dis_->x = x_tmp - move_delta_step.x;
            move_dis_->y = y_tmp - move_delta_step.y;
            m_theta += 10; //
        }
        else {
            m_adjust_step = 2;

            //* reinit
            m_theta = 0;
            m_find_facula_cnt = 0;
            m_find_radius = m_xml_param["find_origin_raduis"];

            m_z_is_finded = 0;
        }
    }
    else if(m_adjust_step > 1) {
        uint8_t data_tmp = max_tmp / 20;
        int16_t xy_peak_delta_limit = data_tmp > 50? data_tmp:50;

        int8_t target_x_delta = 0, target_y_delta = 0;
        uint16_t right_peak = 0, left_peak = 0, down_peak = 0, up_peak = 0;
        uint16_t peak_aver = 0;
        uint16_t max_peak_delta = 0;

        int16_t peak_delta_x = 0, peak_delta_y = 0; //上 - 下

        if(max_tmp < 3) {
            peak_limit_cnt++;
            if(peak_limit_cnt == 3) {
                peak_limit_cnt = 0;
                m_adjust_step = 1;
            }
            return 0;
        }

        //* 第一步 中心
        if(m_adjust_step == 2) { //
            target_x_delta = map_data_->max_mp.ax - mst_target_info.center_tf.ax;
            target_y_delta = map_data_->max_mp.ay - mst_target_info.center_tf.ay;

            right_peak = map_data_->map_matrix.at(mst_target_info.center_right.ay).at(mst_target_info.center_right.ax);
            left_peak = map_data_->map_matrix.at(mst_target_info.center_left.ay).at(mst_target_info.center_left.ax);
            down_peak = map_data_->map_matrix.at(mst_target_info.center_down.ay).at(mst_target_info.center_down.ax);
            up_peak = map_data_->map_matrix.at(mst_target_info.center_up.ay).at(mst_target_info.center_up.ax);
            peak_delta_x = right_peak - (left_peak + m_xml_param["LR_peak_offset"]); //左 - 右
        }
        else { //目标区域
            target_x_delta = map_data_->max_mp.ax - mst_target_info.target_tf.ax;
            target_y_delta = map_data_->max_mp.ay - mst_target_info.target_tf.ay;

            left_peak = map_data_->map_matrix.at(mst_target_info.target_tf.ay).at(mst_target_info.target_tf.ax - 1);
            down_peak = map_data_->map_matrix.at(mst_target_info.target_tf.ay + 1).at(mst_target_info.target_tf.ax);
            up_peak = map_data_->map_matrix.at(mst_target_info.target_tf.ay - 1).at(mst_target_info.target_tf.ax);
            right_peak = m_left_center_peak;
            peak_delta_x = right_peak - left_peak; //左 - 右
        }

        peak_delta_y = down_peak - (up_peak + m_xml_param["UD_peak_offset"]); //上 - 下
        peak_aver = (right_peak + left_peak + down_peak + up_peak)>>2;

        //*
        if((abs(target_x_delta) > 0) || (abs(target_y_delta) > 0)) {/*1.1 光斑不在中心*/
            peak_limit_cnt = 0;

            if(mp_data_s == max_tmp)
            {
                m_adjust_step = 1;

                return 0; //相同最大值，不移动
            }

            move_dis_->y = -target_x_delta;
            move_dis_->x = -target_y_delta;
        }
        else if((abs(peak_delta_x) > xy_peak_delta_limit) || (abs(peak_delta_y) > xy_peak_delta_limit)) {//不对称
            int8_t x_peak_move = 0, y_peak_move = 0;
            peak_limit_cnt = 0;
            //*反向 4300
            if(!mst_target_info.sensor_direction) {
                if(peak_delta_x != 0)
                    x_peak_move = (peak_delta_x/ xy_peak_delta_limit);
                if(peak_delta_y != 0)
                    y_peak_move = (peak_delta_y/ xy_peak_delta_limit);
            }
            else { //4302
                if(peak_delta_x != 0) {
                    x_peak_move = - (peak_delta_x / xy_peak_delta_limit); //XY_PEAK_DELTA_LIMIT; //右边大，正
                    if(x_peak_move > 1) //
                        x_peak_move = 2;
                    else if(x_peak_move > 0)
                        x_peak_move = 1;
                    else if(x_peak_move < -1)
                        x_peak_move = -2;
                    else if(x_peak_move < 0)
                        x_peak_move = -1;
                }
                if(peak_delta_y != 0) {
                    y_peak_move = - (peak_delta_y / xy_peak_delta_limit); //peak_delta_y/XY_PEAK_DELTA_LIMIT; //上大， 正
                    if(y_peak_move > 1) //
                        y_peak_move = 2;
                    else if(y_peak_move > 0)
                        y_peak_move = 1;
                    else if(y_peak_move < -1)
                        y_peak_move = -2;
                    else if(y_peak_move < 0)
                        y_peak_move = -1;
                }
            }
            move_dis_->y = x_peak_move;
            move_dis_->x = y_peak_move;

            if(m_adjust_step == 3) {
                qDebug() << "-i facula circle/ up peak:" << up_peak << down_peak;
            }
            //          m_z_is_finded = 0;
        }
        else {//最强peak寻找 max_tmp < map_data->mp_adjust.max_peak_limit
            int8_t status;
            if(m_adjust_step == 2) { //中心调节
                if(m_z_is_finded == 0) {
                    max_peak_delta = max_tmp - peak_aver;
                    if((max_tmp > (uint32_t)m_xml_param["peak_ok_threshold"])
                            && (max_peak_delta > peak_aver)) {
                        m_z_is_finded++;
                    }
                    else {
                        //* D4
                        if(0) {
                            status = zMaxPeakSingleStep(max_tmp, move_delta_step, move_dis_);
                            //status = z_peak_dichotomy_find(); //
                        }
                        else if(1) { //T4
                            //                            status = zFindDisperseFacula(max_peak_delta, move_delta_step, move_dis_);
                        }
                        if(status == true) m_z_is_finded++; //调节完毕
                        else if(status == -2) return -2;
                    }

                }
                else {
                    m_z_is_finded++;
                    qDebug() << "-i facula circle/ z times:" << m_z_is_finded;
                    if(m_z_is_finded > 2) {
                        peak_limit_cnt = 0; //peak过小次数
                        m_z_is_finded = 0;

                        if(mst_target_info.is_center_to_target) return 1; //完毕
                        else {
                            m_left_center_peak = left_peak;
                            m_adjust_step = 3;
                        }
                    }
                }
            }
            else {
                m_z_is_finded++;
                qDebug() << "-i facula circle/ z:" << up_peak << down_peak;
                if(m_z_is_finded > 5) {
                    peak_limit_cnt = 0; //peak过小次数

                    return 1; //完毕
                }
            }
        }
    }
    return 0;
}
#endif

/**
 * @brief 单步双向寻找
 */
IFaculaAdjust::EFaculaStatus CFaculaCircle::zMaxPeakSingleStep(int8_t &                             z_direction,
                                                               const StZFaculaInfo &                z_facula_info,
                                                               const C3dHandMachine::St3D<int16_t> &move_delta_step,
                                                               C3dHandMachine::St3D<int16_t> *      move_step_) {
    //    static int8_t z_direction = m_xml_param["default_z_direct"];
    static C3dHandMachine::St3D<int16_t> st_max_move_delta;


    if ((int)z_facula_info.max_peak > (m_last_max_peak + Z_PEAK_HEAD_DELTA)) {  //最大peak
        m_last_max_peak     = z_facula_info.max_peak;
        m_z_peak_stable_cnt = 0;

        st_max_move_delta = move_delta_step;
        move_step_->z     = z_direction * m_xml_param["z_move_step"];
    } else if ((int)z_facula_info.max_peak < (m_last_max_peak - Z_PEAK_REVERSE_DELTA)) {  //最强位置反向找
        z_direction = -z_direction;

        if (z_direction == m_xml_param["default_z_direct"]) {  //搜寻完毕
            move_step_->x = st_max_move_delta.x - move_delta_step.x;
            move_step_->y = st_max_move_delta.y - move_delta_step.y;
            move_step_->z = st_max_move_delta.z - move_delta_step.z;

            return EFaculaStatus::eOK;
        }
        m_z_peak_stable_cnt = 0;
        move_step_->z       = st_max_move_delta.z - move_delta_step.z + z_direction * m_xml_param["z_move_step"];
    } else {  //无明显变化
        if (m_z_peak_stable_cnt++ > 3) {
            return EFaculaStatus::eOK;
        }

        move_step_->z = z_direction * m_xml_param["z_move_step"];
    }
    LOG_INFO(MyLogger::LogType::PROCESS_DATA, QString("last max peak: %1, z peak stable cnt: %2").arg(z_direction).arg(m_z_peak_stable_cnt));
    return EFaculaStatus::eWAIT;
}

/**
 * @brief 虚光斑调节
 * @param 默认向下调节
 * @param 中间接近两边
 * @param 中间数值小于550
 * @param
 * @return
 */
IFaculaAdjust::EFaculaStatus CFaculaCircle::zFindDisperseFacula(const StZDisperseFacula &z_disperse_info, C3dHandMachine::St3D<int16_t> *move_step_) {
    uint32_t corner_peak_sum = 0;
    int16_t  arc_peak_delta  = (m_xml_param["ACR_peak_delta"] - 30) < 0 ? 50 : (m_xml_param["ACR_peak_delta"] - 30);

    //* 1. 4个脚 > 200?
    for (uint8_t for_i = 0; for_i < z_disperse_info.edge_corner.count(); for_i++) {
        corner_peak_sum += z_disperse_info.edge_corner.at(for_i);
    }
    uint16_t corner_peak_aver = corner_peak_sum / z_disperse_info.edge_corner.count();

    uint8_t move_mutil = z_disperse_info.max_around_peak / 100;  // m_xml_param["CR_peak_delta"];
    move_mutil         = move_mutil < 1 ? 1 : move_mutil;
    move_mutil         = move_mutil > 4 ? 4 : move_mutil;

    if (((corner_peak_aver < m_xml_param["Aedge_peak_threshold"]) && (z_disperse_info.max_around_peak > m_xml_param["ACR_peak_delta"]) &&
         (z_disperse_info.max_peak > (m_xml_param["peak_threshold"] + 30))) ||
        (z_disperse_info.max_peak > m_xml_param["AMax_peak"])) {

        move_step_->z = m_xml_param["default_z_direct"] * m_xml_param["z_move_step"] * move_mutil;
        return EFaculaStatus::eWAIT;
    } else if ((corner_peak_aver > m_xml_param["Aedge_peak_threshold"]) && (z_disperse_info.max_around_peak < arc_peak_delta) &&
               (z_disperse_info.max_peak < (int16_t)(m_xml_param["AMax_peak"] - 100))) {
        move_step_->z = (-1) * m_xml_param["default_z_direct"] * m_xml_param["z_move_step"];  // * move_mutil;
        return EFaculaStatus::eWAIT;
    } else {
        return EFaculaStatus::eOK;
    }
}

/**
 * @brief:
 */
void CFaculaCircle::findMaxMP(StMapData *map_data_) {
    uint32_t max_tmp = 0, mp_data, mp_data_s;

    //    uint8_t ylen = map_data->ylens;
    //    uint8_t xlen = map_data->xlens;
    uint8_t ylen = map_data_->map_matrix.length();
    uint8_t xlen = map_data_->map_matrix.at(0).length();

    for (uint8_t y = 0; y < ylen; ++y) {
        for (uint8_t x = 0; x < xlen; ++x) {
            mp_data = map_data_->map_matrix.at(y).at(x);
            if (mp_data > max_tmp) {
                max_tmp              = mp_data;
                map_data_->max_mp.ay = y;
                map_data_->max_mp.ax = x;
            } else if (mp_data == max_tmp) {
                mp_data_s = mp_data;
            }
        }
    }
    map_data_->max_mp.peak = max_tmp;
    map_data_->max_peak    = max_tmp;
    map_data_->peak_second = mp_data_s;
}

uint16_t CFaculaCircle::faculaTest(StMapData *map_data_, QVector<uint32_t> &target_facula, const EFaculaJudgeMode &test_mode, const bool &has_adjusted) {
    IFaculaAdjust::UFaculaJudgeDds facula_dds_info;
    facula_dds_info.dds_info = 0;  //

    //    uint16_t right_peak = 0, left_peak = 0, down_peak = 0, up_peak = 0;
    int8_t target_x_delta = 0, target_y_delta = 0;
    //    int16_t peak_delta_x = 0, peak_delta_y = 0;
    uint16_t          peak_aver = 0;  //上 - 下
    QVector<uint32_t> edge_corner;
    StRoundJudgeMP *  round_mp_ = &mst_target_info.target_round;


    target_x_delta = map_data_->max_mp.ax - mst_target_info.target_round.center.ax;
    target_y_delta = map_data_->max_mp.ay - mst_target_info.target_round.center.ay;

    //    left_peak = map_data_->map_matrix.at(mst_target_round_->left.ay).at(mst_target_round_->left.ax);
    //    down_peak = map_data_->map_matrix.at(mst_target_round_->down.ay).at(mst_target_round_->down.ax);
    //    up_peak = map_data_->map_matrix.at(mst_target_round_->up.ay).at(mst_target_round_->up.ax);

    updateRoundMpPeak(round_mp_, map_data_->map_matrix, has_adjusted);
    StSymmetryInfo symmetry_mp_delta = getSymmetryDelta(round_mp_, map_data_->map_matrix, mm_symmetry_mp_param[test_mode], false);

    peak_aver = (round_mp_->right.peak + round_mp_->left.peak + round_mp_->down.peak + round_mp_->up.peak) >> 2;

    // get 3*3 target mp data
    if (!mst_target_info.is_center_to_target) {  //非边缘点
        //        right_peak = map_data_->map_matrix.at(mst_target_round_->right.ay).at(mst_target_round_->right.ax);
        //        peak_delta_x = right_peak - (left_peak + m_xml_param["LR_peak_offset"]); //左 - 右

        if (map_data_->map_matrix.size() >= 3 && map_data_->map_matrix.at(0).size() >= 3) {
            // edge_corner.append(map_data_->map_matrix.at(mst_target_round_->up.ay).at(mst_target_round_->left.ax));     //顺时针
            // edge_corner.append(map_data_->map_matrix.at(mst_target_round_->up.ay).at(mst_target_round_->right.ax));    //顺时针
            // edge_corner.append(map_data_->map_matrix.at(mst_target_round_->down.ay).at(mst_target_round_->right.ax));  //顺时针
            // edge_corner.append(map_data_->map_matrix.at(mst_target_round_->down.ay).at(mst_target_round_->left.ax));   //顺时针
            edge_corner.append(map_data_->map_matrix.at(round_mp_->up.ay).at(round_mp_->left.ax));
            edge_corner.append(map_data_->map_matrix.at(round_mp_->up.ay).at(round_mp_->right.ax));
            edge_corner.append(map_data_->map_matrix.at(round_mp_->down.ay).at(round_mp_->right.ax));
            edge_corner.append(map_data_->map_matrix.at(round_mp_->down.ay).at(round_mp_->left.ax));

            target_facula.append(edge_corner.at(0));
            target_facula.append(round_mp_->up.peak);
            target_facula.append(edge_corner.at(1));
            target_facula.append(round_mp_->left.peak);
            target_facula.append(map_data_->map_matrix.at(round_mp_->center.ay).at(round_mp_->center.ax));
            target_facula.append(round_mp_->right.peak);
            target_facula.append(edge_corner.at(3));
            target_facula.append(round_mp_->down.peak);
            target_facula.append(edge_corner.at(2));
        }
    } else {                 //边缘点
        if (has_adjusted) {  //有调节(非手动录入)
            //            right_peak = m_left_center_peak;
            //            peak_delta_x = right_peak - left_peak;
        } else {  //无调节，直接判定，无右侧数据，有上下数据代替
            m_last_max_peak = map_data_->max_peak;
        }

        target_facula.append(0);
        target_facula.append(round_mp_->up.peak);
        target_facula.append(0);
        target_facula.append(round_mp_->left.peak);
        target_facula.append(map_data_->map_matrix.at(round_mp_->center.ay).at(round_mp_->center.ax));
        target_facula.append(round_mp_->right.peak);
        target_facula.append(0);
        target_facula.append(round_mp_->down.peak);
        target_facula.append(0);
    }

    if (map_data_->map_matrix.size() >= 3 && map_data_->map_matrix.at(0).size() >= 3) {
        //            edge_corner.append(map_data_->map_matrix.at(mst_target_round_->up.ay).at(mst_target_round_->left.ax)); //顺时针
        //            edge_corner.append(map_data_->map_matrix.at(mst_target_round_->up.ay).at(mst_target_round_->right.ax)); //顺时针
        //            edge_corner.append(map_data_->map_matrix.at(mst_target_round_->down.ay).at(mst_target_round_->right.ax)); //顺时针
        //            edge_corner.append(map_data_->map_matrix.at(mst_target_round_->down.ay).at(mst_target_round_->left.ax)); //顺时针

        if ((round_mp_->up.ay > 0) && (round_mp_->up.ay < mst_target_info.map_ylen)) {
            if (round_mp_->left.ax > 0) {
                edge_corner.append(map_data_->map_matrix.at(round_mp_->up.ay).at(round_mp_->left.ax));
            } else {
                edge_corner.append(map_data_->map_matrix.at(round_mp_->up.ay).at(round_mp_->right.ax));
            }
            if (round_mp_->right.ax < mst_target_info.map_xlen) {
                edge_corner.append(map_data_->map_matrix.at(round_mp_->up.ay).at(round_mp_->right.ax));
            } else {
                edge_corner.append(map_data_->map_matrix.at(round_mp_->up.ay).at(round_mp_->left.ax));
            }
        }
        if ((round_mp_->down.ay > 0) && (round_mp_->down.ay < mst_target_info.map_ylen)) {
            if (round_mp_->right.ax < mst_target_info.map_xlen)
                edge_corner.append(map_data_->map_matrix.at(round_mp_->down.ay).at(round_mp_->right.ax));
            else {
                edge_corner.append(map_data_->map_matrix.at(round_mp_->down.ay).at(round_mp_->left.ax));
            }
            if (round_mp_->left.ax > 0) {
                edge_corner.append(map_data_->map_matrix.at(round_mp_->down.ay).at(round_mp_->left.ax));
            } else {
                edge_corner.append(map_data_->map_matrix.at(round_mp_->down.ay).at(round_mp_->right.ax));
            }
        }
    }

    // 多通道光斑中心判定
    bool multi_channel_success = false;

    if (mu_facula_detect_items.all_info.facula_target) {  // 光斑中心判定
        bool is_in_valid_channel = false;

        // 从配置管理器获取多通道配置
        Config::ConfigService &       configService    = Config::ConfigService::getInstance();
        Config::DynamicConfigManager &dynamicManager   = configService.getDynamicManager();
        auto *                        faculaConfigData = dynamicManager.getConfig<PhotonSensor::FaculaConfigData>("Facula");

        if (!faculaConfigData) {
            qDebug() << "[CFaculaCircle] 无法获取光斑配置，使用默认值";
            return 0;
        }

        const auto &faculaConfig = *faculaConfigData;

        // 多通道判定逻辑
        if (!faculaConfig.facula_center_points.isEmpty() && faculaConfig.facula_center_points.size() > 1) {
            // 检查光斑中心是否在任一配置通道内
            for (const QPoint &point : faculaConfig.facula_center_points) {
                int8_t channel_x_delta = map_data_->max_mp.ax - static_cast<int8_t>(point.x());
                int8_t channel_y_delta = map_data_->max_mp.ay - static_cast<int8_t>(point.y());

                if ((channel_x_delta == 0) && (channel_y_delta == 0)) {
                    // 光斑中心在此通道内，检查peak值是否满足要求
                    if (map_data_->max_peak >= faculaConfig.facula_center_peak_threshold) {
                        is_in_valid_channel   = true;
                        multi_channel_success = true;  // 多通道判定成功，跳过后续判定
                        LOG_INFO(MyLogger::LogType::RESULT_CACHE_DATA,
                                 QString("Multi-channel facula center match: channel(%1,%2), peak: %3, threshold: %4 - skipping other tests")
                                     .arg(point.x())
                                     .arg(point.y())
                                     .arg(map_data_->max_peak)
                                     .arg(faculaConfig.facula_center_peak_threshold));
                        break;
                    } else {
                        LOG_WARN(MyLogger::LogType::RESULT_CACHE_DATA,
                                 QString("Multi-channel facula center in channel(%1,%2) but peak too low: %3 < %4")
                                     .arg(point.x())
                                     .arg(point.y())
                                     .arg(map_data_->max_peak)
                                     .arg(faculaConfig.facula_center_peak_threshold));
                    }
                }
            }

            // 如果不在任何有效通道内，标记为失败
            if (!is_in_valid_channel) {
                facula_dds_info.all_info.facula_target = true;
                LOG_WARN(MyLogger::LogType::RESULT_CACHE_DATA,
                         QString("Multi-channel facula center not in valid channels: actual(%1,%2), max_peak: %3")
                             .arg(map_data_->max_mp.ax)
                             .arg(map_data_->max_mp.ay)
                             .arg(map_data_->max_peak));
            }
        } else {
            // 单通道判定逻辑
            if (!((target_x_delta == 0) && (target_y_delta == 0))) {
                facula_dds_info.all_info.facula_target = true;
                LOG_WARN(MyLogger::LogType::RESULT_CACHE_DATA,
                         QString("Single-channel facula center not in target: delta(%1,%2)").arg(target_x_delta).arg(target_y_delta));
            }
        }
    }

    // 如果多通道判定成功，跳过后续的对称性和其他判定
    if (multi_channel_success) {
        LOG_INFO(MyLogger::LogType::RESULT_CACHE_DATA, "Multi-channel facula test passed - returning success");
        return facula_dds_info.dds_info;
    }

    if (mu_facula_detect_items.all_info.center_peak_down) {  //中心阈值
        switch (m_z_function_select) {
        case EZFindFunction::eMAX_PEAK:
            if (!(((int)map_data_->max_peak > (m_last_max_peak - Z_PEAK_DELTA_JUDGE)) && (map_data_->max_peak > (uint32_t)m_xml_param["peak_threshold"]) &&
                  (map_data_->max_peak < (uint32_t)m_xml_param["peak_max_threshold"]))) {
                facula_dds_info.all_info.center_peak_down = true;

                LOG_WARN(MyLogger::LogType::RESULT_CACHE_DATA,
                         QString("JUDAGE_STEP: center max peak:  center_max_peak: %1, last_max_peak: %2, peak_threshold: %3")
                             .arg(map_data_->max_peak)
                             .arg(m_last_max_peak)
                             .arg(m_xml_param["peak_threshold"]));
            }
            break;
        case EZFindFunction::eDISPERSE:
            if (!((map_data_->max_peak > (uint32_t)m_xml_param["peak_threshold"]) && (map_data_->max_peak < (uint32_t)m_xml_param["peak_max_threshold"]))) {
                facula_dds_info.all_info.center_peak_down = true;

                LOG_WARN(MyLogger::LogType::RESULT_CACHE_DATA,
                         QString("JUDAGE_STEP: center disperse peak: center_max_peak: %1, peak_threshold: %2")
                             .arg(map_data_->max_peak)
                             .arg(m_xml_param["peak_threshold"]));
            }
            break;
        default:
            break;
        }
    }

    if (mu_facula_detect_items.all_info.facula_symmetry) {  // 光斑对称
                                                            //        C3dHandMachine::St3D<int16_t> symmetry_peak_threshold =
        //            getSymmetryPeakThreshold(map_data_->map_matrix.at(round_mp_->center.ay).at(round_mp_->center.ax), test_mode);

        if (!((abs(symmetry_mp_delta.peak_value.x) < symmetry_mp_delta.peak_threshold_value.x) &&
              (abs(symmetry_mp_delta.peak_value.y) < symmetry_mp_delta.peak_threshold_value.y))) {
            facula_dds_info.all_info.facula_symmetry = true;

            LOG_WARN(MyLogger::LogType::RESULT_CACHE_DATA,
                     QString("center_mp_peak: %1, x_peak_delta: %2, y_peak_delta: %3, peak_threshold_x: %4, peak_threshold_y: %5")
                         .arg(map_data_->map_matrix.at(round_mp_->center.ay).at(round_mp_->center.ax))
                         .arg(symmetry_mp_delta.peak_value.x)
                         .arg(symmetry_mp_delta.peak_value.y)
                         .arg(symmetry_mp_delta.peak_threshold_value.x)
                         .arg(symmetry_mp_delta.peak_threshold_value.y));
        }
    }

    if (mu_facula_detect_items.all_info.facula_round_sym) {  //外圈对称
        // if(!result) qDebug() << "-e max peak/ max_peak: " << map_data_->max_peak << "last_max_peak:" << m_last_max_peak << "peak_aver: " << peak_aver;
    }


    if (mu_facula_detect_items.all_info.round_peak_down) {  //周围最小阈值->非边缘点才能判定
        uint32_t corner_peak_sum = 0;
        for (uint8_t for_i = 0; for_i < edge_corner.count(); for_i++) {
            corner_peak_sum += edge_corner.at(for_i);
        }
        uint16_t corner_peak_aver = corner_peak_sum / edge_corner.count();

        if (corner_peak_aver < m_xml_param["edge_peak_threshold"]) {
            facula_dds_info.all_info.round_peak_down = true;
        }
    }

    if (mu_facula_detect_items.all_info.CR_peak_delta) {  //中心与周围差值,大于最小阈值
        if (!((int)(map_data_->max_peak - peak_aver) > m_xml_param["CR_peak_delta"]))
            facula_dds_info.all_info.CR_peak_delta = true;
    }

    return facula_dds_info.dds_info;
}
