# 光斑判定标准-多通道判定实现

[[光斑判定标准-多通道判定.excalidraw]]
[[光斑判定标准-多通道判定.md]]


## 配置文件格式

### 新配置格式 (clen_config.ini)

```ini
[ADJUST_PARAM]
# 多通道光斑中心配置 (新格式)
facula_center_channels=2,2;1,2;3,2  # 支持多通道，格式：x1,y1;x2,y2;x3,y3
facula_center_peak_threshold=800     # 多通道模式下的peak阈值

# 兼容性配置 (旧格式，如果没有多通道配置则使用此配置)
facula_center_loc_x=2 # 光斑中心坐标x (兼容性)
facula_center_loc_y=2 # 光斑中心坐标y (兼容性)
```

### 配置说明

1. **facula_center_channels**: 多通道配置字符串
   - 格式：`x1,y1;x2,y2;x3,y3`
   - 每个通道用分号分隔
   - 每个通道的x,y坐标用逗号分隔
   - 示例：`2,2;1,2;3,2` 表示三个通道：(2,2), (1,2), (3,2)

2. **facula_center_peak_threshold**: 多通道模式下的peak阈值
   - 当光斑中心在任一配置通道内且peak值大于此阈值时，判定为成功
   - 默认值：800

## 实现方案

当前光路功能主要分为三步：

1. 调节 + 判定
2. 固化后判定
3. 放气后判定

### 三种配置模式

1. **没有配置**：默认使用中心通道 (兼容性模式)
2. **配置一个通道**：三个判定步骤中，光斑中心(最强值)只能在这个通道
3. **配置多个通道**：三个判定步骤中，判定光斑中心处于这些通道中任一个且peak值大于配置值，**跳过后续其他的判定**(对称，周围通道判定等等)

### 修改部分

1. **配置文件结构** ✅
   - `lensReadIni.h/cpp`: 添加多通道配置解析
   - 支持新配置格式，保持向后兼容

2. **数据结构扩展** ✅
   - `IFaculaAdjust::StMapInfo`: 添加多通道支持
   - `ISensorBoardFactory::StFaculaSensorInfo`: 添加多通道字段
   - `CFaculaCircle::StTargetInfo`: 添加多通道信息

3. **判定逻辑实现** ✅
   - `CFaculaCircle::faculaTest`: 实现多通道判定逻辑
   - 多通道成功时跳过后续判定，提高效率

### 核心判定逻辑

```cpp
// 多通道光斑中心判定
bool multi_channel_success = false;

if (mu_facula_detect_items.all_info.facula_target) {
    if (!mst_target_info.target_channels.isEmpty()) {
        // 检查光斑中心是否在任一配置通道内
        for (const auto& channel : mst_target_info.target_channels) {
            int8_t channel_x_delta = map_data_->max_mp.ax - channel.ax;
            int8_t channel_y_delta = map_data_->max_mp.ay - channel.ay;

            if ((channel_x_delta == 0) && (channel_y_delta == 0)) {
                // 光斑中心在此通道内，检查peak值
                if (map_data_->max_peak >= mst_target_info.multi_channel_peak_threshold) {
                    multi_channel_success = true;  // 成功，跳过后续判定
                    break;
                }
            }
        }
    }
}

// 如果多通道判定成功，跳过后续的对称性和其他判定
if (multi_channel_success) {
    return facula_dds_info.dds_info;  // 直接返回成功
}
```

## 文档更新

- [[TOF接收镜片光路耦合软件使用文档]]
- 配置文件示例和说明

## 测试验证

### 配置示例

1. **单通道配置**：
   ```ini
   facula_center_channels=2,2
   facula_center_peak_threshold=800
   ```

2. **多通道配置**：
   ```ini
   facula_center_channels=2,2;1,2;3,2
   facula_center_peak_threshold=800
   ```

3. **兼容性配置** (无多通道配置时自动使用)：
   ```ini
   facula_center_loc_x=2
   facula_center_loc_y=2
   ```

### 预期行为

- 多通道模式下，光斑中心在任一配置通道内且peak值满足要求时，直接判定成功
- 跳过后续的对称性判定、周围通道判定等，提高判定效率
- 保持与原有单通道配置的完全兼容性