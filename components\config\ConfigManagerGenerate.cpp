#include "ConfigManager.h"
#include <QApplication>
#include <QFile>
#include <QFileInfo>
#include <QSettings>
#include <QXmlStreamWriter>

namespace Config {

// 生成默认配置文件
ConfigResult ConfigManager::generateDefaultConfigs() {
    logInfo("Checking and generating default configuration files...");

    ConfigResult result;

    // 生成缺失的配置文件
    if (!configFileExists(FileType::System)) {
        result = generateDefaultSystemConfig();
        if (!result.success)
            return result;
    }

    if (!configFileExists(FileType::Facula)) {
        result = generateDefaultFaculaConfig();
        if (!result.success)
            return result;
    }

    if (!configFileExists(FileType::Hardware)) {
        result = generateDefaultHardwareConfig();
        if (!result.success)
            return result;
    }

    if (!configFileExists(FileType::Algorithm)) {
        result = generateDefaultAlgorithmConfig();
        if (!result.success)
            return result;
    }

    logInfo("Default configuration files generation completed");
    return ConfigResult(true);
}

// 生成默认系统配置
ConfigResult ConfigManager::generateDefaultSystemConfig() {
    QString filePath = getConfigFilePath(FileType::System);
    logInfo("Generating default system configuration: " + filePath);

    QSettings settings(filePath, QSettings::IniFormat);

    // 写入默认系统配置
    settings.beginGroup("SENSOR_BOARD");
    settings.setValue("version", m_systemConfig.version);
    settings.endGroup();

    settings.beginGroup("MES");
    settings.setValue("userid", m_systemConfig.userid);
    settings.setValue("op", m_systemConfig.op);
    settings.setValue("work_number", m_systemConfig.work_number);
    settings.setValue("work_domain", m_systemConfig.work_domain);
    settings.endGroup();

    settings.beginGroup("DEVICE");
    settings.setValue("sensor_device", m_systemConfig.sensor_device);
    settings.setValue("sensor_device_baud", m_systemConfig.sensor_device_baud);
    settings.setValue("station_number", m_systemConfig.station_number);
    settings.setValue("clens_machine_brand", m_systemConfig.clens_machine_brand);
    settings.endGroup();

    settings.sync();

    if (settings.status() != QSettings::NoError) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to generate default system configuration");
    }

    logInfo("Default system configuration generated successfully");
    return ConfigResult(true);
}

// 生成默认光斑配置
ConfigResult ConfigManager::generateDefaultFaculaConfig() {
    QString filePath = getConfigFilePath(FileType::Facula);
    logInfo("Generating default facula configuration: " + filePath);

    QSettings settings(filePath, QSettings::IniFormat);

    // 写入光斑中心配置
    settings.beginGroup("FACULA_CENTER");
    settings.setValue("facula_center_channels", m_faculaConfig.facula_center_channels);
    settings.setValue("facula_center_peak_threshold", m_faculaConfig.facula_center_peak_threshold);
    settings.setValue("facula_center_loc_x", m_faculaConfig.facula_center_loc_x);
    settings.setValue("facula_center_loc_y", m_faculaConfig.facula_center_loc_y);
    settings.endGroup();

    // 调节参数现在从AdjustProcessConfig获取，但为了兼容性，仍然生成到光斑配置文件中
    settings.beginGroup("ADJUST_PARAM");
    settings.setValue("facula_ok_times", m_adjustProcessConfig.facula_ok_times);
    settings.setValue("solid_time", m_adjustProcessConfig.solid_time);
    settings.setValue("facula_ng_handle", m_adjustProcessConfig.facula_ng_handle);
    settings.endGroup();

    // 写入处理类型
    settings.beginGroup("FACULA_HANDLE");
    settings.setValue("facula_handle_type", m_faculaConfig.facula_handle_type);
    settings.endGroup();

    // 图像处理参数已移到AlgorithmConfig中，这里不再生成

    settings.sync();

    if (settings.status() != QSettings::NoError) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to generate default facula configuration");
    }

    logInfo("Default facula configuration generated successfully");
    return ConfigResult(true);
}

// 生成默认硬件配置
ConfigResult ConfigManager::generateDefaultHardwareConfig() {
    QString filePath = getConfigFilePath(FileType::Hardware);
    logInfo("Generating default hardware configuration: " + filePath);

    QSettings settings(filePath, QSettings::IniFormat);

    // 写入硬件配置
    settings.beginGroup("HARDWARE");
    settings.setValue("xy_radius_limit", m_hardwareConfig.xy_radius_limit);
    settings.setValue("z_radius_limit", m_hardwareConfig.z_radius_limit);
    settings.setValue("x_step_dist", m_hardwareConfig.x_step_dist);
    settings.setValue("y_step_dist", m_hardwareConfig.y_step_dist);
    settings.setValue("z_step_dist", m_hardwareConfig.z_step_dist);
    settings.endGroup();

    settings.sync();

    if (settings.status() != QSettings::NoError) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to generate default hardware configuration");
    }

    logInfo("Default hardware configuration generated successfully");
    return ConfigResult(true);
}

// 生成默认算法配置
ConfigResult ConfigManager::generateDefaultAlgorithmConfig() {
    QString filePath = getConfigFilePath(FileType::Algorithm);
    logInfo("Generating default algorithm configuration: " + filePath);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return ConfigResult(false, ErrorType::PermissionError, "Cannot create algorithm config file: " + filePath);
    }

    QXmlStreamWriter xmlWriter(&file);
    xmlWriter.setAutoFormatting(true);
    xmlWriter.writeStartDocument();

    xmlWriter.writeStartElement("Parameters");

    // 写入所有算法参数
    for (auto it = m_algorithmConfig.parameters.begin(); it != m_algorithmConfig.parameters.end(); ++it) {
        xmlWriter.writeTextElement(it.key(), QString::number(it.value()));
    }

    xmlWriter.writeEndElement();  // Parameters
    xmlWriter.writeEndDocument();

    file.close();

    logInfo("Default algorithm configuration generated successfully");
    return ConfigResult(true);
}

// 生成默认调节流程配置
ConfigResult ConfigManager::generateDefaultAdjustProcessConfig() {
    QString filePath = getConfigFilePath(FileType::AdjustProcess);

    // 确保目录存在
    QFileInfo fileInfo(filePath);
    if (!ensureModuleConfigDirectory("lenAdjust")) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create module config directory");
    }

    QSettings settings(filePath, QSettings::IniFormat);

    // 写入调节流程参数
    settings.beginGroup("ADJUST_PARAM");
    settings.setValue("facula_ok_times", m_adjustProcessConfig.facula_ok_times);
    settings.setValue("solid_time", m_adjustProcessConfig.solid_time);
    settings.setValue("facula_ng_handle", m_adjustProcessConfig.facula_ng_handle);
    settings.endGroup();

    settings.sync();

    logInfo("Default adjust process configuration generated successfully");
    return ConfigResult(true);
}

}  // namespace Config
