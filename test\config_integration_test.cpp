/**
 * @file config_integration_test.cpp
 * @brief 配置系统集成测试 - 模拟主软件光路调节功能
 * 
 * 此测试程序模拟主软件的光路调节功能，验证：
 * 1. 配置文件的自动部署
 * 2. 配置参数的正确加载
 * 3. 光路调节功能的参数应用
 * 4. 配置更新的实时生效
 */

#include <QApplication>
#include <QTimer>
#include <QDebug>
#include <QDir>
#include <iostream>

// 包含光路调节相关组件
#include "../components/config/ConfigManager.h"
#include "../components/config/ConfigDeployer.h"
#include "../components/lensAdjust/lensReadIni.h"
#include "../sensor/photonSensor/IFaculaAdjust.h"

class LensAdjustSimulator : public QObject {
    Q_OBJECT

public:
    LensAdjustSimulator(QObject *parent = nullptr) : QObject(parent) {
        m_configManager = Config::ConfigManager::getInstance();
        m_lensReader = new ClensReadIni();
        m_faculaAdjust = new IFaculaAdjust();
    }

    ~LensAdjustSimulator() {
        delete m_lensReader;
        delete m_faculaAdjust;
    }

    bool initialize() {
        std::cout << "🚀 初始化光路调节模拟器..." << std::endl;
        
        // 1. 部署配置文件
        if (!deployConfigs()) {
            std::cout << "❌ 配置文件部署失败" << std::endl;
            return false;
        }
        
        // 2. 加载配置
        if (!loadConfigs()) {
            std::cout << "❌ 配置加载失败" << std::endl;
            return false;
        }
        
        // 3. 初始化组件
        if (!initializeComponents()) {
            std::cout << "❌ 组件初始化失败" << std::endl;
            return false;
        }
        
        std::cout << "✅ 光路调节模拟器初始化成功" << std::endl;
        return true;
    }

    void runSimulation() {
        std::cout << "\n🧪 开始光路调节功能模拟测试..." << std::endl;
        
        // 模拟光路调节流程
        simulateFaculaDetection();
        simulateAlgorithmExecution();
        simulateHardwareControl();
        simulateConfigUpdate();
        
        std::cout << "✅ 光路调节功能模拟测试完成" << std::endl;
    }

private:
    bool deployConfigs() {
        std::cout << "📦 部署配置文件..." << std::endl;
        
        Config::ConfigDeployer deployer;
        Config::ConfigDeployer::DeployResult result = deployer.deployAllConfigs(
            Config::ConfigDeployer::DeployStrategy::SkipExisting
        );
        
        std::cout << "  📊 部署结果: 成功=" << result.deployedFiles.size() 
                  << ", 跳过=" << result.skippedFiles.size() 
                  << ", 错误=" << result.errorFiles.size() << std::endl;
        
        return result.success || result.errorFiles.isEmpty();
    }

    bool loadConfigs() {
        std::cout << "📖 加载配置文件..." << std::endl;
        
        // 加载所有配置
        Config::ConfigResult result = m_configManager->loadAllConfigs();
        if (!result.success) {
            std::cout << "  ❌ 配置加载失败: " << result.message.toStdString() << std::endl;
            return false;
        }
        
        // 验证配置数据
        const Config::FaculaConfig &faculaConfig = m_configManager->getFaculaConfig();
        const Config::AlgorithmConfig &algorithmConfig = m_configManager->getAlgorithmConfig();
        const Config::HardwareConfig &hardwareConfig = m_configManager->getHardwareConfig();
        
        std::cout << "  ✅ 光斑配置: 通道=" << faculaConfig.facula_center_channels.toStdString()
                  << ", 阈值=" << faculaConfig.facula_center_peak_threshold << std::endl;
        
        std::cout << "  ✅ 算法配置: 参数数量=" << algorithmConfig.parameters.size() << std::endl;
        
        std::cout << "  ✅ 硬件配置: XY限制=" << hardwareConfig.xy_radius_limit 
                  << ", Z限制=" << hardwareConfig.z_radius_limit << std::endl;
        
        return true;
    }

    bool initializeComponents() {
        std::cout << "🔧 初始化组件..." << std::endl;
        
        // 初始化配置读取器
        try {
            ClensReadIni::IniConfig iniConfig = m_lensReader->readConfig();
            std::cout << "  ✅ 配置读取器初始化成功" << std::endl;
            std::cout << "    版本: " << iniConfig.version.toStdString() << std::endl;
            std::cout << "    用户: " << iniConfig.userid.toStdString() << std::endl;
            std::cout << "    设备: " << iniConfig.sensor_device << std::endl;
        } catch (const std::exception &e) {
            std::cout << "  ❌ 配置读取器初始化失败: " << e.what() << std::endl;
            return false;
        }
        
        // 初始化光斑调节器
        try {
            // IFaculaAdjust 在构造函数中自动加载配置
            std::cout << "  ✅ 光斑调节器初始化成功" << std::endl;
        } catch (const std::exception &e) {
            std::cout << "  ❌ 光斑调节器初始化失败: " << e.what() << std::endl;
            return false;
        }
        
        return true;
    }

    void simulateFaculaDetection() {
        std::cout << "\n🔍 模拟光斑检测..." << std::endl;
        
        const Config::FaculaConfig &config = m_configManager->getFaculaConfig();
        
        // 模拟多通道光斑检测
        QStringList channels = config.facula_center_channels.split(';');
        std::cout << "  📍 检测通道数量: " << channels.size() << std::endl;
        
        for (int i = 0; i < channels.size(); ++i) {
            QStringList coords = channels[i].split(',');
            if (coords.size() == 2) {
                int x = coords[0].toInt();
                int y = coords[1].toInt();
                
                // 模拟光斑强度检测
                int intensity = 600 + (i * 100) + (rand() % 200); // 模拟强度值
                bool detected = intensity > config.facula_center_peak_threshold;
                
                std::cout << "  📊 通道" << (i+1) << " (" << x << "," << y << "): "
                          << "强度=" << intensity 
                          << (detected ? " ✅检测到" : " ❌未检测到") << std::endl;
            }
        }
    }

    void simulateAlgorithmExecution() {
        std::cout << "\n⚙️ 模拟算法执行..." << std::endl;
        
        const Config::AlgorithmConfig &config = m_configManager->getAlgorithmConfig();
        
        // 模拟算法参数应用
        std::cout << "  🎯 应用算法参数:" << std::endl;
        
        auto it = config.parameters.find("find_origin_raduis");
        if (it != config.parameters.end()) {
            std::cout << "    搜索半径: " << it.value().toInt() << " 微米" << std::endl;
        }
        
        it = config.parameters.find("peak_ok_threshold");
        if (it != config.parameters.end()) {
            std::cout << "    峰值阈值: " << it.value().toInt() << std::endl;
        }
        
        it = config.parameters.find("z_move_step");
        if (it != config.parameters.end()) {
            std::cout << "    Z轴步长: " << it.value().toInt() << " 微米" << std::endl;
        }
        
        // 模拟算法执行过程
        std::cout << "  🔄 执行光路调节算法..." << std::endl;
        QThread::msleep(100); // 模拟算法执行时间
        std::cout << "  ✅ 算法执行完成" << std::endl;
    }

    void simulateHardwareControl() {
        std::cout << "\n🔧 模拟硬件控制..." << std::endl;
        
        const Config::HardwareConfig &config = m_configManager->getHardwareConfig();
        
        // 模拟硬件参数应用
        std::cout << "  📐 硬件限制参数:" << std::endl;
        std::cout << "    XY轴限制: " << config.xy_radius_limit << " 微米" << std::endl;
        std::cout << "    Z轴限制: " << config.z_radius_limit << " 微米" << std::endl;
        std::cout << "    X轴步距: " << config.x_step_dist << " 微米/脉冲" << std::endl;
        std::cout << "    Y轴步距: " << config.y_step_dist << " 微米/脉冲" << std::endl;
        std::cout << "    Z轴步距: " << config.z_step_dist << " 微米/脉冲" << std::endl;
        
        // 模拟硬件运动
        std::cout << "  🎮 模拟硬件运动:" << std::endl;
        std::cout << "    X轴移动: +50 微米" << std::endl;
        std::cout << "    Y轴移动: -30 微米" << std::endl;
        std::cout << "    Z轴移动: +10 微米" << std::endl;
        
        QThread::msleep(50); // 模拟硬件响应时间
        std::cout << "  ✅ 硬件控制完成" << std::endl;
    }

    void simulateConfigUpdate() {
        std::cout << "\n🔄 模拟配置更新..." << std::endl;
        
        // 模拟配置参数动态更新
        std::cout << "  📝 更新光斑阈值参数..." << std::endl;
        
        // 重新加载配置
        Config::ConfigResult result = m_configManager->loadAllConfigs();
        if (result.success) {
            std::cout << "  ✅ 配置重新加载成功" << std::endl;
        } else {
            std::cout << "  ⚠️ 配置重新加载失败: " << result.message.toStdString() << std::endl;
        }
    }

private:
    Config::ConfigManager *m_configManager;
    ClensReadIni *m_lensReader;
    IFaculaAdjust *m_faculaAdjust;
};

#include "config_integration_test.moc"

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    std::cout << "========================================" << std::endl;
    std::cout << "  光路调节配置系统集成测试" << std::endl;
    std::cout << "  Lens Adjust Config Integration Test" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
    
    LensAdjustSimulator simulator;
    
    if (!simulator.initialize()) {
        std::cout << "❌ 模拟器初始化失败" << std::endl;
        return 1;
    }
    
    simulator.runSimulation();
    
    std::cout << "\n🎉 集成测试完成！" << std::endl;
    std::cout << "💡 提示: 可以修改配置文件后重新运行测试验证配置更新功能" << std::endl;
    
    return 0;
}
