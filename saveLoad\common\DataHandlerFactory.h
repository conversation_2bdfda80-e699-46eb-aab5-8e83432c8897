#ifndef DATAHANDLERFACTORY_H
#define DATAHANDLERFACTORY_H

#include "IDataHandler.h"
#include <QMap>
#include <QStringList>
#include <functional>
#include <memory>

namespace SaveLoad {

/**
 * @brief 数据处理器工厂类
 * 
 * 使用工厂模式创建不同格式的数据处理器
 * 支持动态注册新的处理器类型
 */
class DataHandlerFactory {
public:
    // 工厂方法
    static std::unique_ptr<IDataHandler> createHandler(const QString& format);
    static std::unique_ptr<IDataHandler> createHandlerForFile(const QString& filePath);
    
    // 注册处理器工厂
    static void registerHandlerFactory(const QString& format, 
                                     std::shared_ptr<IDataHandlerFactory> factory);
    
    static void registerHandlerFactory(const QString& format,
                                     std::function<std::unique_ptr<IDataHandler>()> factoryFunc);
    
    // 查询支持的格式
    static QStringList supportedFormats();
    static bool isFormatSupported(const QString& format);
    static bool canHandleFile(const QString& filePath);
    
    // 格式检测
    static QString detectFormat(const QString& filePath);
    static QStringList detectPossibleFormats(const QString& filePath);
    
    // 获取格式信息
    static QString getFormatName(const QString& format);
    static QStringList getFormatExtensions(const QString& format);
    
    // 工厂管理
    static void unregisterHandlerFactory(const QString& format);
    static void clearAllFactories();
    
    // 初始化默认工厂
    static void initializeDefaultFactories();
    
    // 调试和诊断
    static void dumpRegisteredFactories();
    static QStringList getRegisteredFormats();
    
private:
    DataHandlerFactory() = delete; // 静态类，不允许实例化
    
    // 内部数据结构
    struct FactoryInfo {
        std::shared_ptr<IDataHandlerFactory> factory;
        std::function<std::unique_ptr<IDataHandler>()> factoryFunc;
        QString formatName;
        QStringList extensions;
        
        FactoryInfo() = default;
        FactoryInfo(std::shared_ptr<IDataHandlerFactory> f) : factory(f) {
            if (factory) {
                formatName = factory->getFormatName();
                // 从supportedFormats推导扩展名
                QStringList formats = factory->supportedFormats();
                for (const QString& format : formats) {
                    extensions.append(format.toLower());
                }
            }
        }
        
        FactoryInfo(std::function<std::unique_ptr<IDataHandler>()> f, 
                   const QString& name, const QStringList& exts) 
            : factoryFunc(f), formatName(name), extensions(exts) {}
    };
    
    static QMap<QString, FactoryInfo> s_factories;
    static bool s_initialized;
    
    // 内部工具方法
    static QString normalizeFormat(const QString& format);
    static QString extractExtension(const QString& filePath);
    static void ensureInitialized();
};

/**
 * @brief 自动注册助手类
 * 
 * 用于在程序启动时自动注册处理器工厂
 */
template<typename FactoryType>
class AutoRegisterFactory {
public:
    AutoRegisterFactory(const QString& format) {
        DataHandlerFactory::registerHandlerFactory(format, 
            std::make_shared<FactoryType>());
    }
};

// 便利宏，用于自动注册工厂
#define REGISTER_DATA_HANDLER_FACTORY(FactoryClass, format) \
    static AutoRegisterFactory<FactoryClass> g_autoRegister_##FactoryClass(format);

} // namespace SaveLoad

#endif // DATAHANDLERFACTORY_H
