#include "AlgorithmConfigManager.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QSettings>

namespace Config {

AlgorithmConfigManager::AlgorithmConfigManager(QObject *parent) : QObject(parent), m_fileWatcher(new QFileSystemWatcher(this)) {
    // 设置配置文件路径
    m_configFilePath = QApplication::applicationDirPath() + "/config/modules/lenAdjust/algorithm_config.json";

    // 连接文件监控信号
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged, this, &AlgorithmConfigManager::onFileChanged);

    // 生成默认配置
    generateDefaultConfig();

    // 自动加载配置
    loadConfig();

    logInfo("AlgorithmConfigManager initialized");
}

AlgorithmConfigManager::~AlgorithmConfigManager() {
    // 保存配置
    saveConfig();
}

ConfigResult AlgorithmConfigManager::loadConfig() {
    logInfo("Loading algorithm configuration from: " + m_configFilePath);

    if (!QFile::exists(m_configFilePath)) {
        logInfo("Config file not found, generating default configuration");
        ConfigResult result = saveConfig();  // 生成默认配置文件
        if (!result.success) {
            return result;
        }
    }

    QSettings settings(m_configFilePath, QSettings::IniFormat);

    // 清空现有参数
    m_config.parameters.clear();

    // 读取算法参数
    settings.beginGroup("ALGORITHM_PARAM");
    QStringList keys = settings.allKeys();
    for (const QString &key : keys) {
        m_config.parameters[key] = settings.value(key).toInt();
    }
    settings.endGroup();

    // 读取图像处理配置
    settings.beginGroup("IMAGE_PROCESSING");
    m_config.interpolation_type = settings.value("interpolation_type", 0).toInt();
    m_config.filter_types       = settings.value("filter_types", "1,2,3").toString();
    m_config.filter_strength    = settings.value("filter_strength", 1.0).toFloat();
    settings.endGroup();

    // 如果没有加载到参数，使用默认参数
    if (m_config.parameters.isEmpty()) {
        loadDefaultParameters();
    }

    // 验证配置
    ConfigResult validationResult = validateConfig();
    if (!validationResult.success) {
        logError("Configuration validation failed: " + validationResult.message);
        return validationResult;
    }

    // 添加文件监控
    if (!m_fileWatcher->files().contains(m_configFilePath)) {
        m_fileWatcher->addPath(m_configFilePath);
    }

    logInfo("Algorithm configuration loaded successfully");
    logInfo(QString("  Loaded %1 algorithm parameters").arg(m_config.parameters.size()));

    Q_EMIT configChanged();
    return ConfigResult(true);
}

ConfigResult AlgorithmConfigManager::saveConfig() {
    logInfo("Saving algorithm configuration to: " + m_configFilePath);

    // 确保目录存在
    QFileInfo fileInfo(m_configFilePath);
    if (!ensureDirectoryExists(fileInfo.absolutePath())) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create config directory");
    }

    QSettings settings(m_configFilePath, QSettings::IniFormat);

    // 写入算法参数
    settings.beginGroup("ALGORITHM_PARAM");
    for (auto it = m_config.parameters.begin(); it != m_config.parameters.end(); ++it) {
        settings.setValue(it.key(), it.value());
    }
    settings.endGroup();

    // 写入图像处理配置
    settings.beginGroup("IMAGE_PROCESSING");
    settings.setValue("interpolation_type", m_config.interpolation_type);
    settings.setValue("filter_types", m_config.filter_types);
    settings.setValue("filter_strength", m_config.filter_strength);
    settings.endGroup();

    settings.sync();

    logInfo("Algorithm configuration saved successfully");
    return ConfigResult(true);
}

bool AlgorithmConfigManager::isConfigValid() const {
    return validateConfig().success;
}

QString AlgorithmConfigManager::getConfigFilePath() const {
    return m_configFilePath;
}

void AlgorithmConfigManager::setConfig(const AlgorithmConfig &config) {
    m_config = config;
    Q_EMIT configChanged();
}

QVariant AlgorithmConfigManager::getParameter(const QString &key, const QVariant &defaultValue) const {
    return m_config.parameters.value(key, defaultValue.toInt());
}

void AlgorithmConfigManager::setParameter(const QString &key, const QVariant &value) {
    m_config.parameters[key] = value.toInt();
    Q_EMIT parameterChanged(key, value);
    Q_EMIT configChanged();
}

void AlgorithmConfigManager::setInterpolationType(uint8_t type) {
    m_config.interpolation_type = type;
    Q_EMIT configChanged();
}

void AlgorithmConfigManager::onFileChanged(const QString &path) {
    if (path == m_configFilePath) {
        logInfo("Configuration file changed, reloading...");
        loadConfig();
    }
}

ConfigResult AlgorithmConfigManager::validateConfig() const {
    // 验证图像处理参数
    if (m_config.interpolation_type > 3) {
        return ConfigResult(false, ErrorType::ValidationError, QString("Interpolation type out of range: %1 (valid: 0-3)").arg(m_config.interpolation_type));
    }

    if (m_config.filter_strength < 0.0 || m_config.filter_strength > 10.0) {
        return ConfigResult(false, ErrorType::ValidationError, QString("Filter strength out of range: %1 (valid: 0.0-10.0)").arg(m_config.filter_strength));
    }

    return ConfigResult(true);
}

void AlgorithmConfigManager::generateDefaultConfig() {
    // 使用默认构造函数的值
    m_config = AlgorithmConfig();

    // 加载默认算法参数
    loadDefaultParameters();

    logInfo("Generated default algorithm configuration");
}

void AlgorithmConfigManager::loadDefaultParameters() {
    // 加载默认算法参数
    m_config.parameters["initial_x_dist"]     = 0;
    m_config.parameters["initial_y_dist"]     = 0;
    m_config.parameters["initial_z_dist"]     = 0;
    m_config.parameters["find_origin_raduis"] = 140;
    m_config.parameters["find_times"]         = 4;
    m_config.parameters["peak_ok_threshold"]  = 550;
    m_config.parameters["z_move_step"]        = 3;
    m_config.parameters["xy_move_step"]       = 10;
    m_config.parameters["max_adjust_times"]   = 10;
    m_config.parameters["adjust_precision"]   = 1;

    logInfo("Loaded default algorithm parameters");
}

}  // namespace Config
