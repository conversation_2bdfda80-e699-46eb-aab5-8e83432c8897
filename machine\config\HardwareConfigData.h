#pragma once

#include "../components/configModule/ConfigTypeRegistry.h"
#include "../components/configModule/IConfigData.h"
#include <QString>
#include <QStringList>
#include <QVariantMap>

namespace Machine {

/**
 * @brief 硬件配置数据
 *
 * 管理硬件控制的所有配置参数：
 * - 轴限位参数
 * - 步进电机参数
 * - 硬件控制设置
 *
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在machine模块中定义和管理
 * - 专门用于硬件控制配置
 * - 支持参数验证和安全检查
 */
class HardwareConfigData : public Config::BaseConfigData<HardwareConfigData> {
  public:
    HardwareConfigData();
    ~HardwareConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() {
        return "Hardware";
    }

    // IConfigData接口实现
    QString getTypeName() const override {
        return staticTypeName();
    }
    QString getVersion() const override {
        return "1.0.0";
    }
    QString getDescription() const override {
        return "硬件控制配置，包含轴限位和步进电机参数";
    }

    QVariantMap toVariantMap() const override;
    bool        fromVariantMap(const QVariantMap &data) override;
    bool        validate() const override;
    void        setDefaults() override;

    QStringList getFieldNames() const override;
    QString     getFieldType(const QString &fieldName) const override;
    QString     getFieldDescription(const QString &fieldName) const override;
    bool        hasField(const QString &fieldName) const override;
    QVariant    getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool        setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool        resetField(const QString &fieldName) override;

    // 限位参数访问接口
    /**
     * @brief 获取XY轴限位半径
     * @return XY轴限位半径
     */
    uint32_t getXYRadiusLimit() const {
        return xy_radius_limit;
    }

    /**
     * @brief 设置XY轴限位半径
     * @param limit XY轴限位半径 (100-3000)
     */
    void setXYRadiusLimit(uint32_t limit) {
        xy_radius_limit = limit;
    }

    /**
     * @brief 获取Z轴限位
     * @return Z轴限位
     */
    uint32_t getZRadiusLimit() const {
        return z_radius_limit;
    }

    /**
     * @brief 设置Z轴限位
     * @param limit Z轴限位 (100-3000)
     */
    void setZRadiusLimit(uint32_t limit) {
        z_radius_limit = limit;
    }

    // 步进电机参数访问接口
    /**
     * @brief 获取X轴单脉冲移动距离
     * @return X轴单脉冲移动距离
     */
    uint8_t getXStepDist() const {
        return x_step_dist;
    }

    /**
     * @brief 设置X轴单脉冲移动距离
     * @param dist X轴单脉冲移动距离 (1-100)
     */
    void setXStepDist(uint8_t dist) {
        x_step_dist = dist;
    }

    /**
     * @brief 获取Y轴单脉冲移动距离
     * @return Y轴单脉冲移动距离
     */
    uint8_t getYStepDist() const {
        return y_step_dist;
    }

    /**
     * @brief 设置Y轴单脉冲移动距离
     * @param dist Y轴单脉冲移动距离 (1-100)
     */
    void setYStepDist(uint8_t dist) {
        y_step_dist = dist;
    }

    /**
     * @brief 获取Z轴单脉冲移动距离
     * @return Z轴单脉冲移动距离
     */
    uint8_t getZStepDist() const {
        return z_step_dist;
    }

    /**
     * @brief 设置Z轴单脉冲移动距离
     * @param dist Z轴单脉冲移动距离 (1-100)
     */
    void setZStepDist(uint8_t dist) {
        z_step_dist = dist;
    }

    // 便利方法
    /**
     * @brief 检查位置是否在XY限位内
     * @param x X坐标
     * @param y Y坐标
     * @return 是否在限位内
     */
    bool isPositionInXYLimit(int x, int y) const;

    /**
     * @brief 检查Z位置是否在限位内
     * @param z Z坐标
     * @return 是否在限位内
     */
    bool isZPositionInLimit(int z) const;

    /**
     * @brief 获取配置摘要信息
     * @return 配置摘要字符串
     */
    QString getConfigSummary() const;

    /**
     * @brief 检查配置是否为默认值
     * @return 是否为默认值
     */
    bool isDefaultConfig() const;

  public:
    // 配置数据成员
    uint32_t xy_radius_limit;  // XY轴限位半径 (100-3000)
    uint32_t z_radius_limit;   // Z轴限位 (100-3000)
    uint8_t  x_step_dist;      // X单脉冲移动距离 (1-100)
    uint8_t  y_step_dist;      // Y单脉冲移动距离 (1-100)
    uint8_t  z_step_dist;      // Z单脉冲移动距离 (1-100)

  private:
    /**
     * @brief 验证限位参数
     * @param limit 限位值
     * @return 是否有效
     */
    bool validateRadiusLimit(uint32_t limit) const;

    /**
     * @brief 验证步进距离参数
     * @param dist 步进距离
     * @return 是否有效
     */
    bool validateStepDist(uint8_t dist) const;

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;

    // 参数范围定义
    static const uint32_t MIN_RADIUS_LIMIT = 100;
    static const uint32_t MAX_RADIUS_LIMIT = 3000;
    static const uint8_t  MIN_STEP_DIST    = 1;
    static const uint8_t  MAX_STEP_DIST    = 100;
};

}  // namespace Machine

// 自动注册硬件配置类型
REGISTER_CONFIG_TYPE(Machine::HardwareConfigData, "Hardware", "1.0.0", "硬件控制配置，包含轴限位和步进电机参数")
