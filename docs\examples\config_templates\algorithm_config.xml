<?xml version="1.0" encoding="utf-8"?>
<Parameters>
  <!-- 算法参数配置文件 -->
  <!-- Algorithm Parameters Configuration File -->
  
  <!-- 调节参数 -->
  <initial_x_dist>0</initial_x_dist>
  <!-- 初始x移动距离/um -->
  <initial_y_dist>0</initial_y_dist>
  <!-- 初始y移动距离/um -->
  <initial_z_dist>0</initial_z_dist>
  <!-- 初始z移动距离/um -->
  <find_origin_raduis>140</find_origin_raduis>
  <!-- 初始寻找光斑半径/um -->
  <find_angle_step>20</find_angle_step>
  <!-- 寻找光斑角度步进/° -->
  <find_radius_step>140</find_radius_step>
  <!-- 寻找光斑半径步进/um -->
  <find_times>4</find_times>
  <!-- 寻找光斑次数 -->
  <discard_pack_num>1</discard_pack_num>
  <!-- 丢弃package数 -->
  <default_z_direct>1</default_z_direct>
  <!-- 默认Z轴移动方向: 1->坐标增大，镜片下移 -->
  <z_move_step>3</z_move_step>
  <!-- z轴移动步进(脉冲数) -->
  <peak_ok_threshold>550</peak_ok_threshold>
  <!-- 达到后不再查找继续查找 -->
  
  <!-- 对称调节参数 -->
  <Amp_select>30</Amp_select>
  <!-- 十字光斑特定通道调节 bit6-7:0-通道与中心比例, 1-对称通道比例, 2-固定值, 3-对称固定差值; L-0x01, R-0x02, U-0x04, D-0x08 -->
  <ALR_mp_peak>20</ALR_mp_peak>
  <!-- 左右通道 peak比例*100 或 固定值 或 差值 负值-> 左>右 -->
  <ALR_mp_peak_threshold>100</ALR_mp_peak_threshold>
  <!-- 左右通道容差 peak比例*100 或 固定值 或 差值 负值-> 左>右 -->
  <AUD_mp_peak>0</AUD_mp_peak>
  <!-- 上下通道 peak比例*100 或 固定值 或 差值 负值-> 上>下 -->
  <AUD_mp_peak_threshold>100</AUD_mp_peak_threshold>
  <!-- 上下通道容差 peak比例*100 或 固定值 或 差值 负值-> 上>下 -->
  
  <Aedge_peak_threshold>180</Aedge_peak_threshold>
  <!-- 光斑调节：外圈光斑光强最小阈值 -->
  <ACR_peak_delta>120</ACR_peak_delta>
  <!-- 光斑调节：中心与十字光斑光强差值最小阈值 -->
  <ARR_peak_delta>50</ARR_peak_delta>
  <!-- 光斑调节：上下左右MP peak允许偏差 -->
  <AMax_peak>650</AMax_peak>
  <!-- 虚光斑：中心光斑允许最大值 -->
  
  <!-- 判定标准 -->
  <edge_peak_threshold>50</edge_peak_threshold>
  <!-- 光斑判定：四个角最低peak阈值 -->
  <peak_threshold>600</peak_threshold>
  <!-- 光斑判定：最低peak阈值 -->
  <peak_max_threshold>1000</peak_max_threshold>
  <!-- 光斑判定：最高peak阈值 -->
  <CR_peak_delta>150</CR_peak_delta>
  <!-- 中间与周围MP光斑差值 -->
  
  <!-- 功能测试参数 -->
  <FT_LRmp_adjust_peak>25</FT_LRmp_adjust_peak>
  <!-- 调节判定：左右通道 peak比例*100 或 固定值 或 差值 负值-> 左>右 -->
  <FT_LRmp_adjust_peak_threshold>5</FT_LRmp_adjust_peak_threshold>
  <!-- 调节判定容差：左右通道 peak比例*100 或 固定值 或 差值 负值-> 左>右 -->
  <FT_UDmp_adjust_peak>0</FT_UDmp_adjust_peak>
  <!-- 调节判定：上下通道 peak比例*100 或 固定值 或 差值 负值-> 上>下 -->
  <FT_UDmp_adjust_peak_threshold>100</FT_UDmp_adjust_peak_threshold>
  <!-- 调节判定容差：上下通道 peak比例*100 或 固定值 或 差值 负值-> 上>下 -->
  
  <FT_LRmp_solid_peak>25</FT_LRmp_solid_peak>
  <!-- 固化判定：左右通道 peak比例*100 或 固定值 或 差值 负值-> 左>右 -->
  <FT_LRmp_solid_peak_threshold>8</FT_LRmp_solid_peak_threshold>
  <!-- 固化判定容差：左右通道 peak比例*100 或 固定值 或 差值 负值-> 左>右 -->
  <FT_UDmp_solid_peak>0</FT_UDmp_solid_peak>
  <!-- 固化判定：上下通道 peak比例*100 或 固定值 或 差值 负值-> 上>下 -->
  <FT_UDmp_solid_peak_threshold>100</FT_UDmp_solid_peak_threshold>
  <!-- 固化判定容差：上下通道 peak比例*100 或 固定值 或 差值 负值-> 上>下 -->
  
  <FT_LRmp_deflate_peak>25</FT_LRmp_deflate_peak>
  <!-- 放气判定：左右通道 peak比例*100 或 固定值 或 差值 负值-> 左>右 -->
  <FT_LRmp_deflate_peak_threshold>8</FT_LRmp_deflate_peak_threshold>
  <!-- 放气判定容差：左右通道 peak比例*100 或 固定值 或 差值 负值-> 左>右 -->
  <FT_UDmp_deflate_peak>0</FT_UDmp_deflate_peak>
  <!-- 放气判定：上下通道 peak比例*100 或 固定值 或 差值 负值-> 上>下 -->
  <FT_UDmp_deflate_peak_threshold>100</FT_UDmp_deflate_peak_threshold>
  <!-- 放气判定容差：上下通道 peak比例*100 或 固定值 或 差值 负值-> 上>下 -->
</Parameters>
