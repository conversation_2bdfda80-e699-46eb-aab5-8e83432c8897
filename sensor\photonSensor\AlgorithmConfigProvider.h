#pragma once

#include "../../components/config/BaseConfigProvider.h"

namespace Algorithm {

/**
 * @brief 算法配置提供者
 * 
 * 专门负责算法参数相关的配置管理
 * 配置结构完全在模块内部定义，不依赖外部配置类型
 * 
 * 职责：
 * - 管理算法计算参数
 * - 管理图像处理参数
 * - 提供算法配置的访问接口
 * - 处理算法相关的参数验证
 */
class AlgorithmConfigProvider : public Config::BaseConfigProvider {
    Q_OBJECT

public:
    explicit AlgorithmConfigProvider(QObject *parent = nullptr);
    ~AlgorithmConfigProvider() override = default;

    // IConfigProvider 接口实现
    QString getModuleName() const override { return "Algorithm"; }
    QString getModuleVersion() const override { return "1.0.0"; }
    QString getConfigFilePath() const override;

    /**
     * @brief 获取初始X距离
     * @return 初始X距离
     */
    int getInitialXDist() const;

    /**
     * @brief 设置初始X距离
     * @param dist 初始X距离
     */
    void setInitialXDist(int dist);

    /**
     * @brief 获取初始Y距离
     * @return 初始Y距离
     */
    int getInitialYDist() const;

    /**
     * @brief 设置初始Y距离
     * @param dist 初始Y距离
     */
    void setInitialYDist(int dist);

    /**
     * @brief 获取初始Z距离
     * @return 初始Z距离
     */
    int getInitialZDist() const;

    /**
     * @brief 设置初始Z距离
     * @param dist 初始Z距离
     */
    void setInitialZDist(int dist);

    /**
     * @brief 获取寻找原点半径
     * @return 寻找原点半径
     */
    int getFindOriginRadius() const;

    /**
     * @brief 设置寻找原点半径
     * @param radius 寻找原点半径
     */
    void setFindOriginRadius(int radius);

    /**
     * @brief 获取寻找次数
     * @return 寻找次数
     */
    int getFindTimes() const;

    /**
     * @brief 设置寻找次数
     * @param times 寻找次数
     */
    void setFindTimes(int times);

    /**
     * @brief 获取峰值OK阈值
     * @return 峰值OK阈值
     */
    int getPeakOkThreshold() const;

    /**
     * @brief 设置峰值OK阈值
     * @param threshold 峰值OK阈值
     */
    void setPeakOkThreshold(int threshold);

    /**
     * @brief 获取Z轴移动步长
     * @return Z轴移动步长
     */
    int getZMoveStep() const;

    /**
     * @brief 设置Z轴移动步长
     * @param step Z轴移动步长
     */
    void setZMoveStep(int step);

    /**
     * @brief 获取XY轴移动步长
     * @return XY轴移动步长
     */
    int getXYMoveStep() const;

    /**
     * @brief 设置XY轴移动步长
     * @param step XY轴移动步长
     */
    void setXYMoveStep(int step);

    /**
     * @brief 获取最大调节次数
     * @return 最大调节次数
     */
    int getMaxAdjustTimes() const;

    /**
     * @brief 设置最大调节次数
     * @param times 最大调节次数
     */
    void setMaxAdjustTimes(int times);

    /**
     * @brief 获取调节精度
     * @return 调节精度
     */
    int getAdjustPrecision() const;

    /**
     * @brief 设置调节精度
     * @param precision 调节精度
     */
    void setAdjustPrecision(int precision);

    /**
     * @brief 获取插值类型
     * @return 插值类型
     */
    int getInterpolationType() const;

    /**
     * @brief 设置插值类型
     * @param type 插值类型
     */
    void setInterpolationType(int type);

    /**
     * @brief 获取滤波器类型
     * @return 滤波器类型
     */
    int getFilterTypes() const;

    /**
     * @brief 设置滤波器类型
     * @param types 滤波器类型
     */
    void setFilterTypes(int types);

    /**
     * @brief 获取滤波器强度
     * @return 滤波器强度
     */
    int getFilterStrength() const;

    /**
     * @brief 设置滤波器强度
     * @param strength 滤波器强度
     */
    void setFilterStrength(int strength);

protected:
    // BaseConfigProvider 接口实现
    void loadDefaultParameters() override;
    Config::ConfigResult validateParameter(const QString &key, const QVariant &value) const override;
    QString getParameterRange(const QString &key) const override;
    void onParameterChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue) override;

private:
    /**
     * @brief 验证距离参数
     * @param value 参数值
     * @return 是否有效
     */
    bool isValidDistance(int value) const;

    /**
     * @brief 验证半径参数
     * @param value 参数值
     * @return 是否有效
     */
    bool isValidRadius(int value) const;

    /**
     * @brief 验证次数参数
     * @param value 参数值
     * @return 是否有效
     */
    bool isValidTimes(int value) const;

    /**
     * @brief 验证阈值参数
     * @param value 参数值
     * @return 是否有效
     */
    bool isValidThreshold(int value) const;

    /**
     * @brief 验证步长参数
     * @param value 参数值
     * @return 是否有效
     */
    bool isValidStep(int value) const;

    /**
     * @brief 验证精度参数
     * @param value 参数值
     * @return 是否有效
     */
    bool isValidPrecision(int value) const;

    /**
     * @brief 验证插值类型
     * @param value 参数值
     * @return 是否有效
     */
    bool isValidInterpolationType(int value) const;

    /**
     * @brief 验证滤波器类型
     * @param value 参数值
     * @return 是否有效
     */
    bool isValidFilterTypes(int value) const;

    /**
     * @brief 验证滤波器强度
     * @param value 参数值
     * @return 是否有效
     */
    bool isValidFilterStrength(int value) const;

private:
    // 配置参数键名常量
    static const QString KEY_INITIAL_X_DIST;
    static const QString KEY_INITIAL_Y_DIST;
    static const QString KEY_INITIAL_Z_DIST;
    static const QString KEY_FIND_ORIGIN_RADIUS;
    static const QString KEY_FIND_TIMES;
    static const QString KEY_PEAK_OK_THRESHOLD;
    static const QString KEY_Z_MOVE_STEP;
    static const QString KEY_XY_MOVE_STEP;
    static const QString KEY_MAX_ADJUST_TIMES;
    static const QString KEY_ADJUST_PRECISION;
    static const QString KEY_INTERPOLATION_TYPE;
    static const QString KEY_FILTER_TYPES;
    static const QString KEY_FILTER_STRENGTH;

    // 默认值常量
    static const int DEFAULT_INITIAL_X_DIST;
    static const int DEFAULT_INITIAL_Y_DIST;
    static const int DEFAULT_INITIAL_Z_DIST;
    static const int DEFAULT_FIND_ORIGIN_RADIUS;
    static const int DEFAULT_FIND_TIMES;
    static const int DEFAULT_PEAK_OK_THRESHOLD;
    static const int DEFAULT_Z_MOVE_STEP;
    static const int DEFAULT_XY_MOVE_STEP;
    static const int DEFAULT_MAX_ADJUST_TIMES;
    static const int DEFAULT_ADJUST_PRECISION;
    static const int DEFAULT_INTERPOLATION_TYPE;
    static const int DEFAULT_FILTER_TYPES;
    static const int DEFAULT_FILTER_STRENGTH;

    // 验证范围常量
    static const int MIN_DISTANCE;
    static const int MAX_DISTANCE;
    static const int MIN_RADIUS;
    static const int MAX_RADIUS;
    static const int MIN_TIMES;
    static const int MAX_TIMES;
    static const int MIN_THRESHOLD;
    static const int MAX_THRESHOLD;
    static const int MIN_STEP;
    static const int MAX_STEP;
    static const int MIN_PRECISION;
    static const int MAX_PRECISION;
    static const int MAX_INTERPOLATION_TYPE;
    static const int MAX_FILTER_TYPES;
    static const int MAX_FILTER_STRENGTH;
};

} // namespace Algorithm
