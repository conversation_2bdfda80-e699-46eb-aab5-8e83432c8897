#include "IConfigData.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QDebug>

namespace Config {

QString IConfigData::toJsonString() const {
    QVariantMap variantMap = toVariantMap();
    QJsonObject jsonObj = QJsonObject::fromVariantMap(variantMap);
    QJsonDocument doc(jsonObj);
    return doc.toJson(QJsonDocument::Compact);
}

bool IConfigData::fromJsonString(const QString &jsonString) {
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8(), &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        logError(QString("JSON parse error: %1").arg(parseError.errorString()));
        return false;
    }
    
    QVariantMap variantMap = doc.object().toVariantMap();
    return fromVariantMap(variantMap);
}

void IConfigData::logInfo(const QString &message) const {
    qDebug() << QString("[%1]").arg(getTypeName()) << message;
}

void IConfigData::logError(const QString &message) const {
    qCritical() << QString("[%1] ERROR:").arg(getTypeName()) << message;
}

void IConfigData::logWarning(const QString &message) const {
    qWarning() << QString("[%1] WARNING:").arg(getTypeName()) << message;
}

}  // namespace Config
