#include "ConfigManager.h"
#include <QApplication>
#include <QDir>
#include <QSettings>
#include <QXmlStreamReader>
#include <QFile>
#include <QDateTime>

namespace Config {

// 从旧配置文件迁移
ConfigResult ConfigManager::migrateFromLegacyConfig() {
    logInfo("Starting migration from legacy configuration files...");
    
    QString appDir = QApplication::applicationDirPath();
    QStringList legacyFiles;
    legacyFiles << appDir + "/config/clen_config.ini"
                << appDir + "/config/clen_config.xml"
                << appDir + "/config/clen_config_QH_SHJ.xml";
    
    bool foundLegacyConfig = false;
    for (const QString& file : legacyFiles) {
        if (QFile::exists(file)) {
            foundLegacyConfig = true;
            logInfo("Found legacy config file: " + file);
            break;
        }
    }
    
    if (!foundLegacyConfig) {
        logInfo("No legacy configuration files found, skipping migration");
        return ConfigResult(true);
    }
    
    // 备份旧配置文件
    ConfigResult backupResult = backupLegacyConfigs();
    if (!backupResult.success) {
        logError("Failed to backup legacy configs: " + backupResult.message);
        return backupResult;
    }
    
    // 迁移光斑配置
    ConfigResult faculaResult = migrateFaculaConfig();
    if (!faculaResult.success) {
        logError("Failed to migrate facula config: " + faculaResult.message);
        return faculaResult;
    }
    
    // 迁移算法配置
    ConfigResult algorithmResult = migrateAlgorithmConfig();
    if (!algorithmResult.success) {
        logError("Failed to migrate algorithm config: " + algorithmResult.message);
        return algorithmResult;
    }
    
    // 迁移硬件配置
    ConfigResult hardwareResult = migrateHardwareConfig();
    if (!hardwareResult.success) {
        logError("Failed to migrate hardware config: " + hardwareResult.message);
        return hardwareResult;
    }
    
    logInfo("Legacy configuration migration completed successfully");
    return ConfigResult(true);
}

// 备份旧配置文件
ConfigResult ConfigManager::backupLegacyConfigs() {
    QString appDir = QApplication::applicationDirPath();
    QString backupDir = appDir + "/config/backup/" + QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + "/";
    
    QDir dir;
    if (!dir.mkpath(backupDir)) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create backup directory: " + backupDir);
    }
    
    QStringList legacyFiles;
    legacyFiles << "clen_config.ini" << "clen_config.xml" << "clen_config_QH_SHJ.xml";
    
    for (const QString& fileName : legacyFiles) {
        QString sourcePath = appDir + "/config/" + fileName;
        QString backupPath = backupDir + fileName + ".bak";
        
        if (QFile::exists(sourcePath)) {
            if (!QFile::copy(sourcePath, backupPath)) {
                return ConfigResult(false, ErrorType::PermissionError, "Failed to backup file: " + sourcePath);
            }
            logInfo("Backed up legacy config: " + sourcePath + " -> " + backupPath);
        }
    }
    
    return ConfigResult(true);
}

// 迁移光斑配置
ConfigResult ConfigManager::migrateFaculaConfig() {
    QString appDir = QApplication::applicationDirPath();
    QString iniFile = appDir + "/config/clen_config.ini";
    
    if (!QFile::exists(iniFile)) {
        logInfo("No legacy facula config file found, using defaults");
        return ConfigResult(true);
    }
    
    QSettings settings(iniFile, QSettings::IniFormat);
    
    // 读取光斑中心配置
    QString channels = settings.value("ADJUST_PARAM/facula_center_channels", "2,2").toString();
    uint32_t threshold = settings.value("ADJUST_PARAM/facula_center_peak_threshold", 800).toUInt();
    
    // 读取调节参数
    uint8_t okTimes = settings.value("ADJUST_PARAM/facula_ok_time", 3).toUInt();
    uint32_t solidTime = settings.value("ADJUST_PARAM/solid_time", 0).toUInt();
    uint8_t ngHandle = settings.value("ADJUST_PARAM/facula_ng_handle", 1).toUInt();
    uint8_t handleType = settings.value("FACULA_HANDLE/facula_handle_type", 1).toUInt();
    
    // 更新配置
    m_faculaConfig.facula_center_channels = channels;
    m_faculaConfig.facula_center_peak_threshold = threshold;
    m_faculaConfig.facula_ok_times = okTimes;
    m_faculaConfig.solid_time = solidTime;
    m_faculaConfig.facula_ng_handle = ngHandle;
    m_faculaConfig.facula_handle_type = handleType;
    
    // 解析多通道配置
    m_faculaConfig.facula_center_points = parseFaculaCenterChannels(channels);
    
    // 设置兼容性字段
    if (!m_faculaConfig.facula_center_points.isEmpty()) {
        m_faculaConfig.facula_center_loc_x = static_cast<uint8_t>(m_faculaConfig.facula_center_points.first().x());
        m_faculaConfig.facula_center_loc_y = static_cast<uint8_t>(m_faculaConfig.facula_center_points.first().y());
    }
    
    // 保存到新的JSON格式
    ConfigResult result = saveModuleConfig("lenAdjust", FileType::Facula);
    if (!result.success) {
        return result;
    }
    
    logInfo("Migrated facula configuration from legacy INI file");
    return ConfigResult(true);
}

// 迁移算法配置
ConfigResult ConfigManager::migrateAlgorithmConfig() {
    QString appDir = QApplication::applicationDirPath();
    QString xmlFile = appDir + "/config/clen_config.xml";
    
    if (!QFile::exists(xmlFile)) {
        logInfo("No legacy algorithm config file found, using defaults");
        return ConfigResult(true);
    }
    
    QFile file(xmlFile);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return ConfigResult(false, ErrorType::FileNotFound, "Cannot open legacy XML file: " + xmlFile);
    }
    
    QXmlStreamReader xmlReader(&file);
    m_algorithmConfig.parameters.clear();
    
    while (!xmlReader.atEnd() && !xmlReader.hasError()) {
        QXmlStreamReader::TokenType token = xmlReader.readNext();
        
        if (token == QXmlStreamReader::StartElement) {
            if (xmlReader.name() != "Parameters") {
                QString elementName = xmlReader.name().toString();
                QString elementValue = xmlReader.readElementText();
                
                bool ok;
                int value = elementValue.toInt(&ok);
                if (ok) {
                    m_algorithmConfig.parameters[elementName] = value;
                }
            }
        }
    }
    
    file.close();
    
    if (xmlReader.hasError()) {
        return ConfigResult(false, ErrorType::ParseError, "XML parsing error: " + xmlReader.errorString());
    }
    
    // 保存到新的JSON格式
    ConfigResult result = saveModuleConfig("lenAdjust", FileType::Algorithm);
    if (!result.success) {
        return result;
    }
    
    logInfo("Migrated algorithm configuration from legacy XML file");
    return ConfigResult(true);
}

// 迁移硬件配置
ConfigResult ConfigManager::migrateHardwareConfig() {
    // 硬件配置通常在代码中硬编码，这里使用默认值
    // 如果有特定的硬件配置文件，可以在这里添加迁移逻辑
    
    // 保存默认硬件配置到新的JSON格式
    ConfigResult result = saveModuleConfig("lenAdjust", FileType::Hardware);
    if (!result.success) {
        return result;
    }
    
    logInfo("Generated default hardware configuration");
    return ConfigResult(true);
}

} // namespace Config
