#ifndef _FACULA_CONTEXT_H_
#define _FACULA_CONTEXT_H_

#include "../../components/lensAdjust/lensReadIni.h"
#include "interpolation.h"
#include "sensorBoardFactory.h"
#include <memory>
#include <vector>

// 包含ProcessingConfig定义
#include "faculaProcessingConfig.h"

// 包含新的配置提供者
#include "AlgorithmConfigProvider.h"
#include "FaculaConfigProvider.h"


// 前向声明，避免在头文件中包含ImageProcessing头文件
namespace ImageProcessing {
class IInterpolation;
class IImageFilter;
}  // namespace ImageProcessing


class CFaculaContext {
  public:
    CFaculaContext(const ISensorBoardFactory::StFaculaSensorInfo &facula_config);
    ~CFaculaContext();

    //    static CFaculaContext& getInstance(const ISensorBoardFactory::StFaculaSensorInfo &facula_config) {
    //        static CFaculaContext instance(facula_config);
    //        return instance;
    //    }

    void                     varibleInit(void);
    IFaculaAdjust::StMapInfo getExpandMapInfo();
    IFaculaAdjust::StMapData getOriginMapData();
    IFaculaAdjust::StMapData getExpandMapData();
    //    void targetFaculaAreaShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_);
    //    void greyMapShow(QTableWidget *map, IFaculaAdjust::StMapInfo *map_info_, const IFaculaAdjust::StMapData &map_data);

    bool faculaHandle(uint16_t &                           adjust_status,
                      QByteArray &                         facula_origin_data,
                      const C3dHandMachine::St3D<int16_t> &move_delta_step,
                      C3dHandMachine::St3D<int16_t> *      move_dis_);

    //    void targetMapUpdate(const IFaculaAdjust::StMapTargetInfo &target_map);

    bool faculaTest(QByteArray &                           facula_origin_data,
                    uint16_t &                             adjust_status,
                    QVector<uint32_t> &                    target_facula,
                    const IFaculaAdjust::EFaculaJudgeMode &test_mode,
                    const bool &                           is_auto_adjust);
    bool localFaculaTest(const QVector<QVector<uint32_t>> &     map_matrix,
                         uint16_t &                             adjust_status,
                         QVector<uint32_t> &                    target_facula,
                         const IFaculaAdjust::EFaculaJudgeMode &test_mode);

    QVector<QVector<uint32_t>> getMap(void);
    void                       mergeDataClean();

  private:
    typedef struct {
        uint8_t           merge_data_cnt;
        QVector<uint32_t> map_data_cache;
    } StMergeData;

    ISensorBoardFactory::StFaculaSensorInfo mst_facula_sensor_info;
    IFaculaAdjust *                         mi_facula_ = nullptr;

    //*
    StMergeData      mst_merge_data;
    my_interPolation m_my_interPolation;  // 保留作为备选

    IFaculaAdjust::StMapData m_map_data;
    IFaculaAdjust::StMapData m_map_interpolation_data;

    IFaculaAdjust::StMapInfo mst_map_info;
    // IFaculaAdjust::StMapInfo mst_interpolation_map_info;

    // 新增的图像处理组件
    std::unique_ptr<ImageProcessing::IInterpolation>            m_interpolator;
    std::vector<std::unique_ptr<ImageProcessing::IImageFilter>> m_filters;
    ProcessingConfig                                            m_processing_config;  // 处理配置

    // 配置提供者
    std::unique_ptr<Algorithm::AlgorithmConfigProvider> m_algorithmConfigProvider;
    std::unique_ptr<Facula::FaculaConfigProvider>       m_faculaConfigProvider;

    bool targetFaculaArea(void);

    bool              originDataHanlde(QByteArray &mp_origin_bytes);
    QVector<uint32_t> dataMerge(const QVector<uint32_t> &origin_data, const uint8_t &times, const uint8_t &types);
    bool              mapDataMerge(QByteArray &mp_origin_byte, StMergeData *merge_data_, QVector<uint32_t> *map_data_);
    void              externalMapInfoUpdate();

    // 新增的处理方法
    void loadProcessingConfig();
    void initializeProcessingAlgorithms(const ProcessingConfig &config);
    bool processWithNewAlgorithms();
    bool processWithLegacyAlgorithms();
    void logProcessingInfo(const QString &message);
};

#endif
