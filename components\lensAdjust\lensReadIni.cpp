#include "lensReadIni.h"
#include "ConfigManager.h"
#include <QDebug>


ClensReadIni *ClensReadIni::instance = NULL;


ClensReadIni *ClensReadIni::getInstance() {
    if (instance == NULL) {
        instance = new ClensReadIni();
    }
    return instance;
}


ClensReadIni::ClensReadIni() {
    this->readIni();
}


void ClensReadIni::readIni() {
    // 使用新的配置管理器
    Config::ConfigManager *configManager = Config::ConfigManager::getInstance();

    // 加载所有配置（包括自动迁移）
    Config::ConfigResult result = configManager->loadAllConfigs();
    if (!result.success) {
        qDebug() << "[ERROR] Failed to load configurations:" << result.message;
        // 使用默认配置继续运行
    }

    // 获取系统配置
    const auto &systemConfig      = configManager->getSystemConfig();
    iniConfig.version             = systemConfig.version;
    iniConfig.userid              = "U" + systemConfig.userid;  // 保持原有的"U"前缀
    iniConfig.op                  = systemConfig.op;
    iniConfig.work_number         = systemConfig.work_number;
    iniConfig.work_domain         = systemConfig.work_domain;
    iniConfig.sensor_device       = systemConfig.sensor_device;
    iniConfig.sensor_device_buad  = systemConfig.sensor_device_baud;
    iniConfig.station_number      = systemConfig.station_number;
    iniConfig.clens_machine_brand = systemConfig.clens_machine_brand;

    // 获取光斑配置
    const auto &faculaConfig               = configManager->getFaculaConfig();
    iniConfig.facula_center_channels       = faculaConfig.facula_center_channels;
    iniConfig.facula_center_peak_threshold = faculaConfig.facula_center_peak_threshold;
    iniConfig.facula_center_points         = faculaConfig.facula_center_points;
    iniConfig.facula_center_loc_x          = faculaConfig.facula_center_loc_x;
    iniConfig.facula_center_loc_y          = faculaConfig.facula_center_loc_y;
    iniConfig.facula_ok_times              = faculaConfig.facula_ok_times;
    iniConfig.solid_time                   = faculaConfig.solid_time;
    iniConfig.facula_ng_handle             = faculaConfig.facula_ng_handle;
    iniConfig.facula_handle_type           = faculaConfig.facula_handle_type;

    // 记录配置加载日志
    qDebug() << "[INFO] Loaded configuration - Version:" << iniConfig.version << ", User:" << iniConfig.userid << ", Device:" << iniConfig.sensor_device
             << ", Machine:" << iniConfig.clens_machine_brand;

    qDebug() << "[INFO] Facula config - Channels:" << iniConfig.facula_center_channels << ", Threshold:" << iniConfig.facula_center_peak_threshold
             << ", Handle Type:" << iniConfig.facula_handle_type;

    // 记录详细的配置加载日志
    qDebug() << "[lensReadIni] ========== Configuration Loaded Successfully ==========";
    qDebug() << "[lensReadIni] System Configuration:";
    qDebug() << "  Version:" << iniConfig.version;
    qDebug() << "  User ID:" << iniConfig.userid;
    qDebug() << "  Operation:" << iniConfig.op;
    qDebug() << "  Work Number:" << iniConfig.work_number;
    qDebug() << "  Work Domain:" << iniConfig.work_domain;
    qDebug() << "  Sensor Device:" << iniConfig.sensor_device;
    qDebug() << "  Device Baud Rate:" << iniConfig.sensor_device_buad;
    qDebug() << "  Station Number:" << iniConfig.station_number;
    qDebug() << "  Machine Brand:" << iniConfig.clens_machine_brand;

    qDebug() << "[lensReadIni] Facula Configuration:";
    qDebug() << "  Center Channels:" << iniConfig.facula_center_channels;
    qDebug() << "  Peak Threshold:" << iniConfig.facula_center_peak_threshold;
    qDebug() << "  Center Points Count:" << iniConfig.facula_center_points.size();
    for (int i = 0; i < iniConfig.facula_center_points.size(); ++i) {
        const QPoint &point = iniConfig.facula_center_points[i];
        qDebug() << QString("    Point %1: (%2,%3)").arg(i + 1).arg(point.x()).arg(point.y());
    }
    qDebug() << "  Primary Center:" << QString("(%1,%2)").arg(iniConfig.facula_center_loc_x).arg(iniConfig.facula_center_loc_y);
    qDebug() << "  Handle Type:" << iniConfig.facula_handle_type;
    qDebug() << "  OK Times:" << iniConfig.facula_ok_times;
    qDebug() << "  Solid Time:" << iniConfig.solid_time;
    qDebug() << "  NG Handle:" << iniConfig.facula_ng_handle;
    qDebug() << "[lensReadIni] ==================================================";
}


const ClensReadIni::IniConfig &ClensReadIni::getIniConfig() {
    return iniConfig;
}

/**
 * @brief 解析多通道光斑中心配置字符串
 * @param channelsStr 配置字符串，格式："x1,y1;x2,y2;x3,y3"
 * @return 解析后的通道坐标列表
 */
QVector<QPoint> ClensReadIni::parseFaculaCenterChannels(const QString &channelsStr) {
    QVector<QPoint> points;

    if (channelsStr.isEmpty()) {
        return points;
    }

    // 按分号分割各个通道
    QStringList channels = channelsStr.split(';', Qt::SkipEmptyParts);

    for (const QString &channel : channels) {
        // 按逗号分割x,y坐标
        QStringList coords = channel.split(',', Qt::SkipEmptyParts);

        if (coords.size() == 2) {
            bool xOk, yOk;
            int  x = coords[0].trimmed().toInt(&xOk);
            int  y = coords[1].trimmed().toInt(&yOk);

            if (xOk && yOk) {
                QPoint point(x, y);
                if (isValidChannelPoint(point)) {
                    points.append(point);
                } else {
                    qWarning() << "Invalid channel point:" << point << "- coordinates out of range";
                }
            } else {
                qWarning() << "Invalid channel format:" << channel << "- coordinates must be integers";
            }
        } else {
            qWarning() << "Invalid channel format:" << channel << "- expected format: x,y";
        }
    }

    if (points.isEmpty()) {
        qWarning() << "No valid channels found in configuration:" << channelsStr;
    } else {
        qDebug() << "Parsed" << points.size() << "facula center channels:" << points;
    }

    return points;
}

/**
 * @brief 验证通道坐标是否有效
 * @param point 通道坐标点
 * @param maxX 最大X坐标值
 * @param maxY 最大Y坐标值
 * @return 坐标是否有效
 */
bool ClensReadIni::isValidChannelPoint(const QPoint &point, uint8_t maxX, uint8_t maxY) {
    return (point.x() >= 0 && point.x() <= maxX && point.y() >= 0 && point.y() <= maxY);
}
