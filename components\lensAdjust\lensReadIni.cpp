#include "lensReadIni.h"
#include "ConfigService.h"
#include "DynamicConfigManager.h"
#include "config/AdjustProcessConfigData.h"
#include <QDebug>


ClensReadIni *ClensReadIni::instance = NULL;


ClensReadIni *ClensReadIni::getInstance() {
    if (instance == NULL) {
        instance = new ClensReadIni();
    }
    return instance;
}


ClensReadIni::ClensReadIni() {
    this->readIni();
}


void ClensReadIni::readIni() {
    // 使用新的配置服务
    Config::ConfigService &       configService  = Config::ConfigService::getInstance();
    Config::DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 加载所有配置
    Config::ConfigResult result = dynamicManager.loadAllConfigs();
    if (!result.success) {
        qDebug() << "[ERROR] Failed to load configurations:" << result.message;
        // 使用默认配置继续运行
    }

    // 获取系统配置（使用动态配置）
    auto *systemConfig = dynamicManager.getConfig<Config::SystemConfigData>("System");
    if (systemConfig) {
        iniConfig.version             = systemConfig->getVersion();
        iniConfig.userid              = "U" + systemConfig->getUserId();  // 保持原有的"U"前缀
        iniConfig.op                  = systemConfig->getOp();
        iniConfig.work_number         = systemConfig->getWorkNumber();
        iniConfig.work_domain         = systemConfig->getWorkDomain();
        iniConfig.sensor_device       = systemConfig->getSensorDevice();
        iniConfig.sensor_device_buad  = systemConfig->getSensorDeviceBaud();
        iniConfig.station_number      = systemConfig->getStationNumber();
        iniConfig.clens_machine_brand = systemConfig->getClensMachineBrand();
    } else {
        qDebug() << "[WARNING] System config not available, using defaults";
        iniConfig.version             = "1.0.0";
        iniConfig.userid              = "Uadmin";
        iniConfig.op                  = "105";
        iniConfig.work_number         = "10001";
        iniConfig.work_domain         = "001";
        iniConfig.sensor_device       = 111;
        iniConfig.sensor_device_buad  = 6553650;
        iniConfig.station_number      = 1;
        iniConfig.clens_machine_brand = 77;
    }

    // 获取光斑配置（使用动态配置）
    auto *faculaConfig = dynamicManager.getConfig<LensAdjust::FaculaConfigData>("Facula");
    if (faculaConfig) {
        iniConfig.facula_center_channels       = faculaConfig->getFaculaCenterChannels();
        iniConfig.facula_center_peak_threshold = faculaConfig->getFaculaCenterPeakThreshold();
        iniConfig.facula_center_points         = faculaConfig->getFaculaCenterPoints();
        iniConfig.facula_center_loc_x          = faculaConfig->getFaculaCenterLocX();
        iniConfig.facula_center_loc_y          = faculaConfig->getFaculaCenterLocY();
        iniConfig.facula_handle_type           = faculaConfig->getFaculaHandleType();
    } else {
        qDebug() << "[WARNING] Facula config not available, using defaults";
        iniConfig.facula_center_channels       = "320,240";
        iniConfig.facula_center_peak_threshold = 600;
        iniConfig.facula_center_loc_x          = 320;
        iniConfig.facula_center_loc_y          = 240;
        iniConfig.facula_handle_type           = 1;
    }
    // 从新配置系统读取调节流程参数
    auto *adjustConfig = dynamicManager.getConfig<LensAdjust::AdjustProcessConfigData>("AdjustProcess");
    if (adjustConfig) {
        iniConfig.facula_ok_times  = adjustConfig->getFaculaOkTimes();
        iniConfig.solid_time       = adjustConfig->getSolidTime();
        iniConfig.facula_ng_handle = adjustConfig->getFaculaNgHandle();
        qDebug() << "[lensReadIni] 从新配置系统读取调节流程参数";
    } else {
        // 使用默认值
        iniConfig.facula_ok_times  = 3;
        iniConfig.solid_time       = 0;
        iniConfig.facula_ng_handle = 1;
        qDebug() << "[lensReadIni] 新配置系统不可用，使用默认调节流程参数";
    }

    // 记录配置加载日志
    qDebug() << "[INFO] Loaded configuration - Version:" << iniConfig.version << ", User:" << iniConfig.userid << ", Device:" << iniConfig.sensor_device
             << ", Machine:" << iniConfig.clens_machine_brand;

    qDebug() << "[INFO] Facula config - Channels:" << iniConfig.facula_center_channels << ", Threshold:" << iniConfig.facula_center_peak_threshold
             << ", Handle Type:" << iniConfig.facula_handle_type;

    // 记录详细的配置加载日志
    qDebug() << "[lensReadIni] ========== Configuration Loaded Successfully ==========";
    qDebug() << "[lensReadIni] System Configuration:";
    qDebug() << "  Version:" << iniConfig.version;
    qDebug() << "  User ID:" << iniConfig.userid;
    qDebug() << "  Operation:" << iniConfig.op;
    qDebug() << "  Work Number:" << iniConfig.work_number;
    qDebug() << "  Work Domain:" << iniConfig.work_domain;
    qDebug() << "  Sensor Device:" << iniConfig.sensor_device;
    qDebug() << "  Device Baud Rate:" << iniConfig.sensor_device_buad;
    qDebug() << "  Station Number:" << iniConfig.station_number;
    qDebug() << "  Machine Brand:" << iniConfig.clens_machine_brand;

    qDebug() << "[lensReadIni] Facula Configuration:";
    qDebug() << "  Center Channels:" << iniConfig.facula_center_channels;
    qDebug() << "  Peak Threshold:" << iniConfig.facula_center_peak_threshold;
    qDebug() << "  Center Points Count:" << iniConfig.facula_center_points.size();
    for (int i = 0; i < iniConfig.facula_center_points.size(); ++i) {
        const QPoint &point = iniConfig.facula_center_points[i];
        qDebug() << QString("    Point %1: (%2,%3)").arg(i + 1).arg(point.x()).arg(point.y());
    }
    qDebug() << "  Primary Center:" << QString("(%1,%2)").arg(iniConfig.facula_center_loc_x).arg(iniConfig.facula_center_loc_y);
    qDebug() << "  Handle Type:" << iniConfig.facula_handle_type;
    qDebug() << "  OK Times:" << iniConfig.facula_ok_times;
    qDebug() << "  Solid Time:" << iniConfig.solid_time;
    qDebug() << "  NG Handle:" << iniConfig.facula_ng_handle;
    qDebug() << "[lensReadIni] ==================================================";
}


const ClensReadIni::IniConfig &ClensReadIni::getIniConfig() {
    return iniConfig;
}

/**
 * @brief 解析多通道光斑中心配置字符串
 * @param channelsStr 配置字符串，格式："x1,y1;x2,y2;x3,y3"
 * @return 解析后的通道坐标列表
 */
QVector<QPoint> ClensReadIni::parseFaculaCenterChannels(const QString &channelsStr) {
    QVector<QPoint> points;

    if (channelsStr.isEmpty()) {
        return points;
    }

    // 按分号分割各个通道
    QStringList channels = channelsStr.split(';', Qt::SkipEmptyParts);

    for (const QString &channel : channels) {
        // 按逗号分割x,y坐标
        QStringList coords = channel.split(',', Qt::SkipEmptyParts);

        if (coords.size() == 2) {
            bool xOk, yOk;
            int  x = coords[0].trimmed().toInt(&xOk);
            int  y = coords[1].trimmed().toInt(&yOk);

            if (xOk && yOk) {
                QPoint point(x, y);
                if (isValidChannelPoint(point)) {
                    points.append(point);
                } else {
                    qWarning() << "Invalid channel point:" << point << "- coordinates out of range";
                }
            } else {
                qWarning() << "Invalid channel format:" << channel << "- coordinates must be integers";
            }
        } else {
            qWarning() << "Invalid channel format:" << channel << "- expected format: x,y";
        }
    }

    if (points.isEmpty()) {
        qWarning() << "No valid channels found in configuration:" << channelsStr;
    } else {
        qDebug() << "Parsed" << points.size() << "facula center channels:" << points;
    }

    return points;
}

/**
 * @brief 验证通道坐标是否有效
 * @param point 通道坐标点
 * @param maxX 最大X坐标值
 * @param maxY 最大Y坐标值
 * @return 坐标是否有效
 */
bool ClensReadIni::isValidChannelPoint(const QPoint &point, uint8_t maxX, uint8_t maxY) {
    return (point.x() >= 0 && point.x() <= maxX && point.y() >= 0 && point.y() <= maxY);
}
