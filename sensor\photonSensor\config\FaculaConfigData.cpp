#include "FaculaConfigData.h"
#include <QDebug>
#include <QStringList>
#include <QRegularExpression>

namespace PhotonSensor {

// 静态成员初始化
const QMap<QString, QString> FaculaConfigData::s_fieldTypes = {
    {"facula_center_channels", "QString"},
    {"facula_center_peak_threshold", "uint32_t"},
    {"facula_center_loc_x", "uint8_t"},
    {"facula_center_loc_y", "uint8_t"},
    {"facula_handle_type", "uint8_t"}
};

const QMap<QString, QString> FaculaConfigData::s_fieldDescriptions = {
    {"facula_center_channels", "多通道配置字符串，格式：x1,y1;x2,y2;..."},
    {"facula_center_peak_threshold", "多通道模式下的peak阈值，范围100-2000"},
    {"facula_center_loc_x", "单点X坐标，兼容性参数，范围0-255"},
    {"facula_center_loc_y", "单点Y坐标，兼容性参数，范围0-255"},
    {"facula_handle_type", "光斑处理类型：0-基础，1-增强，2-高精度"}
};

const QStringList FaculaConfigData::s_handleTypeDescriptions = {
    "基础检测算法",
    "增强型检测算法",
    "高精度检测算法"
};

FaculaConfigData::FaculaConfigData()
    : facula_center_channels("2,2")
    , facula_center_peak_threshold(800)
    , facula_center_loc_x(2)
    , facula_center_loc_y(2)
    , facula_handle_type(1)
{
    setDefaults();
}

QVariantMap FaculaConfigData::toVariantMap() const {
    QVariantMap map;
    
    // 基本配置
    map["facula_center_channels"] = facula_center_channels;
    map["facula_center_peak_threshold"] = facula_center_peak_threshold;
    map["facula_center_loc_x"] = facula_center_loc_x;
    map["facula_center_loc_y"] = facula_center_loc_y;
    map["facula_handle_type"] = facula_handle_type;
    
    // 添加元信息
    map["_type"] = getTypeName();
    map["_version"] = getVersion();
    
    return map;
}

bool FaculaConfigData::fromVariantMap(const QVariantMap &data) {
    try {
        // 加载基本配置
        if (data.contains("facula_center_channels")) {
            setFaculaCenterChannels(data["facula_center_channels"].toString());
        }
        
        if (data.contains("facula_center_peak_threshold")) {
            facula_center_peak_threshold = data["facula_center_peak_threshold"].toUInt();
        }
        
        if (data.contains("facula_center_loc_x")) {
            facula_center_loc_x = data["facula_center_loc_x"].toUInt();
        }
        
        if (data.contains("facula_center_loc_y")) {
            facula_center_loc_y = data["facula_center_loc_y"].toUInt();
        }
        
        if (data.contains("facula_handle_type")) {
            facula_handle_type = data["facula_handle_type"].toUInt();
        }
        
        return true;
    } catch (const std::exception &e) {
        logError(QString("Failed to load from variant map: %1").arg(e.what()));
        return false;
    } catch (...) {
        logError("Unknown error loading from variant map");
        return false;
    }
}

bool FaculaConfigData::validate() const {
    // 验证通道配置字符串
    if (!validateChannelString(facula_center_channels)) {
        return false;
    }
    
    // 验证peak阈值
    if (!validatePeakThreshold(facula_center_peak_threshold)) {
        return false;
    }
    
    // 验证处理类型
    if (!validateHandleType(facula_handle_type)) {
        return false;
    }
    
    // 验证兼容性坐标
    if (facula_center_loc_x > MAX_COORDINATE || facula_center_loc_y > MAX_COORDINATE) {
        logError(QString("Invalid compatibility coordinates: (%1, %2)")
                .arg(facula_center_loc_x).arg(facula_center_loc_y));
        return false;
    }
    
    return true;
}

void FaculaConfigData::setDefaults() {
    facula_center_channels = "2,2";
    facula_center_peak_threshold = 800;
    facula_center_loc_x = 2;
    facula_center_loc_y = 2;
    facula_handle_type = 1;
    
    // 初始化默认通道点
    facula_center_points.clear();
    facula_center_points.append(QPoint(2, 2));
    
    logInfo("Set facula config to default values");
}

QStringList FaculaConfigData::getFieldNames() const {
    return s_fieldTypes.keys();
}

QString FaculaConfigData::getFieldType(const QString &fieldName) const {
    return s_fieldTypes.value(fieldName, "unknown");
}

QString FaculaConfigData::getFieldDescription(const QString &fieldName) const {
    return s_fieldDescriptions.value(fieldName, "No description available");
}

bool FaculaConfigData::hasField(const QString &fieldName) const {
    return s_fieldTypes.contains(fieldName);
}

QVariant FaculaConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    if (fieldName == "facula_center_channels") {
        return facula_center_channels;
    } else if (fieldName == "facula_center_peak_threshold") {
        return facula_center_peak_threshold;
    } else if (fieldName == "facula_center_loc_x") {
        return facula_center_loc_x;
    } else if (fieldName == "facula_center_loc_y") {
        return facula_center_loc_y;
    } else if (fieldName == "facula_handle_type") {
        return facula_handle_type;
    }
    
    return defaultValue;
}

bool FaculaConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    if (fieldName == "facula_center_channels") {
        setFaculaCenterChannels(value.toString());
        return true;
    } else if (fieldName == "facula_center_peak_threshold") {
        facula_center_peak_threshold = value.toUInt();
        return true;
    } else if (fieldName == "facula_center_loc_x") {
        facula_center_loc_x = value.toUInt();
        return true;
    } else if (fieldName == "facula_center_loc_y") {
        facula_center_loc_y = value.toUInt();
        return true;
    } else if (fieldName == "facula_handle_type") {
        facula_handle_type = value.toUInt();
        return true;
    }
    
    return false;
}

bool FaculaConfigData::resetField(const QString &fieldName) {
    if (fieldName == "facula_center_channels") {
        facula_center_channels = "2,2";
        syncChannelData();
        return true;
    } else if (fieldName == "facula_center_peak_threshold") {
        facula_center_peak_threshold = 800;
        return true;
    } else if (fieldName == "facula_center_loc_x") {
        facula_center_loc_x = 2;
        return true;
    } else if (fieldName == "facula_center_loc_y") {
        facula_center_loc_y = 2;
        return true;
    } else if (fieldName == "facula_handle_type") {
        facula_handle_type = 1;
        return true;
    }
    
    return false;
}

void FaculaConfigData::setFaculaCenterChannels(const QString &channels) {
    facula_center_channels = channels;
    syncChannelData();
}

void FaculaConfigData::setFaculaCenterPoints(const QVector<QPoint> &points) {
    facula_center_points = points;
    facula_center_channels = pointsToChannelString(points);
}

QString FaculaConfigData::getFaculaHandleTypeDescription() const {
    if (facula_handle_type < s_handleTypeDescriptions.size()) {
        return s_handleTypeDescriptions[facula_handle_type];
    }
    return "未知处理类型";
}

QVector<QPoint> FaculaConfigData::parseChannelString(const QString &channels) {
    QVector<QPoint> points;
    
    if (channels.isEmpty()) {
        return points;
    }
    
    // 支持两种格式：
    // 1. 简单格式："2,2" -> QPoint(2, 2)
    // 2. 多通道格式："2,2;3,3;4,4" -> [QPoint(2,2), QPoint(3,3), QPoint(4,4)]
    
    QStringList channelList = channels.split(';', Qt::SkipEmptyParts);
    
    for (const QString &channel : channelList) {
        QStringList coords = channel.split(',', Qt::SkipEmptyParts);
        if (coords.size() == 2) {
            bool xOk, yOk;
            int x = coords[0].trimmed().toInt(&xOk);
            int y = coords[1].trimmed().toInt(&yOk);
            
            if (xOk && yOk && x >= 0 && x <= 255 && y >= 0 && y <= 255) {
                points.append(QPoint(x, y));
            }
        }
    }
    
    return points;
}

QString FaculaConfigData::pointsToChannelString(const QVector<QPoint> &points) {
    QStringList channelList;
    
    for (const QPoint &point : points) {
        channelList.append(QString("%1,%2").arg(point.x()).arg(point.y()));
    }
    
    return channelList.join(';');
}

void FaculaConfigData::addChannelPoint(const QPoint &point) {
    facula_center_points.append(point);
    facula_center_channels = pointsToChannelString(facula_center_points);
}

bool FaculaConfigData::removeChannelPoint(int index) {
    if (index >= 0 && index < facula_center_points.size()) {
        facula_center_points.removeAt(index);
        facula_center_channels = pointsToChannelString(facula_center_points);
        return true;
    }
    return false;
}

void FaculaConfigData::clearChannelPoints() {
    facula_center_points.clear();
    facula_center_channels.clear();
}

bool FaculaConfigData::isDefaultConfig() const {
    return facula_center_channels == "2,2" &&
           facula_center_peak_threshold == 800 &&
           facula_center_loc_x == 2 &&
           facula_center_loc_y == 2 &&
           facula_handle_type == 1;
}

QString FaculaConfigData::getConfigSummary() const {
    return QString("光斑配置: 通道=%1, 阈值=%2, 类型=%3")
           .arg(facula_center_channels)
           .arg(facula_center_peak_threshold)
           .arg(getFaculaHandleTypeDescription());
}

bool FaculaConfigData::validateChannelString(const QString &channels) const {
    if (channels.isEmpty()) {
        logError("Channel string cannot be empty");
        return false;
    }
    
    QVector<QPoint> points = parseChannelString(channels);
    if (points.isEmpty()) {
        logError(QString("Invalid channel string format: %1").arg(channels));
        return false;
    }
    
    return true;
}

bool FaculaConfigData::validatePeakThreshold(uint32_t threshold) const {
    if (threshold < MIN_PEAK_THRESHOLD || threshold > MAX_PEAK_THRESHOLD) {
        logError(QString("Peak threshold %1 out of range [%2, %3]")
                .arg(threshold).arg(MIN_PEAK_THRESHOLD).arg(MAX_PEAK_THRESHOLD));
        return false;
    }
    return true;
}

bool FaculaConfigData::validateHandleType(uint8_t type) const {
    if (type > MAX_HANDLE_TYPE) {
        logError(QString("Handle type %1 out of range [0, %2]")
                .arg(type).arg(MAX_HANDLE_TYPE));
        return false;
    }
    return true;
}

void FaculaConfigData::syncChannelData() {
    facula_center_points = parseChannelString(facula_center_channels);
}

}  // namespace PhotonSensor
