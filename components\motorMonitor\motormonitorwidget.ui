<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>motorMonitorWidget</class>
 <widget class="QDockWidget" name="motorMonitorWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1138</width>
    <height>765</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icon/motor-1.png</normaloff>:/icon/motor-1.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">QDockWidget
{
	background-color: rgb(255,255,255);
	
	font: 12pt &quot;黑体&quot;;
	color: rgb(25, 25, 25);

	border: 2px solid gray;
	border-radius: 6px;
}
QComboBox{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton{
	min-width: 81px;
	min-height: 35px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/ 
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
QPushButton:hover {
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}	
QPushButton:selected {
	border-left: 2px solid gray;
		
    /*border-bottom-color:#C2C7CB;*/
}
QLineEdit
{
	min-width: 150px;
	min-height: 30px;

	background-color: rgb(255,255,255); /*white*/
	
	color: rgb(25, 25, 25); /*浅黑*/
	text-align: right;
	
	border: 2px solid black; /**/
	border-radius: 6px;	 
}
#QLineEdit:hover {
	
	background-color: rgb(25, 25, 25);
	color: rgb(255,255,255);

	border: 2px solid white; /**/
	border-radius: 6px;	
}
QLabel{
	font: 10pt  &quot;黑体&quot;;
	/*font-weight: bold;*/
}
QCustomPlot{
	/*min-width: 1200px;
	min-height: 1000px;*/
}
#monitorResult{
	background-color: rgb(255, 255, 255);
	color: rgb(25, 25, 25); /*浅黑*/

	/*min-width: 230px;
	min-height:150px;*/

	text-align: left top;
    padding-left: 60px;
    padding-top: 2px;
	font: 30pt &quot;黑体&quot;;
}

#tabWidget {
	min-height: 250px;	
}

#offsetLabel {
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}

#label_12{
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}
#standardLabel{
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}
#offset1{
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}
#offset2{
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}
#offset3{
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}

#mean{
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}

#standardRange {
	min-width: 140px;
	min-height: 28px;	
}

#rpsMean {
	min-width: 140px;
	min-height: 28px;
}

#indicator_one {
	min-width: 140px;
	min-height: 28px;
}
#indicator_two {
	min-width: 140px;
	min-height: 28px;
}

#indicator_three {
	min-width: 140px;
	min-height: 28px;
}

#standIntervalLabel {
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;	
}

#label_2 {
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}
#label_3 {
	font: 8pt  &quot;黑体&quot;;
	font-weight: bold;
}

#standInterval {
	min-width: 120px;
	msx-width: 120px;
	min-height: 28px;
	max-height: 28px;	
}

#sigmaMean {
	min-width: 120px;
	min-height: 28px;
}

#sigma {
	min-width: 120px;
	min-height: 28px;
}

</string>
  </property>
  <property name="windowTitle">
   <string>MotorSpeed_V1.7</string>
  </property>
  <widget class="QWidget" name="dockWidgetContents">
   <layout class="QGridLayout" name="gridLayout_4">
    <item row="0" column="0" colspan="2">
     <layout class="QGridLayout" name="gridLayout_2" columnstretch="0,0">
      <property name="spacing">
       <number>6</number>
      </property>
      <item row="0" column="1">
       <widget class="QComboBox" name="portBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_4">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>端口：</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="0" column="2" rowspan="8">
     <widget class="QCustomPlot" name="motorPlot" native="true">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>800</width>
        <height>0</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei</family>
        <pointsize>10</pointsize>
       </font>
      </property>
      <property name="cursor">
       <cursorShape>PointingHandCursor</cursorShape>
      </property>
      <property name="focusPolicy">
       <enum>Qt::ClickFocus</enum>
      </property>
     </widget>
    </item>
    <item row="1" column="0">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="2" column="0" colspan="2">
     <layout class="QGridLayout" name="gridLayout_3">
      <item row="5" column="1">
       <widget class="QLineEdit" name="motorID">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <property name="text">
         <string>0001</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="monitorMode">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>持续监测</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>定时监测</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>定圈监测</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="6" column="1">
       <widget class="QComboBox" name="standard">
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>极值</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>置信度</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>标准差</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_10">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>占空比：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_8">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>监控圈数：</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QComboBox" name="monitorCycle">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>50</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>100</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>200</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>300</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>500</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="QLabel" name="label">
        <property name="text">
         <string>标准：</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_9">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>模式：</string>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="QLabel" name="label_11">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>编号：</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="monitorTime">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>2</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>3</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>5</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>10</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>30</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_7">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>时间(S)：</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QLineEdit" name="PWMRadio">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>154</width>
          <height>34</height>
         </size>
        </property>
        <property name="text">
         <string>28</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="3" column="0">
     <spacer name="verticalSpacer_2">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="4" column="0" colspan="2">
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="extremeValue">
       <attribute name="title">
        <string>极值</string>
       </attribute>
       <widget class="QWidget" name="layoutWidget">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>271</width>
          <height>192</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_6">
         <item row="3" column="0">
          <widget class="QLabel" name="offset2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Vmin偏差/(‰):</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="mean">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>平均转速/Hz:</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="standardRange">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>144</width>
             <height>32</height>
            </size>
           </property>
           <property name="text">
            <string>30</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="offset1">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Vmax偏差/(‰):</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="standardLabel">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>偏差(‰):</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="indicator_one">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="indicator_two">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="rpsMean">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
         <item row="4" column="0">
          <widget class="QLabel" name="offset3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>预留:</string>
           </property>
          </widget>
         </item>
         <item row="4" column="1">
          <widget class="QLineEdit" name="indicator_three">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>0</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="standardDeviation">
       <attribute name="title">
        <string>标准差</string>
       </attribute>
       <widget class="QWidget" name="gridLayoutWidget">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>281</width>
          <height>131</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <item row="3" column="0">
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>标准差:</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLineEdit" name="sigmaMean"/>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>平均转速/Hz：</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLineEdit" name="standInterval">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>124</width>
             <height>32</height>
            </size>
           </property>
           <property name="text">
            <string>0.2</string>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="sigma"/>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="standIntervalLabel">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>范围</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
     </widget>
    </item>
    <item row="5" column="0">
     <spacer name="verticalSpacer_3">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>40</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="6" column="0" colspan="2">
     <widget class="QLineEdit" name="monitorResult">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="maximumSize">
       <size>
        <width>280</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="text">
       <string>result</string>
      </property>
     </widget>
    </item>
    <item row="7" column="1">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Preferred</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="startButton">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>85</width>
          <height>39</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string>start</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item row="8" column="0" colspan="3">
     <layout class="QHBoxLayout" name="statusLayout">
      <item>
       <widget class="QLabel" name="statusMonitor">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="statusMonitorText">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="statusData">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="statusDataText">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="statusComp">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="statusCompText">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string>wait</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
