/**
 * @file config_test_app.cpp
 * @brief 配置系统测试应用程序
 * 
 * 用于测试新的配置管理系统是否正常工作
 * 
 * 编译命令：
 * g++ -I../components/config -I../qtDebug -I$QTDIR/include -L$QTDIR/lib -lQt5Core config_test_app.cpp -o config_test
 * 
 * 使用方法：
 * ./config_test [--deploy] [--test] [--verbose]
 */

#include <QCoreApplication>
#include <QCommandLineParser>
#include <QDebug>
#include <QDir>
#include <iostream>

// 包含配置系统头文件
#include "../components/config/ConfigManager.h"
#include "../components/config/ConfigDeployer.h"
#include "../components/config/ConfigTester.h"

void printHeader() {
    std::cout << "========================================" << std::endl;
    std::cout << "  光路调节配置系统测试工具 v1.0" << std::endl;
    std::cout << "  Configuration System Test Tool" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
}

void printUsage() {
    std::cout << "使用方法 / Usage:" << std::endl;
    std::cout << "  config_test [选项]" << std::endl;
    std::cout << std::endl;
    std::cout << "选项 / Options:" << std::endl;
    std::cout << "  --deploy     部署配置文件到执行目录" << std::endl;
    std::cout << "  --test       运行配置系统测试" << std::endl;
    std::cout << "  --verbose    详细输出模式" << std::endl;
    std::cout << "  --help       显示此帮助信息" << std::endl;
    std::cout << std::endl;
}

bool deployConfigs(bool verbose) {
    std::cout << "🚀 开始部署配置文件..." << std::endl;
    
    Config::ConfigDeployer deployer;
    Config::ConfigDeployer::DeployResult result = deployer.deployAllConfigs(
        Config::ConfigDeployer::DeployStrategy::SkipExisting
    );
    
    if (verbose) {
        std::cout << "源配置目录: " << deployer.getSourceConfigDir().toStdString() << std::endl;
        std::cout << "目标配置目录: " << deployer.getTargetConfigDir().toStdString() << std::endl;
        std::cout << std::endl;
    }
    
    std::cout << "📊 部署结果:" << std::endl;
    std::cout << "  ✅ 已部署: " << result.deployedFiles.size() << " 个文件" << std::endl;
    std::cout << "  ⏭️  已跳过: " << result.skippedFiles.size() << " 个文件" << std::endl;
    std::cout << "  ❌ 错误: " << result.errorFiles.size() << " 个文件" << std::endl;
    
    if (verbose && !result.deployedFiles.isEmpty()) {
        std::cout << std::endl << "已部署的文件:" << std::endl;
        for (const QString &file : result.deployedFiles) {
            std::cout << "  📄 " << file.toStdString() << std::endl;
        }
    }
    
    if (verbose && !result.skippedFiles.isEmpty()) {
        std::cout << std::endl << "已跳过的文件:" << std::endl;
        for (const QString &file : result.skippedFiles) {
            std::cout << "  ⏭️  " << file.toStdString() << std::endl;
        }
    }
    
    if (!result.errorFiles.isEmpty()) {
        std::cout << std::endl << "❌ 错误文件:" << std::endl;
        for (const QString &file : result.errorFiles) {
            std::cout << "  ❌ " << file.toStdString() << std::endl;
        }
    }
    
    std::cout << std::endl << result.message.toStdString() << std::endl;
    return result.success;
}

bool runTests(bool verbose) {
    std::cout << "🧪 开始运行配置系统测试..." << std::endl;
    
    Config::ConfigTester tester;
    tester.setVerbose(verbose);
    
    Config::ConfigTester::TestResult result = tester.runAllTests();
    
    std::cout << "📊 测试结果:" << std::endl;
    std::cout << "  ✅ 通过: " << result.passedTests.size() << " 项测试" << std::endl;
    std::cout << "  ❌ 失败: " << result.failedTests.size() << " 项测试" << std::endl;
    std::cout << "  ⚠️  警告: " << result.warnings.size() << " 项警告" << std::endl;
    std::cout << "  ⏱️  耗时: " << result.totalTime << " 毫秒" << std::endl;
    
    if (verbose && !result.passedTests.isEmpty()) {
        std::cout << std::endl << "✅ 通过的测试:" << std::endl;
        for (const QString &test : result.passedTests) {
            std::cout << "  ✅ " << test.toStdString() << std::endl;
        }
    }
    
    if (!result.failedTests.isEmpty()) {
        std::cout << std::endl << "❌ 失败的测试:" << std::endl;
        for (const QString &test : result.failedTests) {
            std::cout << "  ❌ " << test.toStdString() << std::endl;
        }
    }
    
    if (!result.warnings.isEmpty()) {
        std::cout << std::endl << "⚠️  警告信息:" << std::endl;
        for (const QString &warning : result.warnings) {
            std::cout << "  ⚠️  " << warning.toStdString() << std::endl;
        }
    }
    
    std::cout << std::endl << result.message.toStdString() << std::endl;
    return result.success;
}

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    app.setApplicationName("ConfigTestTool");
    app.setApplicationVersion("1.0");
    
    QCommandLineParser parser;
    parser.setApplicationDescription("光路调节配置系统测试工具");
    parser.addHelpOption();
    parser.addVersionOption();
    
    QCommandLineOption deployOption("deploy", "部署配置文件到执行目录");
    QCommandLineOption testOption("test", "运行配置系统测试");
    QCommandLineOption verboseOption("verbose", "详细输出模式");
    
    parser.addOption(deployOption);
    parser.addOption(testOption);
    parser.addOption(verboseOption);
    
    parser.process(app);
    
    printHeader();
    
    bool verbose = parser.isSet(verboseOption);
    bool success = true;
    
    if (parser.isSet(deployOption)) {
        success &= deployConfigs(verbose);
        std::cout << std::endl;
    }
    
    if (parser.isSet(testOption)) {
        success &= runTests(verbose);
        std::cout << std::endl;
    }
    
    if (!parser.isSet(deployOption) && !parser.isSet(testOption)) {
        printUsage();
        std::cout << "💡 建议先运行部署，再运行测试:" << std::endl;
        std::cout << "   config_test --deploy --test --verbose" << std::endl;
        std::cout << std::endl;
    }
    
    if (success) {
        std::cout << "🎉 所有操作成功完成！" << std::endl;
        return 0;
    } else {
        std::cout << "❌ 部分操作失败，请检查错误信息。" << std::endl;
        return 1;
    }
}
