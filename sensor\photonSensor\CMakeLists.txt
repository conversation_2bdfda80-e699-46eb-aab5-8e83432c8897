# 1.0
cmake_minimum_required(VERSION 3.5)

project(photonSensor LANGUAGES CXX)

# 1.2 启用当前头文件目录？是什么目录？
set(CMAKE_INCLUDE_CURRENT_DIR ON) #包含.h文件？

# 1.3 QT配置
set(CMAKE_AUTOUIC ON) #ui
set(CMAKE_AUTOMOC ON) #moc
set(CMAKE_AUTORCC ON) #rcc编译开关 用于资源文件

# 1.4
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 1.5 变量设置
set(SOURCE_FILE
    IPhotonSensor.cpp faculaFactory.cpp
    IFaculaAdjust.cpp faculaCircle.cpp faculaDouble.cpp faculaHorizontal.cpp faculaVertical.cpp
    faculaContext.cpp
    faculaProcessingConfig.cpp
    faculaDataAdapter.cpp
    FaculaConfigProvider.cpp
    AlgorithmConfigProvider.cpp
    AdjustProcessConfigProvider.cpp
    config/FaculaConfigData.cpp
    )


# 1.6 头文件和动态链接库
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR}) #找头文件
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR}/../../algorithm/imageProcessing) #ImageProcessing模块头文件
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR}/../../components/config) #配置模块头文件
LINK_DIRECTORIES(${PROJECT_SOURCE_DIR}/../lib) #找.so/.a库文件路径


# 1.7 添加各目录编译文件

############################################## 输出
ADD_LIBRARY(${PROJECT_NAME}_lib SHARED ${SOURCE_FILE})


# 链接库文件
target_link_libraries(
    ${PROJECT_NAME}_lib PRIVATE
    Qt5::Core
    Qt5::Widgets

    ConfigModule
    qLog_lib
    loadFile_lib
    image_zoom
    imageProcessing
)

# 设置动态库文件版本号
# SET_TARGET_PROPERTIES(${PROJECT_NAME}_lib PROPERTIES VERSION 1.0.0 SOVERSION 1)
# # 给动态库文件重命名
# SET_TARGET_PROPERTIES(${PROJECT_NAME}_lib PROPERTIES OUTPUT_NAME ${PROJECT_NAME})
# # 动态库路径
# SET(LIBRARY_OUTPUT_PATH ${LA_PROJECT_DIR}/../out)
