cmake_minimum_required(VERSION 3.16)
project(test_adjust_config)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt5
find_package(Qt5 REQUIRED COMPONENTS Core)

# 创建测试可执行文件
add_executable(test_adjust_config 
    test_adjust_config.cpp
    components/lensAdjust/config/AdjustProcessConfigData.cpp
)

# 设置包含目录
target_include_directories(test_adjust_config PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/components
)

# 链接库
target_link_libraries(test_adjust_config PRIVATE
    Qt5::Core
    ${CMAKE_CURRENT_SOURCE_DIR}/build/Debug/lib/libConfigModule.a
)

# 设置输出目录
set_target_properties(test_adjust_config PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build/Debug/bin
)
