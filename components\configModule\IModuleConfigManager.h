#pragma once

#include <QString>
#include "ConfigTypes.h"

namespace Config {

/**
 * @brief 模块配置管理器接口
 * 
 * 每个模块都应该实现自己的配置管理器，负责：
 * - 加载自己的配置文件
 * - 验证配置参数
 * - 提供配置访问接口
 * - 监听配置变更
 */
class IModuleConfigManager {
public:
    virtual ~IModuleConfigManager() = default;

    /**
     * @brief 加载配置文件
     * @return 配置加载结果
     */
    virtual ConfigResult loadConfig() = 0;

    /**
     * @brief 保存配置文件
     * @return 配置保存结果
     */
    virtual ConfigResult saveConfig() = 0;

    /**
     * @brief 验证配置有效性
     * @return 配置是否有效
     */
    virtual bool isConfigValid() const = 0;

    /**
     * @brief 获取配置文件路径
     * @return 配置文件路径
     */
    virtual QString getConfigFilePath() const = 0;

    /**
     * @brief 获取模块名称
     * @return 模块名称
     */
    virtual QString getModuleName() const = 0;

    /**
     * @brief 重新加载配置
     * @return 重新加载结果
     */
    virtual ConfigResult reloadConfig() {
        return loadConfig();
    }

    /**
     * @brief 配置变更通知
     * 当配置文件发生变更时调用
     */
    virtual void onConfigChanged() {
        reloadConfig();
    }

protected:
    /**
     * @brief 确保配置目录存在
     * @param dirPath 目录路径
     * @return 是否成功
     */
    bool ensureDirectoryExists(const QString &dirPath) const;

    /**
     * @brief 记录日志信息
     * @param message 日志消息
     */
    void logInfo(const QString &message) const;

    /**
     * @brief 记录错误信息
     * @param message 错误消息
     */
    void logError(const QString &message) const;
};

} // namespace Config
