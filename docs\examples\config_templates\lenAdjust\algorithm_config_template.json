{"_comment": "Algorithm configuration for lens adjustment module", "_version": "1.0", "_description": "Configuration for traditional algorithms and image processing parameters", "parameters": {"_comment": "Traditional algorithm parameters (migrated from XML)", "initial_x_dist": 0, "initial_y_dist": 0, "initial_z_dist": 0, "find_origin_raduis": 140, "find_angle_step": 20, "find_radius_step": 140, "find_times": 4, "discard_pack_num": 1, "default_z_direct": 1, "z_move_step": 3, "peak_ok_threshold": 550, "Amp_select": 30, "ALR_mp_peak": 20, "ALR_mp_peak_threshold": 100, "AUD_mp_peak": 0, "AUD_mp_peak_threshold": 100, "Aedge_peak_threshold": 180, "ACR_peak_delta": 120, "ARR_peak_delta": 50, "AMax_peak": 650, "edge_peak_threshold": 50, "peak_threshold": 600, "peak_max_threshold": 1000, "CR_peak_delta": 150, "FT_LRmp_adjust_peak": 25, "FT_LRmp_adjust_peak_threshold": 5, "FT_UDmp_adjust_peak": 0, "FT_UDmp_adjust_peak_threshold": 100, "FT_LRmp_solid_peak": 25, "FT_LRmp_solid_peak_threshold": 8, "FT_UDmp_solid_peak": 0, "FT_UDmp_solid_peak_threshold": 100, "FT_LRmp_deflate_peak": 25, "FT_LRmp_deflate_peak_threshold": 8, "FT_UDmp_deflate_peak": 0, "FT_UDmp_deflate_peak_threshold": 100}, "image_processing": {"_comment": "Image processing algorithm parameters (moved from FaculaConfig)", "interpolation_type": 0, "filter_types": "6", "interpolation_offset": 0.5, "kalman_strength": 1.0, "filter_strength": 1.0, "convolution": {"_comment": "Convolution filter parameters", "kernel_size": 3, "preset": "sharpen", "_presets": ["sharpen", "edge_detect", "emboss", "custom"]}, "median": {"_comment": "Median filter parameters", "kernel_size": 3, "preset": "noise_reduction", "_presets": ["noise_reduction", "salt_pepper", "custom"]}, "gaussian": {"_comment": "Gaussian filter parameters", "sigma": 1.0, "kernel_size": 5, "preset": "medium_blur", "_presets": ["light_blur", "medium_blur", "heavy_blur", "custom"]}, "bilateral": {"_comment": "Bilateral filter parameters", "sigma_color": 75.0, "sigma_space": 75.0, "kernel_size": 5, "preset": "smooth", "_presets": ["smooth", "edge_preserve", "custom"]}, "weighted_avg": {"_comment": "Weighted average filter parameters", "kernel_size": 3, "preset": "center_weighted", "_presets": ["center_weighted", "gaussian_weighted", "custom"]}}, "_migration_info": {"migrated_from": ["clen_config.xml", "FaculaConfig"], "migration_date": "auto-generated", "separated_from": "facula_config for better organization"}}