#pragma once

#include "IConfigData.h"
#include <QMutex>
#include <QStringList>
#include <functional>
#include <map>
#include <memory>

namespace Config {

/**
 * @brief 配置类型注册器
 * 
 * 单例模式的配置类型注册中心，负责：
 * - 动态注册新的配置类型
 * - 创建配置对象实例
 * - 管理配置类型的元信息
 * 
 * 设计特点：
 * - 线程安全的单例实现
 * - 支持运行时动态注册
 * - 工厂模式创建配置对象
 * - 类型安全的模板接口
 */
class ConfigTypeRegistry {
public:
    /**
     * @brief 配置工厂函数类型
     */
    using ConfigFactory = std::function<std::unique_ptr<IConfigData>()>;

    /**
     * @brief 配置类型信息
     */
    struct TypeInfo {
        QString typeName;           // 类型名称
        QString version;            // 版本信息
        QString description;        // 描述信息
        ConfigFactory factory;      // 工厂函数
        QStringList dependencies;   // 依赖的其他配置类型
        
        TypeInfo() = default;
        TypeInfo(const QString &name, const QString &ver, const QString &desc, 
                ConfigFactory fact, const QStringList &deps = QStringList())
            : typeName(name), version(ver), description(desc), factory(fact), dependencies(deps) {}
    };

    /**
     * @brief 获取注册器实例
     * @return 单例实例引用
     */
    static ConfigTypeRegistry &getInstance();

    /**
     * @brief 注册配置类型
     * @tparam T 配置类型，必须继承自IConfigData
     * @param typeName 类型名称
     * @param version 版本信息
     * @param description 描述信息
     * @param dependencies 依赖的其他配置类型
     * @return 注册是否成功
     */
    template<typename T>
    bool registerConfigType(const QString &typeName, 
                           const QString &version = "1.0.0",
                           const QString &description = QString(),
                           const QStringList &dependencies = QStringList()) {
        static_assert(std::is_base_of_v<IConfigData, T>, "T must inherit from IConfigData");
        
        QMutexLocker locker(&m_mutex);
        
        if (m_typeInfos.find(typeName) != m_typeInfos.end()) {
            logWarning(QString("Config type '%1' already registered").arg(typeName));
            return false;
        }

        auto factory = []() -> std::unique_ptr<IConfigData> {
            return std::make_unique<T>();
        };

        QString desc = description.isEmpty() ? QString("Configuration for %1").arg(typeName) : description;
        
        m_typeInfos[typeName] = TypeInfo(typeName, version, desc, factory, dependencies);
        
        logInfo(QString("Registered config type: %1 (v%2)").arg(typeName, version));
        return true;
    }

    /**
     * @brief 注销配置类型
     * @param typeName 类型名称
     * @return 注销是否成功
     */
    bool unregisterConfigType(const QString &typeName);

    /**
     * @brief 创建配置对象
     * @param typeName 类型名称
     * @return 配置对象实例，失败时返回nullptr
     */
    std::unique_ptr<IConfigData> createConfig(const QString &typeName);

    /**
     * @brief 创建指定类型的配置对象
     * @tparam T 配置类型
     * @param typeName 类型名称
     * @return 配置对象实例，失败时返回nullptr
     */
    template<typename T>
    std::unique_ptr<T> createConfig(const QString &typeName) {
        auto config = createConfig(typeName);
        if (!config) {
            return nullptr;
        }
        
        // 尝试转换为指定类型
        T* ptr = dynamic_cast<T*>(config.get());
        if (!ptr) {
            logError(QString("Failed to cast config '%1' to requested type").arg(typeName));
            return nullptr;
        }
        
        config.release();
        return std::unique_ptr<T>(ptr);
    }

    /**
     * @brief 检查配置类型是否已注册
     * @param typeName 类型名称
     * @return 是否已注册
     */
    bool isRegistered(const QString &typeName) const;

    /**
     * @brief 获取所有已注册的配置类型
     * @return 类型名称列表
     */
    QStringList getRegisteredTypes() const;

    /**
     * @brief 获取配置类型信息
     * @param typeName 类型名称
     * @return 类型信息，未找到时返回空的TypeInfo
     */
    TypeInfo getTypeInfo(const QString &typeName) const;

    /**
     * @brief 获取所有类型信息
     * @return 类型信息映射表
     */
    std::map<QString, TypeInfo> getAllTypeInfos() const;

    /**
     * @brief 验证配置类型的依赖关系
     * @param typeName 类型名称
     * @return 依赖关系是否满足
     */
    bool validateDependencies(const QString &typeName) const;

    /**
     * @brief 获取配置类型的依赖顺序
     * @return 按依赖关系排序的类型名称列表
     */
    QStringList getDependencyOrder() const;

    /**
     * @brief 清空所有注册的配置类型
     */
    void clear();

    /**
     * @brief 获取注册统计信息
     * @return 统计信息映射表
     */
    QVariantMap getStatistics() const;

private:
    ConfigTypeRegistry() = default;
    ~ConfigTypeRegistry() = default;
    ConfigTypeRegistry(const ConfigTypeRegistry&) = delete;
    ConfigTypeRegistry& operator=(const ConfigTypeRegistry&) = delete;

    /**
     * @brief 记录日志信息
     * @param message 日志消息
     */
    void logInfo(const QString &message) const;

    /**
     * @brief 记录错误信息
     * @param message 错误消息
     */
    void logError(const QString &message) const;

    /**
     * @brief 记录警告信息
     * @param message 警告消息
     */
    void logWarning(const QString &message) const;

    /**
     * @brief 递归检查依赖关系
     * @param typeName 类型名称
     * @param visited 已访问的类型集合
     * @param path 当前路径
     * @return 是否存在循环依赖
     */
    bool checkCircularDependency(const QString &typeName, 
                                QSet<QString> &visited, 
                                QStringList &path) const;

private:
    mutable QMutex m_mutex;                          // 线程安全锁
    std::map<QString, TypeInfo> m_typeInfos;        // 类型信息映射表
    static ConfigTypeRegistry* s_instance;          // 单例实例
    static QMutex s_instanceMutex;                  // 实例创建锁
};

/**
 * @brief 自动注册配置类型的宏
 * 
 * 使用方法：
 * REGISTER_CONFIG_TYPE(AlgorithmConfig, "Algorithm", "1.0.0", "算法配置参数")
 */
#define REGISTER_CONFIG_TYPE(ClassName, TypeName, Version, Description, ...) \
    namespace { \
        static bool registered_##ClassName = []() { \
            return Config::ConfigTypeRegistry::getInstance().registerConfigType<ClassName>( \
                TypeName, Version, Description, ##__VA_ARGS__); \
        }(); \
    }

/**
 * @brief 简化的自动注册宏（使用默认参数）
 * 
 * 使用方法：
 * AUTO_REGISTER_CONFIG(AlgorithmConfig, "Algorithm")
 */
#define AUTO_REGISTER_CONFIG(ClassName, TypeName) \
    REGISTER_CONFIG_TYPE(ClassName, TypeName, "1.0.0", "")

}  // namespace Config
