{"_comment": "硬件配置文件 - 光路调节模块", "_version": "1.0", "_description": "镜片调节设备硬件参数和运动控制配置", "_last_updated": "2024-01-01T00:00:00Z", "hardware": {"_comment": "通用硬件配置参数", "xy_radius_limit": 1500, "z_radius_limit": 1400, "x_step_dist": 10, "y_step_dist": 10, "z_step_dist": 10, "_field_descriptions": {"xy_radius_limit": "XY轴运动半径限制（微米），防止超出安全范围", "z_radius_limit": "Z轴运动限制（微米），防止碰撞", "x_step_dist": "X轴单脉冲移动距离（微米/脉冲）", "y_step_dist": "Y轴单脉冲移动距离（微米/脉冲）", "z_step_dist": "Z轴单脉冲移动距离（微米/脉冲）"}, "_safety_notes": {"radius_limits": "半径限制是安全保护参数，不建议随意修改", "step_distances": "步距参数影响运动精度，需要根据实际设备标定", "coordinate_system": "坐标系以设备中心为原点，正值表示正方向运动"}}, "device_specific": {"_comment": "设备特定配置，根据clens_machine_brand自动选择", "shunTuo": {"_comment": "顺拓设备配置（brand=1）", "xy_radius_limit": 900, "z_radius_limit": 1000, "x_step_dist": 7, "y_step_dist": 7, "z_step_dist": 8, "_device_notes": {"precision": "顺拓设备精度较高，步距较小", "range": "运动范围相对较小，适合精密调节", "speed": "运动速度中等，稳定性好"}}, "qingHe": {"_comment": "清河设备配置（brand=2）", "xy_radius_limit": 1200, "z_radius_limit": 1200, "x_step_dist": 8, "y_step_dist": 8, "z_step_dist": 9, "_device_notes": {"precision": "清河设备精度中等，性价比高", "range": "运动范围适中，通用性好", "speed": "运动速度较快，效率高"}}, "qingHeShiJue": {"_comment": "清河视觉设备配置（brand=3，推荐）", "xy_radius_limit": 1500, "z_radius_limit": 1400, "x_step_dist": 10, "y_step_dist": 10, "z_step_dist": 10, "_device_notes": {"precision": "清河视觉设备集成度高，精度和范围平衡", "range": "运动范围大，适合各种应用场景", "speed": "运动速度和精度兼顾，推荐使用", "vision_integration": "集成视觉系统，调节精度更高"}}}, "communication": {"_comment": "通信参数配置", "buffer_size": 2000, "timeout_ms": 5000, "retry_count": 3, "protocol": "ModBus", "_field_descriptions": {"buffer_size": "通信缓冲区大小（字节）", "timeout_ms": "通信超时时间（毫秒）", "retry_count": "通信失败重试次数", "protocol": "通信协议类型"}, "_communication_notes": {"buffer_size": "缓冲区大小影响数据传输效率，过小可能丢数据", "timeout": "超时时间需要根据设备响应速度调整", "retry": "重试次数过多会影响实时性，过少可能不稳定"}}, "motion_control": {"_comment": "运动控制参数", "acceleration": 1000, "max_velocity": 5000, "jerk_limit": 10000, "home_velocity": 1000, "_field_descriptions": {"acceleration": "加速度（微米/秒²）", "max_velocity": "最大速度（微米/秒）", "jerk_limit": "加加速度限制（微米/秒³）", "home_velocity": "回零速度（微米/秒）"}, "_motion_notes": {"acceleration": "加速度影响运动平滑性，过大可能震动", "velocity": "最大速度影响调节效率，但过快可能影响精度", "jerk": "加加速度限制可以减少机械冲击", "homing": "回零速度应该较慢以保证精度"}}, "_migration_info": {"_comment": "配置迁移信息", "migrated_from": "hardcoded values in machine classes", "migration_date": "auto-generated", "original_files": ["clensMachineST.cpp", "clensMachineQH.cpp"], "centralization": "将分散在各设备类中的硬编码参数集中管理"}, "_calibration_info": {"step_distance_calibration": "步距参数需要通过实际测量标定，建议定期校准", "limit_verification": "运动限制参数需要通过实际测试验证安全性", "device_matching": "确保配置参数与实际设备型号匹配", "backup_recommendation": "修改硬件参数前建议备份原始配置"}}