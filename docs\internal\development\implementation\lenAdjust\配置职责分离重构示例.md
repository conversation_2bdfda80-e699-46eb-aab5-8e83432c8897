# 配置职责分离重构示例

## 问题描述

当前的配置加载存在职责混乱的问题：
- CLenAdjustOpt加载了`facula_handle_type`，但自己不使用
- CFaculaContext需要这个参数，但要通过参数传递获得
- 违反了"谁使用谁加载"的设计原则

## 重构方案

### 1. 新增专门的配置接口

在ConfigManager中新增按职责分离的配置获取接口：

```cpp
// 设备配置 - CLenAdjustOpt使用
DeviceConfig getDeviceConfig() const;

// 流程配置 - CLenAdjustOpt使用  
ProcessConfig getProcessConfig() const;

// 光斑配置 - CFaculaContext使用
const FaculaConfig &getFaculaConfig() const;

// 算法配置 - IFaculaAdjust使用
const AlgorithmConfig &getAlgorithmConfig() const;
```

### 2. 重构前的代码结构

**CLenAdjustOpt (重构前)**:
```cpp
class CLenAdjustOpt {
private:
    ClensReadIni::IniConfig mst_iniConfig;  // 加载了所有配置
    
public:
    void initializeComponents() {
        // 加载了不需要的光斑配置
        mst_iniConfig = ClensReadIni().readConfig();
        
        // 传递给CFaculaContext
        m_faculaContext = new CFaculaContext(mst_iniConfig.facula_handle_type);
    }
};
```

**CFaculaContext (重构前)**:
```cpp
class CFaculaContext {
public:
    CFaculaContext(uint8_t handleType) {
        // 通过参数获得配置，依赖外部传入
        m_handleType = handleType;
    }
};
```

### 3. 重构后的代码结构

**CLenAdjustOpt (重构后)**:
```cpp
class CLenAdjustOpt {
private:
    Config::SystemConfig m_systemConfig;
    Config::DeviceConfig m_deviceConfig;
    Config::ProcessConfig m_processConfig;
    
public:
    void initializeComponents() {
        // 只加载自己需要的配置
        Config::ConfigManager* manager = Config::ConfigManager::getInstance();
        m_systemConfig = manager->getSystemConfig();
        m_deviceConfig = manager->getDeviceConfig();
        m_processConfig = manager->getProcessConfig();
        
        // CFaculaContext自主加载配置，无需参数传递
        m_faculaContext = new CFaculaContext();
    }
    
    // 使用自己的配置
    void setupDevice() {
        setupSensorDevice(m_deviceConfig.sensor_device, m_deviceConfig.sensor_device_baud);
    }
    
    void processAdjustment() {
        for (int i = 0; i < m_processConfig.facula_ok_times; ++i) {
            // 使用流程配置
        }
    }
};
```

**CFaculaContext (重构后)**:
```cpp
class CFaculaContext {
private:
    Config::FaculaConfig m_faculaConfig;
    
public:
    CFaculaContext() {
        // 自主加载需要的配置
        Config::ConfigManager* manager = Config::ConfigManager::getInstance();
        m_faculaConfig = manager->getFaculaConfig();
        
        // 使用自己的配置初始化
        initializeProcessing();
    }
    
private:
    void initializeProcessing() {
        // 使用光斑配置
        setupChannels(m_faculaConfig.facula_center_channels);
        setThreshold(m_faculaConfig.facula_center_peak_threshold);
        setHandleType(m_faculaConfig.facula_handle_type);
    }
};
```

### 4. 配置结构重新设计

```cpp
// 系统配置 - MES和版本管理
struct SystemConfig {
    QString version;
    QString userid;
    QString op;
    QString work_number;
    QString work_domain;
    uint8_t station_number;
};

// 设备配置 - 设备通信和控制
struct DeviceConfig {
    uint8_t  sensor_device;
    uint32_t sensor_device_baud;
    uint8_t  clens_machine_brand;
};

// 流程配置 - 调节流程控制
struct ProcessConfig {
    uint8_t  facula_ok_times;
    uint32_t solid_time;
    uint8_t  facula_ng_handle;
};

// 光斑配置 - 光斑检测和处理
struct FaculaConfig {
    QString         facula_center_channels;
    uint32_t        facula_center_peak_threshold;
    uint8_t         facula_handle_type;
    // ... 其他光斑相关参数
};
```

### 5. 重构的优势

#### 5.1 职责清晰
- 每个类只加载自己需要的配置
- 配置的使用者负责配置的加载
- 减少不必要的依赖关系

#### 5.2 参数传递简化
- 消除了不必要的参数传递
- 减少了构造函数的复杂性
- 提高了代码的可读性

#### 5.3 可测试性提升
- 每个类的配置依赖明确
- 便于进行单元测试
- 可以轻松模拟配置数据

#### 5.4 可维护性改善
- 配置变更的影响范围明确
- 减少了配置参数的传递链
- 符合单一职责原则

### 6. 实施步骤

1. **第一阶段**: 添加新的配置接口（已完成）
   - 在ConfigManager中添加getDeviceConfig()和getProcessConfig()
   - 保持现有接口的兼容性

2. **第二阶段**: 重构CFaculaContext
   - 修改构造函数，移除参数传递
   - 在构造函数中直接加载FaculaConfig
   - 测试光斑处理功能

3. **第三阶段**: 重构CLenAdjustOpt
   - 使用专门的配置接口
   - 移除不需要的配置加载
   - 简化组件初始化代码

4. **第四阶段**: 清理和优化
   - 移除不再使用的配置传递代码
   - 更新相关的单元测试
   - 完善文档和注释

### 7. 风险控制

#### 7.1 向后兼容
- 保持现有的配置结构不变
- 新接口作为现有接口的补充
- 逐步迁移，避免破坏性变更

#### 7.2 测试验证
- 每个重构步骤都要进行功能测试
- 确保配置加载的正确性
- 验证光路调节功能的完整性

#### 7.3 回滚机制
- 保留原有的代码作为备份
- 可以快速回滚到重构前的状态
- 分阶段实施，降低风险

## 总结

这个重构方案解决了配置职责混乱的问题，实现了"谁使用谁加载"的设计原则，提高了代码的可维护性和可测试性。通过分阶段实施和严格的测试验证，可以安全地完成这个重构。
