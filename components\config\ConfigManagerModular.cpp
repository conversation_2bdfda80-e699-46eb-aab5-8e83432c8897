#include "ConfigManager.h"
#include <QApplication>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QSettings>
#include <QXmlStreamReader>
#include <QXmlStreamWriter>

namespace Config {

// 获取模块配置文件路径
QString ConfigManager::getModuleConfigFilePath(const QString& moduleName, FileType type) const {
    QString configDir = QApplication::applicationDirPath() + "/config/modules/" + moduleName + "/";
    
    switch (type) {
    case FileType::System:
        return configDir + "system_config.json";
    case FileType::Facula:
        return configDir + "facula_config.json";
    case FileType::Hardware:
        return configDir + "hardware_config.json";
    case FileType::Algorithm:
        return configDir + "algorithm_config.json";
    default:
        return QString();
    }
}

// 检查模块配置文件是否存在
bool ConfigManager::moduleConfigFileExists(const QString& moduleName, FileType type) const {
    return QFile::exists(getModuleConfigFilePath(moduleName, type));
}

// 确保模块配置目录存在
bool ConfigManager::ensureModuleConfigDirectory(const QString& moduleName) {
    QString configDir = QApplication::applicationDirPath() + "/config/modules/" + moduleName + "/";
    QDir dir;
    
    if (!dir.exists(configDir)) {
        if (!dir.mkpath(configDir)) {
            logError("Failed to create module config directory: " + configDir);
            return false;
        }
        logInfo("Created module config directory: " + configDir);
    }
    
    return true;
}

// 检测配置文件格式
ConfigFormat ConfigManager::detectConfigFormat(const QString& filePath) const {
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();
    
    if (suffix == "json") {
        return ConfigFormat::JSON;
    } else if (suffix == "ini") {
        return ConfigFormat::INI;
    } else if (suffix == "xml") {
        return ConfigFormat::XML;
    }
    
    // 尝试通过文件内容检测
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return ConfigFormat::Auto;
    }
    
    QByteArray data = file.read(1024); // 读取前1024字节
    file.close();
    
    QString content = QString::fromUtf8(data).trimmed();
    
    if (content.startsWith("{") || content.startsWith("[")) {
        return ConfigFormat::JSON;
    } else if (content.startsWith("<?xml") || content.startsWith("<")) {
        return ConfigFormat::XML;
    } else if (content.contains("[") && content.contains("=")) {
        return ConfigFormat::INI;
    }
    
    return ConfigFormat::Auto;
}

// 从JSON文件加载配置
ConfigResult ConfigManager::loadConfigFromJson(const QString& filePath, FileType type) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return ConfigResult(false, ErrorType::FileNotFound, "Cannot open JSON file: " + filePath);
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        return ConfigResult(false, ErrorType::ParseError, "JSON parse error: " + error.errorString());
    }
    
    QJsonObject rootObj = doc.object();
    
    switch (type) {
    case FileType::System:
        return loadSystemConfigFromJson(rootObj);
    case FileType::Facula:
        return loadFaculaConfigFromJson(rootObj);
    case FileType::Hardware:
        return loadHardwareConfigFromJson(rootObj);
    case FileType::Algorithm:
        return loadAlgorithmConfigFromJson(rootObj);
    default:
        return ConfigResult(false, ErrorType::UnknownError, "Unknown config type");
    }
}

// 保存配置到JSON文件
ConfigResult ConfigManager::saveConfigToJson(const QString& filePath, FileType type) {
    QJsonObject rootObj;
    
    switch (type) {
    case FileType::System:
        saveSystemConfigToJson(rootObj);
        break;
    case FileType::Facula:
        saveFaculaConfigToJson(rootObj);
        break;
    case FileType::Hardware:
        saveHardwareConfigToJson(rootObj);
        break;
    case FileType::Algorithm:
        saveAlgorithmConfigToJson(rootObj);
        break;
    default:
        return ConfigResult(false, ErrorType::UnknownError, "Unknown config type");
    }
    
    QJsonDocument doc(rootObj);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return ConfigResult(false, ErrorType::PermissionError, "Cannot write JSON file: " + filePath);
    }
    
    file.write(doc.toJson(QJsonDocument::Indented));
    file.close();
    
    logInfo("Saved configuration to JSON: " + filePath);
    return ConfigResult(true);
}

// 加载模块配置
ConfigResult ConfigManager::loadModuleConfig(const QString& moduleName, FileType type) {
    if (!ensureModuleConfigDirectory(moduleName)) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create module directory");
    }
    
    QString filePath = getModuleConfigFilePath(moduleName, type);
    
    if (!QFile::exists(filePath)) {
        // 生成默认配置
        ConfigResult result = generateDefaultModuleConfig(moduleName, type);
        if (!result.success) {
            return result;
        }
    }
    
    ConfigFormat format = detectConfigFormat(filePath);
    
    switch (format) {
    case ConfigFormat::JSON:
        return loadConfigFromJson(filePath, type);
    case ConfigFormat::INI:
        return loadConfig(type); // 使用现有的INI加载方法
    case ConfigFormat::XML:
        return loadConfig(type); // 使用现有的XML加载方法
    default:
        return ConfigResult(false, ErrorType::ParseError, "Unknown config format: " + filePath);
    }
}

// 保存模块配置
ConfigResult ConfigManager::saveModuleConfig(const QString& moduleName, FileType type) {
    if (!ensureModuleConfigDirectory(moduleName)) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create module directory");
    }
    
    QString filePath = getModuleConfigFilePath(moduleName, type);
    return saveConfigToJson(filePath, type);
}

// 生成默认模块配置
ConfigResult ConfigManager::generateDefaultModuleConfig(const QString& moduleName, FileType type) {
    if (!ensureModuleConfigDirectory(moduleName)) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create module directory");
    }
    
    QString filePath = getModuleConfigFilePath(moduleName, type);
    logInfo("Generating default module configuration: " + filePath);
    
    return saveConfigToJson(filePath, type);
}

} // namespace Config
