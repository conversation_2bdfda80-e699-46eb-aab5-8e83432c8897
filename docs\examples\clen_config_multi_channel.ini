# 光斑判定标准-多通道判定配置示例
# Multi-channel Facula Center Detection Configuration Example

[SENSOR_BOARD]
version=V1.0.0

[MES]
userid=001
op=OP001
work_number=WN001
work_domain=DOMAIN001

[DEVICE]
sensor_device=4
sensor_device_baud=115200
station_number=1
clens_machine_brand=1

[ADJUST_PARAM]
# 多通道光斑中心配置 (新格式)
# Multi-channel facula center configuration (new format)
facula_center_channels=2,2;1,2;3,2  # 支持多通道，格式：x1,y1;x2,y2;x3,y3
facula_center_peak_threshold=800     # 多通道模式下的peak阈值

# 兼容性配置 (旧格式，如果没有多通道配置则使用此配置)
# Compatibility configuration (old format, used if no multi-channel config)
# facula_center_loc_x=2 # 光斑中心坐标x (兼容性)
# facula_center_loc_y=2 # 光斑中心坐标y (兼容性)

facula_ok_time=3
solid_time=5000
facula_ng_handle=0

[FACULA_HANDLE]
facula_handle_type=0

# 配置说明 Configuration Notes:
# 
# 1. facula_center_channels: 多通道配置字符串
#    - 格式：x1,y1;x2,y2;x3,y3
#    - 每个通道用分号分隔
#    - 每个通道的x,y坐标用逗号分隔
#    - 示例：2,2;1,2;3,2 表示三个通道：(2,2), (1,2), (3,2)
#
# 2. facula_center_peak_threshold: 多通道模式下的peak阈值
#    - 当光斑中心在任一配置通道内且peak值大于此阈值时，判定为成功
#    - 默认值：800
#
# 3. 三种配置模式：
#    - 没有配置：默认使用中心通道 (兼容性模式)
#    - 配置一个通道：光斑中心只能在这个通道
#    - 配置多个通道：光斑中心在任一通道内且peak值满足要求即可，跳过后续判定
#
# 4. 多通道判定优势：
#    - 解决反光镜导致的光斑中心偏移问题
#    - 提高判定效率，成功时跳过后续对称性判定
#    - 保持完全向后兼容性
