#include "AlgorithmConfigData.h"
#include <QDebug>

namespace Config {

// 静态成员初始化
const QMap<QString, QString> AlgorithmConfigData::s_fieldTypes = {
    {"parameters", "QMap<QString, int>"},
    {"interpolation_type", "uint8_t"},
    {"filter_types", "QString"},
    {"filter_strength", "float"}
};

const QMap<QString, QString> AlgorithmConfigData::s_fieldDescriptions = {
    {"parameters", "传统算法参数映射表，包含60+个算法参数"},
    {"interpolation_type", "插值类型：0=最近邻，1=双线性，2=双三次，3=Lanczos"},
    {"filter_types", "滤波器类型列表，逗号分隔的滤波器ID"},
    {"filter_strength", "滤波器强度，范围0.0-10.0"}
};

const QMap<QString, QPair<int, int>> AlgorithmConfigData::s_parameterRanges = {
    // 光斑检测参数
    {"facula_threshold_min", {0, 255}},
    {"facula_threshold_max", {0, 255}},
    {"facula_area_min", {1, 10000}},
    {"facula_area_max", {100, 100000}},
    {"facula_circularity_min", {0, 100}},
    {"facula_circularity_max", {0, 100}},
    
    // 图像处理参数
    {"gaussian_kernel_size", {3, 15}},
    {"gaussian_sigma", {1, 10}},
    {"median_kernel_size", {3, 15}},
    {"bilateral_d", {5, 15}},
    {"bilateral_sigma_color", {10, 150}},
    {"bilateral_sigma_space", {10, 150}},
    
    // 边缘检测参数
    {"canny_threshold1", {50, 200}},
    {"canny_threshold2", {100, 300}},
    {"canny_aperture_size", {3, 7}},
    
    // 形态学操作参数
    {"morph_kernel_size", {3, 15}},
    {"morph_iterations", {1, 10}},
    
    // 轮廓检测参数
    {"contour_area_threshold", {100, 50000}},
    {"contour_perimeter_threshold", {50, 5000}},
    
    // 其他算法参数
    {"roi_margin", {0, 100}},
    {"max_features", {10, 1000}},
    {"quality_level", {1, 100}},
    {"min_distance", {1, 50}}
};

const QMap<QString, int> AlgorithmConfigData::s_defaultParameters = {
    // 光斑检测默认参数
    {"facula_threshold_min", 50},
    {"facula_threshold_max", 200},
    {"facula_area_min", 100},
    {"facula_area_max", 5000},
    {"facula_circularity_min", 30},
    {"facula_circularity_max", 100},
    
    // 图像处理默认参数
    {"gaussian_kernel_size", 5},
    {"gaussian_sigma", 2},
    {"median_kernel_size", 5},
    {"bilateral_d", 9},
    {"bilateral_sigma_color", 75},
    {"bilateral_sigma_space", 75},
    
    // 边缘检测默认参数
    {"canny_threshold1", 100},
    {"canny_threshold2", 200},
    {"canny_aperture_size", 3},
    
    // 形态学操作默认参数
    {"morph_kernel_size", 5},
    {"morph_iterations", 2},
    
    // 轮廓检测默认参数
    {"contour_area_threshold", 500},
    {"contour_perimeter_threshold", 200},
    
    // 其他算法默认参数
    {"roi_margin", 10},
    {"max_features", 100},
    {"quality_level", 10},
    {"min_distance", 10}
};

AlgorithmConfigData::AlgorithmConfigData()
    : interpolation_type(0)
    , filter_types("1,2,3")
    , filter_strength(1.0f)
{
    setDefaults();
}

QVariantMap AlgorithmConfigData::toVariantMap() const {
    QVariantMap map;
    
    // 转换参数映射表
    QVariantMap paramMap;
    for (auto it = parameters.begin(); it != parameters.end(); ++it) {
        paramMap[it.key()] = it.value();
    }
    map["parameters"] = paramMap;
    
    // 转换图像处理参数
    map["interpolation_type"] = interpolation_type;
    map["filter_types"] = filter_types;
    map["filter_strength"] = filter_strength;
    
    // 添加元信息
    map["_type"] = getTypeName();
    map["_version"] = getVersion();
    
    return map;
}

bool AlgorithmConfigData::fromVariantMap(const QVariantMap &data) {
    try {
        // 加载参数映射表
        if (data.contains("parameters")) {
            QVariantMap paramMap = data["parameters"].toMap();
            parameters.clear();
            for (auto it = paramMap.begin(); it != paramMap.end(); ++it) {
                parameters[it.key()] = it.value().toInt();
            }
        }
        
        // 加载图像处理参数
        if (data.contains("interpolation_type")) {
            interpolation_type = data["interpolation_type"].toUInt();
        }
        
        if (data.contains("filter_types")) {
            filter_types = data["filter_types"].toString();
        }
        
        if (data.contains("filter_strength")) {
            filter_strength = data["filter_strength"].toFloat();
        }
        
        return true;
    } catch (const std::exception &e) {
        logError(QString("Failed to load from variant map: %1").arg(e.what()));
        return false;
    } catch (...) {
        logError("Unknown error loading from variant map");
        return false;
    }
}

bool AlgorithmConfigData::validate() const {
    // 验证插值类型
    if (interpolation_type > 3) {
        logError(QString("Invalid interpolation type: %1 (valid: 0-3)").arg(interpolation_type));
        return false;
    }
    
    // 验证滤波器强度
    if (filter_strength < 0.0f || filter_strength > 10.0f) {
        logError(QString("Invalid filter strength: %1 (valid: 0.0-10.0)").arg(filter_strength));
        return false;
    }
    
    // 验证滤波器类型格式
    if (filter_types.isEmpty()) {
        logError("Filter types cannot be empty");
        return false;
    }
    
    // 验证参数值范围
    for (auto it = parameters.begin(); it != parameters.end(); ++it) {
        if (!validateParameterValue(it.key(), it.value())) {
            return false;
        }
    }
    
    return true;
}

void AlgorithmConfigData::setDefaults() {
    // 设置图像处理默认值
    interpolation_type = 0;
    filter_types = "1,2,3";
    filter_strength = 1.0f;
    
    // 加载默认算法参数
    loadDefaultParameters();
    
    logInfo("Set algorithm config to default values");
}

QStringList AlgorithmConfigData::getFieldNames() const {
    return s_fieldTypes.keys();
}

QString AlgorithmConfigData::getFieldType(const QString &fieldName) const {
    return s_fieldTypes.value(fieldName, "unknown");
}

QString AlgorithmConfigData::getFieldDescription(const QString &fieldName) const {
    return s_fieldDescriptions.value(fieldName, "No description available");
}

bool AlgorithmConfigData::hasField(const QString &fieldName) const {
    return s_fieldTypes.contains(fieldName);
}

QVariant AlgorithmConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    if (fieldName == "parameters") {
        QVariantMap paramMap;
        for (auto it = parameters.begin(); it != parameters.end(); ++it) {
            paramMap[it.key()] = it.value();
        }
        return paramMap;
    } else if (fieldName == "interpolation_type") {
        return interpolation_type;
    } else if (fieldName == "filter_types") {
        return filter_types;
    } else if (fieldName == "filter_strength") {
        return filter_strength;
    }
    
    return defaultValue;
}

bool AlgorithmConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    if (fieldName == "parameters") {
        QVariantMap paramMap = value.toMap();
        parameters.clear();
        for (auto it = paramMap.begin(); it != paramMap.end(); ++it) {
            parameters[it.key()] = it.value().toInt();
        }
        return true;
    } else if (fieldName == "interpolation_type") {
        interpolation_type = value.toUInt();
        return true;
    } else if (fieldName == "filter_types") {
        filter_types = value.toString();
        return true;
    } else if (fieldName == "filter_strength") {
        filter_strength = value.toFloat();
        return true;
    }
    
    return false;
}

bool AlgorithmConfigData::resetField(const QString &fieldName) {
    if (fieldName == "parameters") {
        loadDefaultParameters();
        return true;
    } else if (fieldName == "interpolation_type") {
        interpolation_type = 0;
        return true;
    } else if (fieldName == "filter_types") {
        filter_types = "1,2,3";
        return true;
    } else if (fieldName == "filter_strength") {
        filter_strength = 1.0f;
        return true;
    }
    
    return false;
}

int AlgorithmConfigData::getParameter(const QString &key, int defaultValue) const {
    return parameters.value(key, defaultValue);
}

void AlgorithmConfigData::setParameter(const QString &key, int value) {
    parameters[key] = value;
}

bool AlgorithmConfigData::hasParameter(const QString &key) const {
    return parameters.contains(key);
}

bool AlgorithmConfigData::removeParameter(const QString &key) {
    return parameters.remove(key) > 0;
}

QStringList AlgorithmConfigData::getParameterNames() const {
    return parameters.keys();
}

void AlgorithmConfigData::importParameters(const AlgorithmConfigData &other, bool overwrite) {
    for (auto it = other.parameters.begin(); it != other.parameters.end(); ++it) {
        if (overwrite || !parameters.contains(it.key())) {
            parameters[it.key()] = it.value();
        }
    }
}

void AlgorithmConfigData::loadDefaultParameters() {
    parameters = s_defaultParameters;
}

bool AlgorithmConfigData::validateParameterValue(const QString &key, int value) const {
    if (s_parameterRanges.contains(key)) {
        QPair<int, int> range = s_parameterRanges[key];
        if (value < range.first || value > range.second) {
            logError(QString("Parameter '%1' value %2 out of range [%3, %4]")
                    .arg(key).arg(value).arg(range.first).arg(range.second));
            return false;
        }
    }
    return true;
}

QPair<int, int> AlgorithmConfigData::getParameterRange(const QString &key) const {
    return s_parameterRanges.value(key, {INT_MIN, INT_MAX});
}

int AlgorithmConfigData::getParameterDefaultValue(const QString &key) const {
    return s_defaultParameters.value(key, 0);
}

}  // namespace Config
