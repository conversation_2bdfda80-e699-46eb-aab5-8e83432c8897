#ifndef _LENS_READ_INI_H_
#define _LENS_READ_INI_H_

#include <QPoint>
#include <QSettings>
#include <QVector>


#define DATACONFIG ClensReadIni::getInstance()->getIniConfig()

class ClensReadIni {
  public:
    static ClensReadIni *getInstance();

    typedef struct {
        QString  version;      //固件账号
        QString  userid;       //账号
        QString  op;           //工序号
        QString  work_number;  //工单号
        QString  work_domain;
        uint     sensor_device_buad;
        uint32_t solid_time;           //固化时间
        uint8_t  sensor_device;        //模组
        uint8_t  station_number;       //工位号
        uint8_t  clens_machine_brand;  //光路设备

        // 多通道光斑中心配置
        QString         facula_center_channels;        // 多通道配置字符串，格式："x1,y1;x2,y2;x3,y3"
        QVector<QPoint> facula_center_points;          // 解析后的通道坐标列表
        uint32_t        facula_center_peak_threshold;  // 多通道模式下的peak阈值

        // 兼容性：保留原有单点配置（从多通道配置中取第一个点）
        uint8_t facula_center_loc_x;
        uint8_t facula_center_loc_y;

        uint8_t facula_ok_times;     //
        uint8_t facula_ng_handle;    //光斑异常处理方式，0-手动处理；1-继续执行
        uint8_t facula_handle_type;  // 0-原光斑调节; 1-启用处理后光斑调节
    } IniConfig;

    void             readIni();
    const IniConfig &getIniConfig();

  private:
    ClensReadIni();

    // 多通道配置解析方法
    QVector<QPoint> parseFaculaCenterChannels(const QString &channelsStr);
    bool            isValidChannelPoint(const QPoint &point, uint8_t maxX = 255, uint8_t maxY = 255);


    IniConfig iniConfig;


    static ClensReadIni *instance;
};

#endif
