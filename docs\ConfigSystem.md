# 激光工具配置系统文档

## 概述

本文档描述了激光工具系统的配置管理架构，包括配置类型定义、动态配置管理、文件格式和使用方法。

## 系统架构

### 核心组件

```
ConfigModule/
├── ConfigTypes.h              # 配置系统基础类型定义
├── IConfigData.h             # 配置数据接口
├── ConfigTypeRegistry.h      # 配置类型注册器
├── DynamicConfigManager.h    # 动态配置管理器
└── 具体配置实现/
    ├── AlgorithmConfigData    # 算法配置
    ├── AdjustProcessConfigData # 调节流程配置
    ├── HardwareConfigData     # 硬件配置
    └── FaculaConfigData       # 光斑配置
```

### 设计模式

1. **工厂模式**: ConfigTypeRegistry使用工厂模式创建配置对象
2. **单例模式**: ConfigTypeRegistry和DynamicConfigManager采用单例设计
3. **策略模式**: 不同配置类型实现统一的IConfigData接口
4. **观察者模式**: 配置变更通知机制

## 配置类型

### 1. 算法配置 (AlgorithmConfigData)

**用途**: 控制图像处理和光斑检测算法参数

**主要参数**:
- `interpolation_type`: 插值类型 (0=最近邻, 1=双线性, 2=双三次)
- `filter_type`: 滤波器类型 (0=无, 1=高斯, 2=中值, 3=双边)
- `threshold_value`: 阈值设置 (0-255)
- `detection_sensitivity`: 检测灵敏度 (0.1-1.0)

**配置文件示例**:
```ini
; Algorithm 配置文件
; 版本: 2.1.0
; 描述: 算法模块配置参数，包含传统算法、图像处理和光斑检测参数

[image_processing]
; 插值类型 (uint8_t)
interpolation_type=1
; 滤波器类型 (uint8_t)
filter_type=1
; 阈值设置 (uint8_t)
threshold_value=128

[detection]
; 检测灵敏度 (double)
detection_sensitivity=0.8
```

### 2. 硬件配置 (HardwareConfigData)

**用途**: 控制硬件设备的限位和步进电机参数

**主要参数**:
- `xy_radius_limit`: XY轴限位半径 (100-3000)
- `z_radius_limit`: Z轴限位 (100-3000)
- `x_step_dist`: X轴单脉冲移动距离 (1-100)
- `y_step_dist`: Y轴单脉冲移动距离 (1-100)
- `z_step_dist`: Z轴单脉冲移动距离 (1-100)

**配置文件示例**:
```ini
; Hardware 配置文件
; 版本: 1.0.0
; 描述: 硬件控制配置，包含轴限位和步进电机参数

[limits]
; XY轴限位半径 (100-3000) (uint32_t)
xy_radius_limit=1000
; Z轴限位 (100-3000) (uint32_t)
z_radius_limit=1000

[step_motor]
; X轴单脉冲移动距离 (1-100) (uint8_t)
x_step_dist=10
; Y轴单脉冲移动距离 (1-100) (uint8_t)
y_step_dist=10
; Z轴单脉冲移动距离 (1-100) (uint8_t)
z_step_dist=10
```

### 3. 调节流程配置 (AdjustProcessConfigData)

**用途**: 控制光路调节流程的业务逻辑参数

**主要参数**:
- `facula_ok_times`: 光斑判定次数 (1-10)
- `solidify_time`: 固化时间 (1-60秒)
- `exception_retry_times`: 异常重试次数 (0-5)
- `auto_save_enabled`: 自动保存开关

**配置文件示例**:
```ini
; AdjustProcess 配置文件
; 版本: 1.0.0
; 描述: 调节流程配置，包含光斑判定和异常处理参数

[General]
; 光斑判定次数 (1-10) (uint8_t)
facula_ok_times=3
; 固化时间(秒) (1-60) (uint8_t)
solidify_time=5
; 异常重试次数 (0-5) (uint8_t)
exception_retry_times=2
; 自动保存开关 (bool)
auto_save_enabled=true
```

## API 使用指南

### 1. 基本使用流程

```cpp
#include "components/configModule/DynamicConfigManager.h"

// 1. 获取配置管理器实例
Config::DynamicConfigManager& manager = Config::DynamicConfigManager::getInstance();

// 2. 注册配置类型
bool success = manager.registerConfig("Algorithm");
if (!success) {
    qDebug() << "配置注册失败";
    return;
}

// 3. 加载配置
Config::ConfigResult result = manager.loadConfig("Algorithm");
if (!result.success) {
    qDebug() << "配置加载失败:" << result.message;
    return;
}

// 4. 获取配置对象
auto* config = manager.getConfig<Algorithm::AlgorithmConfigData>("Algorithm");
if (config) {
    // 使用配置
    uint8_t interpolationType = config->getInterpolationType();
    config->setThresholdValue(150);
}

// 5. 保存配置
Config::ConfigResult saveResult = manager.saveConfig("Algorithm");
if (saveResult.success) {
    qDebug() << "配置保存成功";
}
```

### 2. 配置验证

```cpp
// 验证单个配置
auto* config = manager.getConfig<Hardware::HardwareConfigData>("Hardware");
if (config && config->isValid()) {
    qDebug() << "硬件配置有效";
} else {
    qDebug() << "硬件配置无效";
}

// 验证所有配置
Config::ConfigResult validateResult = manager.validateAllConfigs();
if (validateResult.success) {
    qDebug() << "所有配置验证通过";
} else {
    qDebug() << "配置验证失败:" << validateResult.message;
}
```

### 3. 配置文件路径管理

```cpp
// 设置自定义配置目录
manager.setConfigDirectory("/custom/config/path/");

// 获取配置文件路径
QString configPath = manager.getConfigFilePath("Algorithm");
qDebug() << "算法配置文件路径:" << configPath;

// 检查配置文件是否存在
if (QFile::exists(configPath)) {
    qDebug() << "配置文件存在";
}
```

### 4. 批量操作

```cpp
// 初始化所有配置
Config::ConfigResult initResult = manager.initializeAllConfigs();
if (initResult.success) {
    qDebug() << "所有配置初始化成功";
}

// 保存所有配置
Config::ConfigResult saveAllResult = manager.saveAllConfigs();
if (saveAllResult.success) {
    qDebug() << "所有配置保存成功";
}

// 获取已注册的配置类型
QStringList registeredTypes = Config::ConfigTypeRegistry::getInstance().getRegisteredTypes();
qDebug() << "已注册的配置类型:" << registeredTypes;
```

## 错误处理

### 错误类型

配置系统定义了以下错误类型：

```cpp
enum class ErrorType {
    None,                    // 无错误
    FileNotFound,           // 文件未找到
    FileReadError,          // 文件读取错误
    FileWriteError,         // 文件写入错误
    PermissionError,        // 权限错误
    ValidationError,        // 验证错误
    TypeNotRegistered,      // 类型未注册
    ConfigNotFound,         // 配置未找到
    UnknownError           // 未知错误
};
```

### 错误处理示例

```cpp
Config::ConfigResult result = manager.loadConfig("Algorithm");
if (!result.success) {
    switch (result.errorType) {
        case Config::ErrorType::FileNotFound:
            qDebug() << "配置文件不存在，将创建默认配置";
            manager.initializeConfig("Algorithm");
            break;
        case Config::ErrorType::ValidationError:
            qDebug() << "配置验证失败:" << result.message;
            // 重置为默认值
            break;
        case Config::ErrorType::TypeNotRegistered:
            qDebug() << "配置类型未注册，请先注册";
            manager.registerConfig("Algorithm");
            break;
        default:
            qDebug() << "未知错误:" << result.message;
            break;
    }
}
```

## 配置文件格式

### INI 格式规范

1. **注释**: 使用分号(;)开头的行作为注释
2. **分组**: 使用方括号[]定义配置分组
3. **键值对**: 使用等号(=)分隔键和值
4. **数据类型**:
   - 字符串: 直接写入，无需引号
   - 数字: 整数或浮点数
   - 布尔值: true/false

### 文件结构

```ini
; 配置文件头部注释
; 版本: x.x.x
; 描述: 配置说明

[分组名称]
; 参数注释 (数据类型)
参数名=参数值

[另一个分组]
; 更多参数...
```

## 最佳实践

### 1. 配置注册

```cpp
// 在应用程序启动时注册所有配置类型
void initializeConfigs() {
    Config::DynamicConfigManager& manager = Config::DynamicConfigManager::getInstance();

    QStringList configTypes = {"Algorithm", "Hardware", "AdjustProcess"};
    for (const QString& type : configTypes) {
        if (!manager.registerConfig(type)) {
            qWarning() << "Failed to register config type:" << type;
        }
    }
}
```

### 2. 配置验证

```cpp
// 在使用配置前进行验证
template<typename T>
T* getValidatedConfig(const QString& typeName) {
    Config::DynamicConfigManager& manager = Config::DynamicConfigManager::getInstance();

    T* config = manager.getConfig<T>(typeName);
    if (!config) {
        qWarning() << "Config not found:" << typeName;
        return nullptr;
    }

    if (!config->isValid()) {
        qWarning() << "Config validation failed:" << typeName;
        // 可以选择重置为默认值
        config->resetToDefaults();
    }

    return config;
}
```

### 3. 配置备份

```cpp
// 在修改配置前创建备份
void backupConfig(const QString& typeName) {
    Config::DynamicConfigManager& manager = Config::DynamicConfigManager::getInstance();

    QString configPath = manager.getConfigFilePath(typeName);
    QString backupPath = configPath + ".backup";

    QFile::copy(configPath, backupPath);
}
```

### 4. 线程安全

```cpp
// 在多线程环境中使用配置
class ThreadSafeConfigAccess {
private:
    QMutex m_mutex;
    Config::DynamicConfigManager& m_manager;

public:
    ThreadSafeConfigAccess() : m_manager(Config::DynamicConfigManager::getInstance()) {}

    template<typename T>
    T getConfigValue(const QString& typeName, std::function<T(const IConfigData*)> getter) {
        QMutexLocker locker(&m_mutex);
        auto* config = m_manager.getConfig<IConfigData>(typeName);
        return config ? getter(config) : T{};
    }
};
```

## 故障排除

### 常见问题

#### 1. 配置文件不存在

**问题**: 程序启动时提示配置文件不存在

**解决方案**:
```cpp
// 自动创建默认配置文件
Config::ConfigResult result = manager.initializeConfig("Algorithm");
if (result.success) {
    qDebug() << "默认配置文件已创建";
}
```

#### 2. 配置验证失败

**问题**: 配置参数超出有效范围

**解决方案**:
```cpp
// 检查并修正配置值
auto* config = manager.getConfig<Hardware::HardwareConfigData>("Hardware");
if (config) {
    if (config->getXYRadiusLimit() > 3000) {
        config->setXYRadiusLimit(3000);  // 设置为最大值
    }
    if (config->getXYRadiusLimit() < 100) {
        config->setXYRadiusLimit(100);   // 设置为最小值
    }
}
```

#### 3. 权限错误

**问题**: 无法写入配置文件

**解决方案**:
- 检查配置目录的写入权限
- 以管理员权限运行程序
- 更改配置目录到用户可写位置

#### 4. 内存泄漏

**问题**: 配置对象未正确释放

**解决方案**:
```cpp
// 在程序退出时清理配置
void cleanupConfigs() {
    Config::DynamicConfigManager& manager = Config::DynamicConfigManager::getInstance();
    manager.clearAllConfigs();
}
```

### 调试技巧

#### 1. 启用详细日志

```cpp
// 在main函数中启用配置系统日志
QLoggingCategory::setFilterRules("config.debug=true");
```

#### 2. 配置状态检查

```cpp
// 检查配置系统状态
void checkConfigStatus() {
    Config::DynamicConfigManager& manager = Config::DynamicConfigManager::getInstance();

    qDebug() << "已注册配置数量:" << manager.getRegisteredConfigCount();
    qDebug() << "配置目录:" << manager.getConfigDirectory();

    QStringList types = Config::ConfigTypeRegistry::getInstance().getRegisteredTypes();
    for (const QString& type : types) {
        bool loaded = manager.isConfigLoaded(type);
        qDebug() << QString("配置 %1: %2").arg(type).arg(loaded ? "已加载" : "未加载");
    }
}
```

## 性能优化

### 1. 延迟加载

```cpp
// 只在需要时加载配置
class LazyConfigLoader {
private:
    QHash<QString, bool> m_loadedConfigs;

public:
    template<typename T>
    T* getConfig(const QString& typeName) {
        if (!m_loadedConfigs.value(typeName, false)) {
            Config::DynamicConfigManager::getInstance().loadConfig(typeName);
            m_loadedConfigs[typeName] = true;
        }
        return Config::DynamicConfigManager::getInstance().getConfig<T>(typeName);
    }
};
```

### 2. 配置缓存

```cpp
// 缓存常用配置值
class ConfigCache {
private:
    QHash<QString, QVariant> m_cache;
    QTimer m_refreshTimer;

public:
    ConfigCache() {
        m_refreshTimer.setInterval(5000);  // 5秒刷新一次
        connect(&m_refreshTimer, &QTimer::timeout, this, &ConfigCache::refreshCache);
        m_refreshTimer.start();
    }

    QVariant getCachedValue(const QString& key) {
        return m_cache.value(key);
    }

private slots:
    void refreshCache() {
        // 刷新缓存逻辑
    }
};
```

## 总结

激光工具配置系统提供了以下核心功能：

1. **统一的配置管理**: 通过DynamicConfigManager统一管理所有配置类型
2. **类型安全**: 使用模板和强类型确保配置访问的安全性
3. **文件格式支持**: 支持INI格式，易于阅读和编辑
4. **验证机制**: 内置配置验证，确保参数有效性
5. **错误处理**: 完善的错误处理和恢复机制
6. **扩展性**: 易于添加新的配置类型

### 关键优势

- **简单易用**: API设计简洁，易于理解和使用
- **高性能**: 优化的加载和缓存机制
- **可靠性**: 完善的错误处理和数据验证
- **可维护性**: 清晰的架构和良好的代码组织

### 未来改进

1. 支持配置热重载
2. 添加配置变更通知机制
3. 支持更多文件格式（JSON、YAML）
4. 增强配置版本管理
5. 添加配置导入/导出功能

---

*本文档版本: 1.0.0*
*最后更新: 2025-07-20*