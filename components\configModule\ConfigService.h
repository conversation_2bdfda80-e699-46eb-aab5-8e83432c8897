#pragma once

#include "ConfigTypes.h"
#include "DynamicConfigManager.h"
#include "IConfigData.h"
#include "IConfigProvider.h"
#include <QMap>
#include <QMutex>
#include <QObject>
#include <map>
#include <memory>

namespace Config {

/**
 * @brief 配置服务中心
 *
 * 作为配置系统的核心协调者，负责：
 * - 管理所有模块的配置提供者
 * - 提供统一的配置访问接口
 * - 处理配置变更事件
 * - 维护配置服务的生命周期
 *
 * 设计特点：
 * - 单例模式，全局唯一
 * - 线程安全
 * - 支持动态注册和注销
 * - 事件驱动的配置变更通知
 */
class ConfigService : public QObject {
    Q_OBJECT

  public:
    /**
     * @brief 获取配置服务实例
     * @return 配置服务单例
     */
    static ConfigService &getInstance();

    /**
     * @brief 注册配置提供者
     * @param provider 配置提供者实例
     * @return 注册是否成功
     */
    bool registerProvider(std::unique_ptr<IConfigProvider> provider);

    /**
     * @brief 注销配置提供者
     * @param moduleName 模块名称
     * @return 注销是否成功
     */
    bool unregisterProvider(const QString &moduleName);

    /**
     * @brief 获取配置提供者
     * @param moduleName 模块名称
     * @return 配置提供者指针，如果不存在返回nullptr
     */
    IConfigProvider *getProvider(const QString &moduleName);

    /**
     * @brief 获取动态配置管理器
     * @return 动态配置管理器引用
     */
    DynamicConfigManager &getDynamicManager() {
        return m_dynamicManager;
    }

    /**
     * @brief 注册配置类型（模板方法）
     * @tparam T 配置类型
     * @param typeName 配置类型名称
     * @return 注册是否成功
     */
    template <typename T> bool registerConfig(const QString &typeName) {
        return m_dynamicManager.registerConfig(typeName);
    }

    /**
     * @brief 获取配置对象（模板方法）
     * @tparam T 配置类型
     * @param typeName 配置类型名称
     * @return 配置对象指针，失败时返回nullptr
     */
    template <typename T> T *getConfig(const QString &typeName) {
        return m_dynamicManager.getConfig<T>(typeName);
    }

    /**
     * @brief 获取配置对象（通用方法）
     * @param typeName 配置类型名称
     * @return 配置对象指针，失败时返回nullptr
     */
    IConfigData *getConfig(const QString &typeName);

    /**
     * @brief 获取所有已注册的模块名称
     * @return 模块名称列表
     */
    QStringList getRegisteredModules() const;

    /**
     * @brief 检查模块是否已注册
     * @param moduleName 模块名称
     * @return 是否已注册
     */
    bool isModuleRegistered(const QString &moduleName) const;

    /**
     * @brief 获取配置参数
     * @param moduleName 模块名称
     * @param key 参数名
     * @param defaultValue 默认值
     * @return 参数值
     */
    QVariant getParameter(const QString &moduleName, const QString &key, const QVariant &defaultValue = QVariant());

    /**
     * @brief 设置配置参数
     * @param moduleName 模块名称
     * @param key 参数名
     * @param value 参数值
     * @return 设置是否成功
     */
    bool setParameter(const QString &moduleName, const QString &key, const QVariant &value);

    /**
     * @brief 获取模块的所有配置参数
     * @param moduleName 模块名称
     * @return 参数映射表
     */
    QVariantMap getModuleParameters(const QString &moduleName);

    /**
     * @brief 加载所有模块的配置
     * @return 加载结果
     */
    ConfigResult loadAllConfigs();

    /**
     * @brief 保存所有模块的配置
     * @return 保存结果
     */
    ConfigResult saveAllConfigs();

    /**
     * @brief 验证所有模块的配置
     * @return 验证结果
     */
    ConfigResult validateAllConfigs();

    /**
     * @brief 重新加载指定模块的配置
     * @param moduleName 模块名称
     * @return 重新加载结果
     */
    ConfigResult reloadModuleConfig(const QString &moduleName);

    /**
     * @brief 重置指定模块为默认配置
     * @param moduleName 模块名称
     * @return 重置结果
     */
    ConfigResult resetModuleToDefault(const QString &moduleName);

    /**
     * @brief 获取配置服务状态信息
     * @return 状态信息
     */
    QVariantMap getServiceStatus() const;

  Q_SIGNALS:
    /**
     * @brief 模块注册信号
     * @param moduleName 模块名称
     */
    void moduleRegistered(const QString &moduleName);

    /**
     * @brief 模块注销信号
     * @param moduleName 模块名称
     */
    void moduleUnregistered(const QString &moduleName);

    /**
     * @brief 配置变更信号
     * @param moduleName 模块名称
     * @param key 参数名
     * @param oldValue 旧值
     * @param newValue 新值
     */
    void configChanged(const QString &moduleName, const QString &key, const QVariant &oldValue, const QVariant &newValue);

    /**
     * @brief 服务状态变更信号
     * @param status 新状态
     */
    void serviceStatusChanged(const QString &status);

  private Q_SLOTS:
    /**
     * @brief 处理提供者配置变更
     * @param moduleName 模块名称
     * @param key 参数名
     * @param oldValue 旧值
     * @param newValue 新值
     */
    void onProviderConfigChanged(const QString &moduleName, const QString &key, const QVariant &oldValue, const QVariant &newValue);

  private:
    ConfigService(QObject *parent = nullptr);
    ~ConfigService() override;

    // 禁用拷贝构造和赋值
    ConfigService(const ConfigService &) = delete;
    ConfigService &operator=(const ConfigService &) = delete;

    /**
     * @brief 初始化服务
     */
    void initialize();

    /**
     * @brief 连接提供者信号
     * @param provider 配置提供者
     */
    void connectProviderSignals(IConfigProvider *provider);

    /**
     * @brief 断开提供者信号
     * @param provider 配置提供者
     */
    void disconnectProviderSignals(IConfigProvider *provider);

  private:
    std::map<QString, std::unique_ptr<IConfigProvider>> m_providers;       // 配置提供者映射
    DynamicConfigManager                                m_dynamicManager;  // 动态配置管理器
    mutable QMutex                                      m_mutex;           // 线程安全锁
    bool                                                m_initialized;     // 初始化标志
};

}  // namespace Config
