# 硬件控制模块开发文档

## 模块概述

硬件控制模块是激光工具系统的底层控制模块，负责步进电机控制、轴限位管理、硬件安全保护等关键功能。

### 模块职责

1. **步进电机控制** - 控制X、Y、Z轴步进电机的精确运动
2. **轴限位管理** - 管理各轴的运动限位和安全边界
3. **硬件安全保护** - 实施硬件安全保护机制
4. **运动参数配置** - 管理运动控制相关的配置参数
5. **状态监控** - 监控硬件设备的运行状态

### 设计原则

1. **安全第一** - 确保硬件操作的安全性
2. **精确控制** - 提供精确的运动控制能力
3. **参数化配置** - 支持灵活的硬件参数配置
4. **实时监控** - 实时监控硬件状态
5. **故障保护** - 完善的故障检测和保护机制

## 架构设计

### 模块结构

```
machine/
├── config/
│   ├── HardwareConfigData.h/.cpp       # 硬件配置数据类
│   └── MachineModule.h/.cpp            # 模块初始化
├── motion/
│   ├── StepperMotor.h/.cpp             # 步进电机控制
│   ├── AxisController.h/.cpp           # 轴控制器
│   └── MotionPlanner.h/.cpp            # 运动规划器
├── safety/
│   ├── LimitSwitch.h/.cpp              # 限位开关
│   ├── SafetyMonitor.h/.cpp            # 安全监控
│   └── EmergencyStop.h/.cpp            # 紧急停止
└── communication/
    ├── SerialInterface.h/.cpp          # 串口通信
    └── ProtocolHandler.h/.cpp          # 协议处理
```

### 配置管理架构

```mermaid
graph TB
    subgraph "Machine模块"
        HCD[HardwareConfigData<br/>硬件配置数据]
        MM[MachineModule<br/>模块管理器]
        SM[StepperMotor<br/>步进电机]
        AC[AxisController<br/>轴控制器]
        MP[MotionPlanner<br/>运动规划器]
        SMon[SafetyMonitor<br/>安全监控]
    end
    
    subgraph "Config服务"
        CS[ConfigService<br/>配置服务]
        DCM[DynamicConfigManager<br/>动态管理器]
    end
    
    HCD --> CS
    MM --> HCD
    SM --> HCD
    AC --> HCD
    MP --> HCD
    SMon --> HCD
    
    CS --> DCM
    
    style HCD fill:#e1f5fe
    style MM fill:#f3e5f5
    style SMon fill:#ffcccb
```

## 配置数据设计

### HardwareConfigData类

```cpp
namespace Machine {
    class HardwareConfigData : public Config::BaseConfigData<HardwareConfigData> {
    public:
        // 轴限位参数
        uint32_t xy_radius_limit;  // XY轴限位半径 (100-3000)
        uint32_t z_radius_limit;   // Z轴限位 (100-3000)
        
        // 步进电机参数
        uint8_t  x_step_dist;      // X单脉冲移动距离 (1-100)
        uint8_t  y_step_dist;      // Y单脉冲移动距离 (1-100)
        uint8_t  z_step_dist;      // Z单脉冲移动距离 (1-100)
        
        // 接口方法
        static QString staticTypeName() { return "Hardware"; }
        QString getTypeName() const override;
        QVariantMap toVariantMap() const override;
        bool fromVariantMap(const QVariantMap &data) override;
        bool validate() const override;
        void setDefaults() override;
        
        // 业务方法
        uint32_t getXYRadiusLimit() const;
        void setXYRadiusLimit(uint32_t limit);
        uint32_t getZRadiusLimit() const;
        void setZRadiusLimit(uint32_t limit);
        uint8_t getXStepDist() const;
        void setXStepDist(uint8_t dist);
        uint8_t getYStepDist() const;
        void setYStepDist(uint8_t dist);
        uint8_t getZStepDist() const;
        void setZStepDist(uint8_t dist);
        bool isPositionInXYLimit(int x, int y) const;
        bool isZPositionInLimit(int z) const;
    };
}
```

### 配置参数详解

#### 1. 轴限位参数
- **xy_radius_limit**: XY轴限位半径 (100-3000)，默认1500
- **z_radius_limit**: Z轴限位 (100-3000)，默认1400

#### 2. 步进电机参数
- **x_step_dist**: X轴单脉冲移动距离 (1-100)，默认10
- **y_step_dist**: Y轴单脉冲移动距离 (1-100)，默认10
- **z_step_dist**: Z轴单脉冲移动距离 (1-100)，默认10

## 模块初始化

### 配置注册

```cpp
// machine/config/MachineModule.cpp
namespace Machine {
    void MachineModule::initialize() {
        // 配置会通过REGISTER_CONFIG_TYPE宏自动注册
        qDebug() << "Machine module initialized with config registration";
        
        // 验证配置注册
        auto &registry = Config::ConfigTypeRegistry::getInstance();
        if (registry.isRegistered("Hardware")) {
            qDebug() << "✅ Hardware config registered successfully";
        } else {
            qCritical() << "❌ Hardware config registration failed";
        }
        
        // 初始化硬件组件
        initializeHardwareComponents();
        
        // 启动安全监控
        startSafetyMonitoring();
    }
    
    void MachineModule::initializeHardwareComponents() {
        // 初始化步进电机
        StepperMotor::getInstance().initialize();
        
        // 初始化轴控制器
        AxisController::getInstance().initialize();
        
        // 初始化运动规划器
        MotionPlanner::getInstance().initialize();
    }
    
    void MachineModule::startSafetyMonitoring() {
        // 启动安全监控
        SafetyMonitor::getInstance().start();
    }
}
```

### 自动注册机制

```cpp
// 在HardwareConfigData.h文件末尾
REGISTER_CONFIG_TYPE(Machine::HardwareConfigData, "Hardware", "1.0.0", 
                    "硬件控制配置，包含轴限位和步进电机参数");
```

## 使用指南

### 获取硬件配置

```cpp
// 在硬件控制相关代码中
auto* hardwareConfig = CONFIG_SERVICE().getConfig<Machine::HardwareConfigData>("Hardware");

if (hardwareConfig) {
    // 读取限位参数
    uint32_t xyLimit = hardwareConfig->getXYRadiusLimit();
    uint32_t zLimit = hardwareConfig->getZRadiusLimit();
    
    // 读取步进参数
    uint8_t xStep = hardwareConfig->getXStepDist();
    uint8_t yStep = hardwareConfig->getYStepDist();
    uint8_t zStep = hardwareConfig->getZStepDist();
    
    qDebug() << "硬件配置:";
    qDebug() << "  XY限位半径:" << xyLimit;
    qDebug() << "  Z轴限位:" << zLimit;
    qDebug() << "  X步进距离:" << xStep;
    qDebug() << "  Y步进距离:" << yStep;
    qDebug() << "  Z步进距离:" << zStep;
}
```

### 修改硬件配置

```cpp
// 修改限位参数
hardwareConfig->setXYRadiusLimit(2000);    // 设置XY限位为2000
hardwareConfig->setZRadiusLimit(1800);     // 设置Z限位为1800

// 修改步进参数
hardwareConfig->setXStepDist(5);           // 设置X步进距离为5
hardwareConfig->setYStepDist(5);           // 设置Y步进距离为5
hardwareConfig->setZStepDist(8);           // 设置Z步进距离为8

// 验证配置
if (hardwareConfig->validate()) {
    // 保存配置
    CONFIG_SERVICE().saveConfig("Hardware");
    qDebug() << "硬件配置已更新并保存";
} else {
    qWarning() << "硬件配置验证失败";
}
```

### 在运动控制中使用配置

```cpp
// AxisController类中的运动控制
void AxisController::moveToPosition(int x, int y, int z) {
    auto* config = CONFIG_SERVICE().getConfig<Machine::HardwareConfigData>("Hardware");
    if (!config) {
        qCritical() << "无法获取硬件配置";
        return;
    }
    
    // 检查位置是否在限位内
    if (!config->isPositionInXYLimit(x, y)) {
        qWarning() << "目标位置超出XY限位:" << x << "," << y;
        return;
    }
    
    if (!config->isZPositionInLimit(z)) {
        qWarning() << "目标位置超出Z限位:" << z;
        return;
    }
    
    // 获取步进参数
    uint8_t xStep = config->getXStepDist();
    uint8_t yStep = config->getYStepDist();
    uint8_t zStep = config->getZStepDist();
    
    // 计算步进数
    int xSteps = x / xStep;
    int ySteps = y / yStep;
    int zSteps = z / zStep;
    
    // 执行运动
    executeMotion(xSteps, ySteps, zSteps);
}

void AxisController::executeMotion(int xSteps, int ySteps, int zSteps) {
    qDebug() << "执行运动:";
    qDebug() << "  X轴步数:" << xSteps;
    qDebug() << "  Y轴步数:" << ySteps;
    qDebug() << "  Z轴步数:" << zSteps;
    
    // 发送运动指令到步进电机
    StepperMotor::getInstance().moveX(xSteps);
    StepperMotor::getInstance().moveY(ySteps);
    StepperMotor::getInstance().moveZ(zSteps);
}
```

### 安全检查

```cpp
// SafetyMonitor类中的安全检查
void SafetyMonitor::checkSafety() {
    auto* config = CONFIG_SERVICE().getConfig<Machine::HardwareConfigData>("Hardware");
    if (!config) {
        return;
    }
    
    // 获取当前位置
    Position currentPos = getCurrentPosition();
    
    // 检查是否超出限位
    if (!config->isPositionInXYLimit(currentPos.x, currentPos.y)) {
        qCritical() << "当前位置超出XY限位，触发紧急停止";
        EmergencyStop::getInstance().trigger();
        return;
    }
    
    if (!config->isZPositionInLimit(currentPos.z)) {
        qCritical() << "当前位置超出Z限位，触发紧急停止";
        EmergencyStop::getInstance().trigger();
        return;
    }
    
    qDebug() << "安全检查通过";
}
```

## 配置文件格式

### JSON配置文件结构

```json
{
    "_type": "Hardware",
    "_version": "1.0.0",
    "xy_radius_limit": 1500,
    "z_radius_limit": 1400,
    "x_step_dist": 10,
    "y_step_dist": 10,
    "z_step_dist": 10
}
```

### 配置文件位置

- **默认路径**: `config/modules/hardware/hardware_config.json`
- **备份路径**: `config/backup/hardware/`
- **模板路径**: `config/templates/hardware_template.json`

## 安全机制

### 限位保护

```cpp
bool HardwareConfigData::isPositionInXYLimit(int x, int y) const {
    double distance = sqrt(x * x + y * y);
    return distance <= xy_radius_limit;
}

bool HardwareConfigData::isZPositionInLimit(int z) const {
    return abs(z) <= static_cast<int>(z_radius_limit);
}
```

### 紧急停止

```cpp
class EmergencyStop {
public:
    static EmergencyStop& getInstance();
    
    void trigger() {
        qCritical() << "紧急停止触发！";
        
        // 停止所有运动
        StepperMotor::getInstance().stopAll();
        
        // 发送停止信号
        Q_EMIT emergencyStopTriggered();
        
        // 记录日志
        logEmergencyStop();
    }
    
Q_SIGNALS:
    void emergencyStopTriggered();
    
private:
    void logEmergencyStop();
};
```

## 测试方案

### 配置测试

```cpp
#ifdef ENABLE_CONFIG_TESTS
void testHardwareConfig() {
    auto* config = CONFIG_SERVICE().getConfig<Machine::HardwareConfigData>("Hardware");
    
    // 测试限位设置
    config->setXYRadiusLimit(2000);
    QCOMPARE(config->getXYRadiusLimit(), 2000u);
    
    config->setZRadiusLimit(1800);
    QCOMPARE(config->getZRadiusLimit(), 1800u);
    
    // 测试步进设置
    config->setXStepDist(5);
    QCOMPARE(config->getXStepDist(), 5);
    
    // 测试位置检查
    QVERIFY(config->isPositionInXYLimit(1000, 1000));
    QVERIFY(!config->isPositionInXYLimit(3000, 3000));
    
    // 测试配置验证
    QVERIFY(config->validate());
}
#endif
```

### 运动控制测试

```cpp
#ifdef ENABLE_CONFIG_MOCK_SIGNALS
void testMotionControl() {
    // 模拟运动控制
    auto* config = CONFIG_SERVICE().getConfig<Machine::HardwareConfigData>("Hardware");
    
    // 设置测试参数
    config->setXYRadiusLimit(1000);
    config->setZRadiusLimit(800);
    config->setXStepDist(5);
    config->setYStepDist(5);
    config->setZStepDist(5);
    
    // 模拟运动控制
    AxisController controller;
    
    // 使用QTimer模拟异步运动
    QTimer::singleShot(100, [&]() {
        controller.moveToPosition(500, 500, 400);
    });
}
#endif
```

## 性能考虑

1. **实时性** - 确保运动控制的实时响应
2. **精度** - 保证步进控制的精确性
3. **安全性** - 优先考虑安全保护机制
4. **稳定性** - 确保长时间运行的稳定性

## 维护指南

1. **定期校准步进电机参数**
2. **检查限位开关的工作状态**
3. **监控硬件设备的运行状态**
4. **备份重要的配置参数**
5. **更新安全保护机制**

硬件控制模块通过模块化的配置管理，为系统提供了安全可靠的硬件控制能力。
