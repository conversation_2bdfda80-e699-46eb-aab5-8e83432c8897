#pragma once

#include "../../components/config/BaseConfigProvider.h"
#include <QPoint>
#include <QVector>

namespace Facula {

/**
 * @brief 光斑配置提供者
 * 
 * 专门负责光斑检测和处理相关的配置管理
 * 配置结构完全在模块内部定义，不依赖外部配置类型
 * 
 * 职责：
 * - 管理光斑中心检测配置
 * - 管理光斑处理算法类型
 * - 提供光斑配置的访问接口
 * - 处理光斑相关的参数验证
 */
class FaculaConfigProvider : public Config::BaseConfigProvider {
    Q_OBJECT

public:
    explicit FaculaConfigProvider(QObject *parent = nullptr);
    ~FaculaConfigProvider() override = default;

    // IConfigProvider 接口实现
    QString getModuleName() const override { return "Facula"; }
    QString getModuleVersion() const override { return "1.0.0"; }
    QString getConfigFilePath() const override;

    /**
     * @brief 获取光斑中心通道配置
     * @return 通道配置字符串
     */
    QString getFaculaCenterChannels() const;

    /**
     * @brief 设置光斑中心通道配置
     * @param channels 通道配置字符串，格式如 "2,2;3,3"
     */
    void setFaculaCenterChannels(const QString &channels);

    /**
     * @brief 获取光斑中心坐标点列表
     * @return 坐标点列表
     */
    QVector<QPoint> getFaculaCenterPoints() const;

    /**
     * @brief 获取光斑峰值阈值
     * @return 峰值阈值
     */
    uint32_t getPeakThreshold() const;

    /**
     * @brief 设置光斑峰值阈值
     * @param threshold 峰值阈值
     */
    void setPeakThreshold(uint32_t threshold);

    /**
     * @brief 获取光斑处理类型
     * @return 处理类型：0-基础，1-增强，2-高精度
     */
    uint8_t getHandleType() const;

    /**
     * @brief 设置光斑处理类型
     * @param handleType 处理类型：0-基础，1-增强，2-高精度
     */
    void setHandleType(uint8_t handleType);

    /**
     * @brief 获取光斑中心位置X坐标（兼容性）
     * @return X坐标
     */
    uint8_t getFaculaCenterLocX() const;

    /**
     * @brief 获取光斑中心位置Y坐标（兼容性）
     * @return Y坐标
     */
    uint8_t getFaculaCenterLocY() const;

protected:
    // BaseConfigProvider 接口实现
    void loadDefaultParameters() override;
    Config::ConfigResult validateParameter(const QString &key, const QVariant &value) const override;
    QString getParameterRange(const QString &key) const override;
    void onParameterChanged(const QString &key, const QVariant &oldValue, const QVariant &newValue) override;

private:
    /**
     * @brief 解析通道配置字符串
     * @param channels 通道配置字符串
     * @return 坐标点列表
     */
    QVector<QPoint> parseChannels(const QString &channels) const;

    /**
     * @brief 更新兼容性坐标
     * 当通道配置变更时，更新单点坐标以保持向后兼容
     */
    void updateCompatibilityCoordinates();

    /**
     * @brief 验证通道配置格式
     * @param channels 通道配置字符串
     * @return 是否有效
     */
    bool isValidChannelFormat(const QString &channels) const;

    /**
     * @brief 验证峰值阈值范围
     * @param threshold 峰值阈值
     * @return 是否有效
     */
    bool isValidThreshold(uint32_t threshold) const;

    /**
     * @brief 验证处理类型
     * @param handleType 处理类型
     * @return 是否有效
     */
    bool isValidHandleType(uint8_t handleType) const;

private:
    // 配置参数键名常量
    static const QString KEY_FACULA_CENTER_CHANNELS;
    static const QString KEY_FACULA_CENTER_PEAK_THRESHOLD;
    static const QString KEY_FACULA_CENTER_LOC_X;
    static const QString KEY_FACULA_CENTER_LOC_Y;
    static const QString KEY_FACULA_HANDLE_TYPE;

    // 默认值常量
    static const QString DEFAULT_CHANNELS;
    static const uint32_t DEFAULT_PEAK_THRESHOLD;
    static const uint8_t DEFAULT_LOC_X;
    static const uint8_t DEFAULT_LOC_Y;
    static const uint8_t DEFAULT_HANDLE_TYPE;

    // 验证范围常量
    static const uint32_t MIN_PEAK_THRESHOLD;
    static const uint32_t MAX_PEAK_THRESHOLD;
    static const uint8_t MAX_HANDLE_TYPE;
};

} // namespace Facula
