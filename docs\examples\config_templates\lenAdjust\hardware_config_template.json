{"_comment": "Hardware configuration for lens adjustment module", "_version": "1.0", "_description": "Configuration for lens adjustment hardware devices and motion parameters", "hardware": {"_comment": "Hardware configuration for lens adjustment devices", "xy_radius_limit": 1500, "z_radius_limit": 1400, "x_step_dist": 10, "y_step_dist": 10, "z_step_dist": 10, "_descriptions": {"xy_radius_limit": "XY axis movement radius limit in micrometers", "z_radius_limit": "Z axis movement limit in micrometers", "x_step_dist": "X axis single pulse movement distance", "y_step_dist": "Y axis single pulse movement distance", "z_step_dist": "Z axis single pulse movement distance"}}, "device_specific": {"_comment": "Device-specific configurations", "shunTuo": {"xy_radius_limit": 900, "z_radius_limit": 1000, "x_step_dist": 7, "y_step_dist": 7, "z_step_dist": 8}, "qingHe": {"xy_radius_limit": 1200, "z_radius_limit": 1200, "x_step_dist": 8, "y_step_dist": 8, "z_step_dist": 9}, "qingHeShiJue": {"xy_radius_limit": 1500, "z_radius_limit": 1400, "x_step_dist": 10, "y_step_dist": 10, "z_step_dist": 10}}, "communication": {"_comment": "Communication parameters", "buffer_size": 2000, "timeout_ms": 5000, "retry_count": 3}, "_migration_info": {"migrated_from": "hardcoded values in machine classes", "migration_date": "auto-generated", "original_files": ["clensMachineST.cpp", "clensMachineQH.cpp"]}}