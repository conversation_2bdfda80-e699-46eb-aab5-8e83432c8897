# 1.0
cmake_minimum_required(VERSION 3.5)

project(component LANGUAGES CXX)

# 1.2 启用当前头文件目录？是什么目录？
set(CMAKE_INCLUDE_CURRENT_DIR ON) #包含.h文件？

# 1.3 QT配置
set(CMAKE_AUTOUIC ON) #ui
set(CMAKE_AUTOMOC ON) #moc
set(CMAKE_AUTORCC ON) #rcc编译开关 用于资源文件

# 1.4
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 1.5 变量设置
set(SOURCE_FILE mywidget.cpp mywidget.ui)

#set(CLENS_ADJUST_DIR ${COMPONENTS_DIR}/clensAdjust)
#set(DBSHOW_DIR ${COMPONENTS_DIR}/dbShow)
#set(MOTOR_MONITOR_DIR ${COMPONENTS_DIR}/motorMonitor)
#set(TURN_ON_TIME_DIR ${COMPONENTS_DIR}/turnOnTime)
#set(ASSISTANT_DIR ${COMPONENTS_DIR}/assistant)
#set(CLOUD_DIR ${COMPONENTS_DIR}/pointCloud)

# 1.6 头文件和动态链接库
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR} )

#INCLUDE_DIRECTORIES(${COMPONENTS_DIR}) #找头文件
#INCLUDE_DIRECTORIES(${CLENS_ADJUST_DIR})
#INCLUDE_DIRECTORIES(${DBSHOW_DIR})
#INCLUDE_DIRECTORIES(${COMMUNICATION_DIR})
#INCLUDE_DIRECTORIES(${MOTOR_MONITOR_DIR})

#INCLUDE_DIRECTORIES(${TURN_ON_TIME_DIR})
#INCLUDE_DIRECTORIES(${ASSISTANT_DIR})
#INCLUDE_DIRECTORIES(${CLOUD_DIR})

LINK_DIRECTORIES(${PROJECT_SOURCE_DIR}/../lib) #找.so/.a库文件路径


#find_package(Qt5 COMPONENTS Core Gui Widgets Sql SerialPort PrintSupport Charts Network REQUIRED) #启用qrc资源文件

#1.7.2 分级目录添加
add_subdirectory(configModule)
add_subdirectory(motorMonitor)
add_subdirectory(lensAdjust)
add_subdirectory(lensAdjust_MEMD)
add_subdirectory(lensAdjust_rework)
#add_subdirectory(turnOnTime)
#add_subdirectory(dbShow)
add_subdirectory(assistant)
#add_subdirectory(pointCloud)
add_subdirectory(comCheck)
add_subdirectory(novaCalibration)

# 1.8 输出
ADD_LIBRARY(${PROJECT_NAME}_lib ${SOURCE_FILE})


# 1.9 链接库文件
target_link_libraries(
    ${PROJECT_NAME}_lib
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets

    ConfigModule
    motorMonitor_lib
    lensAdjust_lib
    lensAdjust_MEMD_lib
    lensAdjust_rework_lib
    comCheck_lib
    novaCalibration_lib
    database_lib
    mySql_lib
    assistant
    )


#turnOnTime comCheck assistant  turnOnTime dbShow assistant

