# 1.0
cmake_minimum_required(VERSION 3.5)

# 1.1 这个命令不是强制性的，但最好都加上。它会引入两个变量 demo_BINARY_DIR 和 demo_SOURCE_DIR，
#同时，cmake 自动定义了两个等价的变量 PROJECT_BINARY_DIR 和 PROJECT_SOURCE_DIR
project(LIDAR_IA LANGUAGES CXX)

# 1.2 启用当前头文件目录？是什么目录？
set(CMAKE_INCLUDE_CURRENT_DIR ON) #包含.h文件？

# 1.3 QT配置
set(CMAKE_AUTOUIC ON) #ui
set(CMAKE_AUTOMOC ON) #moc
set(CMAKE_AUTORCC ON) #rcc编译开关 用于资源文件

# 1.4
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 测试选项配置
option(ENABLE_CONFIG_TESTS "Enable configuration module tests" OFF)
option(ENABLE_CONFIG_MOCK_SIGNALS "Enable configuration mock signal tests" OFF)
option(ENABLE_CONFIG_VALIDATION "Enable configuration validation tests" OFF)

# 测试宏定义
if (ENABLE_CONFIG_TESTS)
    message(STATUS "ENABLE_CONFIG_TESTS ON")
    add_compile_definitions(ENABLE_CONFIG_TESTS)
endif()

if (ENABLE_CONFIG_MOCK_SIGNALS)
    message(STATUS "ENABLE_CONFIG_MOCK_SIGNALS ON")
    add_compile_definitions(ENABLE_CONFIG_MOCK_SIGNALS)
endif()

if (ENABLE_CONFIG_VALIDATION)
    message(STATUS "ENABLE_CONFIG_VALIDATION ON")
    add_compile_definitions(ENABLE_CONFIG_VALIDATION)
endif()

# QtCreator supports the following variables for Android, which are identical to qmake Android variables.
# Check http://doc.qt.io/qt-5/deployment-android.html for more information.
# They need to be set before the find_package(Qt5 ...) call.


# 1.5 变量配置
set(SOURCE_FILE main.cpp)
# set(LA_PROJECT_DIR ${PROJECT_SOURCE_DIR})
# set(COMPONENTS_DIR ${PROJECT_SOURCE_DIR}/components)
# set(LOGIN_DIR ${PROJECT_SOURCE_DIR}/account/login)
# set(REGISTER_DIR ${PROJECT_SOURCE_DIR}/account/register)
# set(DATABASE_DIR ${PROJECT_SOURCE_DIR}/database)
# set(PROCESS_DIR ${PROJECT_SOURCE_DIR}/processList)
# set(COMMUNICATION_DIR ${PROJECT_SOURCE_DIR}/communication)
# set(SENSOR_DIR ${PROJECT_SOURCE_DIR}/sensor)
# set(MACHINE_DIR ${PROJECT_SOURCE_DIR}/machine)
# set(THREAD_DIR ${PROJECT_SOURCE_DIR}/thread)
# set(DATA_DIR ${PROJECT_SOURCE_DIR}/data)
# set(SHOW_DIR ${PROJECT_SOURCE_DIR}/show)
# set(QTDEBUG_DIR ${PROJECT_SOURCE_DIR}/qtDebug)
# set(SAVE_LOAD_DIR ${PROJECT_SOURCE_DIR}/saveLoad)

# 1.5 指定Qt路径和启用当前目录(按需设置)，找QT模块用
#SET(CMAKE_PREFIX_PATH ${QT_PATH}/lib/cmake)
#SET(CMAKE_PREFIX_PATH <PREFIX_PATH>/lib/cmake) #PREFIX_PATH 是什么？

# 1.6 头文件和动态链接库
if(1)
macro(FIND_INCLUDE_DIR result curdir)  #定义函数,2个参数:存放结果result；指定路径curdir；
     file(GLOB_RECURSE children "${curdir}/*.hpp" "${curdir}/*.h" )	#遍历获取{curdir}中*.hpp和*.h文件列表
     message(STATUS "children= ${children}")	#打印*.hpp和*.h的文件列表
     set(dirlist "")	 #定义dirlist中间变量，并初始化
     foreach(child ${children}) #for循环
        string(REGEX REPLACE "(.*)/.*" "\\1" LIB_NAME ${child}) #字符串替换,用/前的字符替换/*h
        if(IS_DIRECTORY ${LIB_NAME}) #判断是否为路径
            LIST(APPEND dirlist ${LIB_NAME}) #将合法的路径加入dirlist变量中
        endif() #结束判断
      endforeach() #结束for循环
    set(${result} ${dirlist}) #dirlist结果放入result变量中
endmacro() #函数结束
FIND_INCLUDE_DIR(INCLUDE_DIR_LIST ${PROJECT_SOURCE_DIR})
INCLUDE_DIRECTORIES(${INCLUDE_DIR_LIST}) #找头文件

else()
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR}) #找头文件
INCLUDE_DIRECTORIES(${CMAKE_CURRENT_BINARY_DIR})
INCLUDE_DIRECTORIES(${COMPONENTS_DIR})
INCLUDE_DIRECTORIES(${LOGIN_DIR})
INCLUDE_DIRECTORIES(${REGISTER_DIR})
INCLUDE_DIRECTORIES(${QTDEBUG_DIR}/qtOpt)

endif()

LINK_DIRECTORIES(${PROJECT_SOURCE_DIR}/../lib) #找.so/.a库文件路径


find_package (
    Qt5
    COMPONENTS
    Core
    Gui
    Widgets
    Sql
    SerialPort
    PrintSupport
    Charts
    Network
    Xml
    REQUIRED
) #启用qrc资源文件

set(QRC_FILE resource.qrc)
qt5_add_resources(QRC ${QRC_FILE}) #用这个函数把这些QRC文件添加进来

############################################# 构建模式->输出配置 ################################
# 1. 初始化变量输出
#   设置构建模式
option(LOG_TO_CONSOLE "debug build type" ON)
option(INIT_OUTPUT "varibles init value ouput" ON)
option(COMM_OUTPUT "communication cmd output" ON)
option(COMM_ACK_OUTPUT "communication ack" ON)
option(PROCESS_STATUS_OUTPUT "task status output" ON)
option(PROCESS_DATA_OUTPUT "process data output" ON)

# 配置模块测试选项
option(ENABLE_CONFIG_TESTS "Enable configuration module tests" OFF)
option(ENABLE_CONFIG_MOCK_SIGNALS "Enable configuration mock signal tests" OFF)
option(ENABLE_CONFIG_VALIDATION "Enable configuration validation tests" OFF)
option(RESULT_CACHE_DATA "result detail data output" ON)
option(ERROR_TO_LOG "error log output" ON)

set(RELEASE_BUILD_TYPES "Release;MinSizeRel")
if(CMAKE_BUILD_TYPE IN_LIST RELEASE_BUILD_TYPES)
    set(BUILD_MODE "release")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3")

    set(LOG_TO_CONSOLE OFF)
    set(INIT_OUTPUT OFF) #变更构建方式，需要右键->清除所有项目
    set(COMM_OUTPUT OFF)
    set(COMM_ACK_OUTPUT OFF)
    set(PROCESS_STATUS_OUTPUT OFF)
    set(PROCESS_DATA_OUTPUT OFF)
    set(RESULT_CACHE_OUTPUT ON)

    set(ERROR_TO_LOG ON)

    ADD_DEFINITIONS(-DQT_MESSAGELOGCONTEXT) #QDebug在release下依然可以输出函数名和行号
    #ADD_DEFINITIONS(-DQT_NO_DEBUG_OUTPUT)  #全部qdebug关闭
else()
    set(BUILD_MODE "debug")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O0 -g") #禁止优化

    set(LOG_TO_CONSOLE ON)
    set(INIT_OUTPUT ON) #变更构建方式，需要右键->清除所有项目
    set(COMM_OUTPUT ON)
    set(COMM_ACK_OUTPUT ON)
    set(PROCESS_STATUS_OUTPUT ON)
    set(PROCESS_DATA_OUTPUT ON)
    set(RESULT_CACHE_OUTPUT ON)

    set(ERROR_TO_LOG ON)
endif()

# 1.0 Debug type
if (LOG_TO_CONSOLE)
    message(STATUS "LOG_TO_CONSOLE ON")
    add_compile_definitions(LOG_TO_CONSOLE)
else()
    message(STATUS "LOG_TO_CONSOLE OFF")
endif()

# 配置模块测试宏定义
if (ENABLE_CONFIG_TESTS)
    message(STATUS "ENABLE_CONFIG_TESTS ON")
    add_compile_definitions(ENABLE_CONFIG_TESTS)
else()
    message(STATUS "ENABLE_CONFIG_TESTS OFF")
endif()

if (ENABLE_CONFIG_MOCK_SIGNALS)
    message(STATUS "ENABLE_CONFIG_MOCK_SIGNALS ON")
    add_compile_definitions(ENABLE_CONFIG_MOCK_SIGNALS)
else()
    message(STATUS "ENABLE_CONFIG_MOCK_SIGNALS OFF")
endif()

if (ENABLE_CONFIG_VALIDATION)
    message(STATUS "ENABLE_CONFIG_VALIDATION ON")
    add_compile_definitions(ENABLE_CONFIG_VALIDATION)
else()
    message(STATUS "ENABLE_CONFIG_VALIDATION OFF")
endif()

if (INIT_OUTPUT)
    message(STATUS "INIT_OUTPUT ON")
    ADD_DEFINITIONS(-DINIT_OUTPUT)
else()
    message(STATUS "INIT_OUTPUT OFF")
endif()

# 2. 交互指令输出
# 指令初始化
# option(COMM_INIT "communication cmd INIT" ON)

# if (COMM_INIT)
#     message(STATUS "COMM_INIT ON")
#     ADD_DEFINITIONS(-DCOMM_INIT)
# else()
#     message(STATUS "COMM_INIT OFF")
# endif()

if (COMM_OUTPUT)
    message(STATUS "COMM_OUTPUT ON")
    ADD_DEFINITIONS(-DCOMM_OUTPUT)
else()
    message(STATUS "COMM_OUTPUT OFF")
endif()


# 2. 交互指令输出
if (COMM_ACK_OUTPUT)
    message(STATUS "COMM_ACK_OUTPUT ON")
    ADD_DEFINITIONS(-DCOMM_ACK_OUTPUT)
else()
    message(STATUS "COMM_ACK_OUTPUT OFF")
endif()

# 3. 运行步骤输出
if (PROCESS_STATUS_OUTPUT)
    message(STATUS "PROCESS_STATUS_OUTPUT ON")
    ADD_DEFINITIONS(-DPROCESS_STATUS_OUTPUT)
else()
    message(STATUS "PROCESS_STATUS_OUTPUT OFF")
endif()

# 4. 数据计算
if (PROCESS_DATA_OUTPUT)
    message(STATUS "PROCESS_DATA_OUTPUT ON")
    ADD_DEFINITIONS(-DPROCESS_DATA_OUTPUT)
else()
    message(STATUS "PROCESS_DATA_OUTPUT OFF")
endif()

# 5. 结果缓存
if (RESULT_CACHE_OUTPUT)
    message(STATUS "RESULT_CACHE_OUTPUT ON")
    ADD_DEFINITIONS(-DRESULT_CACHE_OUTPUT)
else()
    message(STATUS "RESULT_CACHE_OUTPUT OFF")
endif()

# 10. 错误日志本地输出
if (ERROR_TO_LOG)
    message(STATUS "ERROR_TO_LOG ON")
    ADD_DEFINITIONS(-DERROR_TO_LOG)
else()
    message(STATUS "ERROR_TO_LOG OFF")
endif()

# 11. 输出本地存储或数据库输出

#21. 动态库输出
option(LIBRARY "LIBRARY" ON)
if (LIBRARY)
    message(STATUS "LIBRARY ON")
    add_compile_definitions(LIBRARY)
else()
    message(STATUS "LIBRARY OFF")
endif()

set(PROGRAM_PREFIX  "CSPC")

########################################### 编译 ###########################
# 3.1 版本号处理
set(Python3_EXECUTABLE "D:/Programs/Python313/python.exe")
find_package(Python3 REQUIRED COMPONENTS Interpreter)

#   读取 CHANGELOG.md 中的最新版本号
execute_process(
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/scriptFile/get_version_from_log.py ${CMAKE_CURRENT_SOURCE_DIR}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE SW_VERSION
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
#   读取生成软件类型：测试版(build)，发布版(git commit)
execute_process(
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/scriptFile/get_sw_type.py ${CMAKE_SOURCE_DIR}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE SW_TYPE
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

message(STATUS "${PROJECT_NAME} project version: ${SW_VERSION}")
#   将项目+版本号存储在变量中
set(PROJECT_VERSION "${PROGRAM_PREFIX}_${PROJECT_NAME}_V${SW_VERSION}")

#   设置窗口标题
add_definitions(-DMAIN_APP_WINDOW_TITLE="${PROJECT_VERSION}")

# 3.2 设置目标名称
string(TIMESTAMP CURRENT_DATE "%Y%m%d") #   获取当前日期
# release-发布版 
# test-测试版, release build
# debug-调试版，debug build
string(STRIP "${SW_TYPE}" SW_TYPE)
if(SW_TYPE STREQUAL "release") 
set(TARGET_NAME "${PROGRAM_PREFIX}_${PROJECT_NAME}_v${SW_VERSION}_${CURRENT_DATE}_${BUILD_MODE}") #   使用新版本号命名生成的可执行文件
else()
# set(TARGET_NAME "${PROGRAM_PREFIX}_${PROJECT_NAME}_v${SW_VERSION}_${CURRENT_DATE}_${SW_TYPE}") #   使用新版本号命名生成的可执行文件
set(TARGET_NAME "${PROGRAM_PREFIX}_${PROJECT_NAME}") #   使用新版本号命名生成的可执行文件
endif()

# 3.3 指定最终编译产物的输出路径
set(BASE_OUTPUT_DIR "${CMAKE_BINARY_DIR}")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${BASE_OUTPUT_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${BASE_OUTPUT_DIR}/bin) # 将库文件也放在 bin 目录中
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${BASE_OUTPUT_DIR}/lib) # 静态库仍然放在 lib 目录中  

# 定义一个函数来设置输出路径
function(set_output_directories target)
    set_target_properties(${target} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
        LIBRARY_OUTPUT_DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
        ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}
    )
endfunction()

# 3.4 一次性添加所有文件
if(0)
    #AUX_SOURCE_DIRECTORY(./ src_list)
    #AUX_SOURCE_DIRECTORY(./<mod_2> mod_2_src_list)
    file(GLOB_RECURSE SRC_LIST "*.cpp" "*.ui" "*.h") #"protocol/*.cpp" "protocol/*.ui"
    AUX_SOURCE_DIRECTORY(. SRC_LIST)

    # 1.8 输出
    if(ANDROID)
      ADD_LIBRARY(${PROJECT_NAME} SHARED ${SRC_LIST} ${QRC})
    else()
      ADD_EXECUTABLE(${PROJECT_NAME} ${SRC_LIST} ${QRC}) #
    endif()

else() #1.7.2 分级目录添加
    add_subdirectory(account)
    add_subdirectory(components) # processList communication algorithm assistant dataHandle database qtDebug/qtOpt sensor show)
    add_subdirectory(processList)
    add_subdirectory(thread)
    add_subdirectory(sensor)
    add_subdirectory(machine)
    add_subdirectory(communication)
    add_subdirectory(data)
    add_subdirectory(algorithm)
    add_subdirectory(show)
    add_subdirectory(database)
    add_subdirectory(saveLoad)
    add_subdirectory(qtDebug)
endif()

# 3.5 
if(CMAKE_BUILD_TYPE IN_LIST RELEASE_BUILD_TYPES)
ADD_EXECUTABLE(${PROJECT_NAME} WIN32 ${SOURCE_FILE} ${QRC}) #logo.rc
else()
ADD_EXECUTABLE(${PROJECT_NAME} ${SOURCE_FILE} ${QRC}) #logo.rc
endif()

set_output_directories(${PROJECT_NAME})
set_target_properties(${PROJECT_NAME} PROPERTIES OUTPUT_NAME ${TARGET_NAME})


# 3.6 链接库文件
target_link_libraries(
    ${PROJECT_NAME} PRIVATE
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::WinMain
    Qt5::Sql
    Qt5::PrintSupport
    Qt5::Charts
    account_lib
    component_lib
    qLog_lib
)

############################################ 测试单元 ###########################
# 4.1 


