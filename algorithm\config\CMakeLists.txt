# Algorithm Config Module
cmake_minimum_required(VERSION 3.5)

project(algorithmConfig LANGUAGES CXX)

# Qt配置
set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 源文件列表
set(ALGORITHM_CONFIG_SOURCES
    AlgorithmConfigData.cpp
)

# 头文件列表
set(ALGORITHM_CONFIG_HEADERS
    AlgorithmConfigData.h
)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../../components/configModule)

# 创建静态库
add_library(algorithmConfig STATIC
    ${ALGORITHM_CONFIG_SOURCES}
    ${ALGORITHM_CONFIG_HEADERS}
)

# 链接Qt库
target_link_libraries(algorithmConfig
    Qt5::Core
    Qt5::Gui
)

# 设置目标属性
set_target_properties(algorithmConfig PROPERTIES
    OUTPUT_NAME "algorithmConfig"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# 安装规则
install(TARGETS algorithmConfig
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${ALGORITHM_CONFIG_HEADERS}
    DESTINATION include/algorithm/config
)
