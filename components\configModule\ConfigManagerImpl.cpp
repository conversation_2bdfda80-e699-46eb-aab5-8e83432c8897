#include "ConfigManager.h"
#include "ConfigService.h"
#include <QApplication>
#include <QFile>
#include <QSettings>
#include <QXmlStreamReader>
#include <QXmlStreamWriter>

namespace Config {

// 硬件配置加载
ConfigResult ConfigManager::loadHardwareConfig() {
    QString filePath = getConfigFilePath(FileType::Hardware);
    if (!QFile::exists(filePath)) {
        return ConfigResult(false, ErrorType::FileNotFound, "Hardware config file not found: " + filePath);
    }

    QSettings settings(filePath, QSettings::IniFormat);

    // 读取硬件配置
    m_hardwareConfig.xy_radius_limit = settings.value("HARDWARE/xy_radius_limit", 1500).toUInt();
    m_hardwareConfig.z_radius_limit  = settings.value("HARDWARE/z_radius_limit", 1400).toUInt();
    m_hardwareConfig.x_step_dist     = settings.value("HARDWARE/x_step_dist", 10).toUInt();
    m_hardwareConfig.y_step_dist     = settings.value("HARDWARE/y_step_dist", 10).toUInt();
    m_hardwareConfig.z_step_dist     = settings.value("HARDWARE/z_step_dist", 10).toUInt();

    logInfo("Hardware configuration loaded successfully");
    logInfo(QString("  XY limit: %1, Z limit: %2, Step: %3,%4,%5")
                .arg(m_hardwareConfig.xy_radius_limit)
                .arg(m_hardwareConfig.z_radius_limit)
                .arg(m_hardwareConfig.x_step_dist)
                .arg(m_hardwareConfig.y_step_dist)
                .arg(m_hardwareConfig.z_step_dist));

    return ConfigResult(true);
}

// 算法配置加载
ConfigResult ConfigManager::loadAlgorithmConfig() {
    QString filePath = getConfigFilePath(FileType::Algorithm);
    if (!QFile::exists(filePath)) {
        return ConfigResult(false, ErrorType::FileNotFound, "Algorithm config file not found: " + filePath);
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return ConfigResult(false, ErrorType::PermissionError, "Cannot open algorithm config file: " + filePath);
    }

    QXmlStreamReader xmlReader(&file);
    m_algorithmConfig.parameters.clear();

    while (!xmlReader.atEnd() && !xmlReader.hasError()) {
        QXmlStreamReader::TokenType token = xmlReader.readNext();

        if (token == QXmlStreamReader::StartElement) {
            if (xmlReader.name() != "Parameters") {
                QString elementName  = xmlReader.name().toString();
                QString elementValue = xmlReader.readElementText();

                bool ok;
                int  value = elementValue.toInt(&ok);
                if (ok) {
                    m_algorithmConfig.parameters[elementName] = value;
                }
            }
        }
    }

    file.close();

    if (xmlReader.hasError()) {
        return ConfigResult(false, ErrorType::ParseError, "XML parsing error: " + xmlReader.errorString());
    }

    logInfo("Algorithm configuration loaded successfully");
    logInfo(QString("  Loaded %1 algorithm parameters").arg(m_algorithmConfig.parameters.size()));

    return ConfigResult(true);
}

// 多通道配置解析方法
QVector<QPoint> ConfigManager::parseFaculaCenterChannels(const QString &channelsStr) {
    QVector<QPoint> points;

    if (channelsStr.isEmpty()) {
        logWarning("Empty facula center channels string");
        return points;
    }

    // 按分号分割各个通道
    QStringList channels = channelsStr.split(';', Qt::SkipEmptyParts);

    for (const QString &channel : channels) {
        // 按逗号分割x,y坐标
        QStringList coords = channel.split(',', Qt::SkipEmptyParts);

        if (coords.size() == 2) {
            bool xOk, yOk;
            int  x = coords[0].trimmed().toInt(&xOk);
            int  y = coords[1].trimmed().toInt(&yOk);

            if (xOk && yOk) {
                QPoint point(x, y);
                if (isValidChannelPoint(point)) {
                    points.append(point);
                    logInfo(QString("  Parsed channel point: (%1,%2)").arg(x).arg(y));
                } else {
                    logError(QString("Invalid channel point: (%1,%2) - coordinates out of range").arg(x).arg(y));
                }
            } else {
                logError(QString("Invalid channel format: %1 - coordinates must be integers").arg(channel));
            }
        } else {
            logError(QString("Invalid channel format: %1 - expected format: x,y").arg(channel));
        }
    }

    if (points.isEmpty()) {
        logError("No valid channels found in configuration: " + channelsStr);
        // 使用默认值
        points.append(QPoint(2, 2));
        logInfo("Using default channel point: (2,2)");
    } else {
        logInfo(QString("Parsed %1 facula center channels successfully").arg(points.size()));
    }

    return points;
}

bool ConfigManager::isValidChannelPoint(const QPoint &point, uint8_t maxX, uint8_t maxY) {
    return (point.x() >= 0 && point.x() <= maxX && point.y() >= 0 && point.y() <= maxY);
}

// 配置验证
ConfigResult ConfigManager::validateConfigs() {
    logInfo("Validating all configurations...");

    // 使用新的配置服务进行验证
    ConfigService &configService = ConfigService::getInstance();

    // 验证所有已注册的配置模块
    ConfigResult result = configService.validateAllConfigs();
    if (!result.success) {
        return result;
    }

    // 验证系统配置
    if (m_systemConfig.version.isEmpty()) {
        return ConfigResult(false, ErrorType::ValidationError, "System config: version is empty");
    }

    logInfo("All configurations validated successfully");
    return ConfigResult(true);
}

// 设置配置数据的方法
void ConfigManager::setSystemConfig(const SystemConfig &config) {
    m_systemConfig = config;
    Q_EMIT configChanged(FileType::System);
}

void ConfigManager::setFaculaConfig(const FaculaConfig &config) {
    m_faculaConfig = config;
    Q_EMIT configChanged(FileType::Facula);
}

void ConfigManager::setHardwareConfig(const HardwareConfig &config) {
    m_hardwareConfig = config;
    Q_EMIT configChanged(FileType::Hardware);
}

void ConfigManager::setAlgorithmConfig(const AlgorithmConfig &config) {
    m_algorithmConfig = config;
    Q_EMIT configChanged(FileType::Algorithm);
}

}  // namespace Config
