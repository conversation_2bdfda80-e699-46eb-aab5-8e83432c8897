# 光子传感器模块开发文档

## 模块概述

光子传感器模块是激光工具系统的核心传感模块，负责光斑检测、光子信号处理、多通道数据采集等关键功能。

### 模块职责

1. **光斑检测** - 检测和定位光斑的位置和特征
2. **多通道处理** - 支持多通道光斑数据的并行处理
3. **信号处理** - 处理光子传感器的原始信号数据
4. **数据适配** - 适配不同类型的光斑处理算法
5. **配置管理** - 管理光斑检测相关的配置参数

### 设计原则

1. **精确检测** - 确保光斑检测的精确性和可靠性
2. **多通道支持** - 支持多通道并行处理
3. **算法可扩展** - 支持多种光斑处理算法
4. **数据驱动** - 基于配置的检测参数控制
5. **性能优化** - 优化检测算法的执行效率

## 架构设计

### 模块结构

```
sensor/photonSensor/
├── config/
│   ├── FaculaConfigData.h/.cpp         # 光斑配置数据类
│   └── PhotonSensorModule.h/.cpp       # 模块初始化
├── IPhotonSensor.h/.cpp                # 光子传感器接口
├── IFaculaAdjust.h/.cpp                # 光斑调节接口
├── faculaContext.h/.cpp                # 光斑上下文
├── faculaFactory.h/.cpp                # 光斑工厂
├── faculaDataAdapter.h/.cpp            # 数据适配器
├── faculaProcessingConfig.h/.cpp       # 处理配置
├── 光斑处理算法/
│   ├── faculaCircle.h/.cpp             # 圆形光斑处理
│   ├── faculaDouble.h/.cpp             # 双光斑处理
│   ├── faculaHorizontal.h/.cpp         # 水平光斑处理
│   └── faculaVertical.h/.cpp           # 垂直光斑处理
└── 配置提供者/
    ├── FaculaConfigProvider.h/.cpp     # 光斑配置提供者
    ├── AlgorithmConfigProvider.h/.cpp  # 算法配置提供者
    └── AdjustProcessConfigProvider.h/.cpp # 调节流程配置提供者
```

### 配置管理架构

```mermaid
graph TB
    subgraph "PhotonSensor模块"
        FCD[FaculaConfigData<br/>光斑配置数据]
        PSM[PhotonSensorModule<br/>模块管理器]
        IPS[IPhotonSensor<br/>传感器接口]
        IFA[IFaculaAdjust<br/>调节接口]
        FC[FaculaContext<br/>光斑上下文]
        FF[FaculaFactory<br/>光斑工厂]
        FDA[FaculaDataAdapter<br/>数据适配器]
    end
    
    subgraph "光斑处理算法"
        FCir[FaculaCircle<br/>圆形光斑]
        FDou[FaculaDouble<br/>双光斑]
        FHor[FaculaHorizontal<br/>水平光斑]
        FVer[FaculaVertical<br/>垂直光斑]
    end
    
    subgraph "Config服务"
        CS[ConfigService<br/>配置服务]
        DCM[DynamicConfigManager<br/>动态管理器]
    end
    
    FCD --> CS
    PSM --> FCD
    IPS --> FCD
    IFA --> FCD
    FC --> FCD
    FF --> FCD
    FDA --> FCD
    
    FCir --> FCD
    FDou --> FCD
    FHor --> FCD
    FVer --> FCD
    
    CS --> DCM
    
    style FCD fill:#e1f5fe
    style PSM fill:#f3e5f5
```

## 配置数据设计

### FaculaConfigData类

```cpp
namespace PhotonSensor {
    class FaculaConfigData : public Config::BaseConfigData<FaculaConfigData> {
    public:
        // 多通道光斑中心配置
        QString         facula_center_channels;        // 多通道配置字符串
        QVector<QPoint> facula_center_points;          // 解析后的通道坐标列表
        uint32_t        facula_center_peak_threshold;  // 多通道模式下的peak阈值

        // 兼容性配置
        uint8_t facula_center_loc_x;  // 单点X坐标
        uint8_t facula_center_loc_y;  // 单点Y坐标

        // 光斑处理类型
        uint8_t facula_handle_type;  // 处理类型：0-基础，1-增强，2-高精度
        
        // 接口方法
        static QString staticTypeName() { return "Facula"; }
        QString getTypeName() const override;
        QVariantMap toVariantMap() const override;
        bool fromVariantMap(const QVariantMap &data) override;
        bool validate() const override;
        void setDefaults() override;
        
        // 业务方法
        const QString &getFaculaCenterChannels() const;
        void setFaculaCenterChannels(const QString &channels);
        const QVector<QPoint> &getFaculaCenterPoints() const;
        void setFaculaCenterPoints(const QVector<QPoint> &points);
        uint32_t getFaculaCenterPeakThreshold() const;
        void setFaculaCenterPeakThreshold(uint32_t threshold);
        uint8_t getFaculaHandleType() const;
        void setFaculaHandleType(uint8_t type);
        QString getFaculaHandleTypeDescription() const;
    };
}
```

### 配置参数详解

#### 1. 多通道光斑中心配置
- **facula_center_channels**: 多通道配置字符串 (格式: "2,2")
- **facula_center_points**: 解析后的通道坐标列表
- **facula_center_peak_threshold**: 多通道模式下的peak阈值 (100-2000)

#### 2. 兼容性配置
- **facula_center_loc_x**: 单点X坐标 (0-255)
- **facula_center_loc_y**: 单点Y坐标 (0-255)

#### 3. 光斑处理类型
- **0 - 基础**: 基础光斑检测算法
- **1 - 增强**: 增强型检测算法（默认）
- **2 - 高精度**: 高精度检测算法

## 模块初始化

### 配置注册

```cpp
// sensor/photonSensor/config/PhotonSensorModule.cpp
namespace PhotonSensor {
    void PhotonSensorModule::initialize() {
        // 配置会通过REGISTER_CONFIG_TYPE宏自动注册
        qDebug() << "PhotonSensor module initialized with config registration";
        
        // 验证配置注册
        auto &registry = Config::ConfigTypeRegistry::getInstance();
        if (registry.isRegistered("Facula")) {
            qDebug() << "✅ Facula config registered successfully";
        } else {
            qCritical() << "❌ Facula config registration failed";
        }
        
        // 初始化光斑工厂
        initializeFaculaFactory();
        
        // 初始化数据适配器
        initializeDataAdapter();
    }
    
    void PhotonSensorModule::initializeFaculaFactory() {
        // 注册光斑处理算法
        FaculaFactory::getInstance().registerAlgorithm("circle", std::make_unique<FaculaCircle>());
        FaculaFactory::getInstance().registerAlgorithm("double", std::make_unique<FaculaDouble>());
        FaculaFactory::getInstance().registerAlgorithm("horizontal", std::make_unique<FaculaHorizontal>());
        FaculaFactory::getInstance().registerAlgorithm("vertical", std::make_unique<FaculaVertical>());
    }
}
```

### 自动注册机制

```cpp
// 在FaculaConfigData.h文件末尾
REGISTER_CONFIG_TYPE(PhotonSensor::FaculaConfigData, "Facula", "1.0.0", 
                    "光斑检测配置，包含多通道光斑中心和处理参数");
```

## 使用指南

### 获取光斑配置

```cpp
// 在光斑检测相关代码中
auto* faculaConfig = CONFIG_SERVICE().getConfig<PhotonSensor::FaculaConfigData>("Facula");

if (faculaConfig) {
    // 读取多通道配置
    QString channels = faculaConfig->getFaculaCenterChannels();
    QVector<QPoint> points = faculaConfig->getFaculaCenterPoints();
    uint32_t threshold = faculaConfig->getFaculaCenterPeakThreshold();
    
    // 读取处理类型
    uint8_t handleType = faculaConfig->getFaculaHandleType();
    QString typeDesc = faculaConfig->getFaculaHandleTypeDescription();
    
    qDebug() << "光斑配置:";
    qDebug() << "  通道配置:" << channels;
    qDebug() << "  通道数量:" << points.size();
    qDebug() << "  Peak阈值:" << threshold;
    qDebug() << "  处理类型:" << typeDesc;
}
```

### 修改光斑配置

```cpp
// 修改多通道配置
faculaConfig->setFaculaCenterChannels("3,3;5,5;7,7");  // 设置3个通道
faculaConfig->setFaculaCenterPeakThreshold(900);       // 设置peak阈值
faculaConfig->setFaculaHandleType(2);                  // 设置为高精度模式

// 验证配置
if (faculaConfig->validate()) {
    // 保存配置
    CONFIG_SERVICE().saveConfig("Facula");
    qDebug() << "光斑配置已更新并保存";
} else {
    qWarning() << "光斑配置验证失败";
}
```

### 在光斑检测中使用配置

```cpp
// FaculaContext类中的光斑检测
void FaculaContext::detectFacula() {
    auto* config = CONFIG_SERVICE().getConfig<PhotonSensor::FaculaConfigData>("Facula");
    if (!config) {
        qCritical() << "无法获取光斑配置";
        return;
    }
    
    // 获取配置参数
    QVector<QPoint> channels = config->getFaculaCenterPoints();
    uint32_t threshold = config->getFaculaCenterPeakThreshold();
    uint8_t handleType = config->getFaculaHandleType();
    
    // 根据处理类型选择算法
    QString algorithmType;
    switch (handleType) {
        case 0: algorithmType = "circle"; break;
        case 1: algorithmType = "double"; break;
        case 2: algorithmType = "horizontal"; break;
        default: algorithmType = "circle"; break;
    }
    
    // 创建光斑处理算法
    auto algorithm = FaculaFactory::getInstance().createAlgorithm(algorithmType);
    if (!algorithm) {
        qCritical() << "无法创建光斑处理算法:" << algorithmType;
        return;
    }
    
    // 执行多通道检测
    for (const QPoint &channel : channels) {
        FaculaResult result = algorithm->detectFacula(channel, threshold);
        processFaculaResult(result);
    }
}

void FaculaContext::processFaculaResult(const FaculaResult &result) {
    if (result.isValid()) {
        qDebug() << "光斑检测成功:";
        qDebug() << "  位置:" << result.position;
        qDebug() << "  强度:" << result.intensity;
        qDebug() << "  质量:" << result.quality;
    } else {
        qWarning() << "光斑检测失败:" << result.errorMessage;
    }
}
```

## 配置文件格式

### JSON配置文件结构

```json
{
    "_type": "Facula",
    "_version": "1.0.0",
    "facula_center_channels": "2,2",
    "facula_center_peak_threshold": 800,
    "facula_center_loc_x": 2,
    "facula_center_loc_y": 2,
    "facula_handle_type": 1
}
```

### 配置文件位置

- **默认路径**: `config/modules/facula/facula_config.json`
- **备份路径**: `config/backup/facula/`
- **模板路径**: `config/templates/facula_template.json`

## 光斑处理算法

### 算法类型

1. **FaculaCircle** - 圆形光斑检测算法
2. **FaculaDouble** - 双光斑检测算法
3. **FaculaHorizontal** - 水平光斑检测算法
4. **FaculaVertical** - 垂直光斑检测算法

### 算法选择策略

```cpp
QString selectAlgorithm(uint8_t handleType, const QVector<QPoint> &channels) {
    switch (handleType) {
        case 0: // 基础
            return "circle";
        case 1: // 增强
            return channels.size() > 1 ? "double" : "circle";
        case 2: // 高精度
            return channels.size() > 2 ? "horizontal" : "double";
        default:
            return "circle";
    }
}
```

## 测试方案

### 配置测试

```cpp
#ifdef ENABLE_CONFIG_TESTS
void testFaculaConfig() {
    auto* config = CONFIG_SERVICE().getConfig<PhotonSensor::FaculaConfigData>("Facula");
    
    // 测试多通道配置
    config->setFaculaCenterChannels("3,3;5,5");
    QVector<QPoint> points = config->getFaculaCenterPoints();
    QCOMPARE(points.size(), 2);
    QCOMPARE(points[0], QPoint(3, 3));
    QCOMPARE(points[1], QPoint(5, 5));
    
    // 测试阈值设置
    config->setFaculaCenterPeakThreshold(1000);
    QCOMPARE(config->getFaculaCenterPeakThreshold(), 1000u);
    
    // 测试处理类型
    config->setFaculaHandleType(2);
    QCOMPARE(config->getFaculaHandleType(), 2);
    
    // 测试配置验证
    QVERIFY(config->validate());
}
#endif
```

### 光斑检测测试

```cpp
#ifdef ENABLE_CONFIG_MOCK_SIGNALS
void testFaculaDetection() {
    // 模拟光斑检测
    auto* config = CONFIG_SERVICE().getConfig<PhotonSensor::FaculaConfigData>("Facula");
    
    // 设置测试参数
    config->setFaculaCenterChannels("2,2;4,4");
    config->setFaculaCenterPeakThreshold(500);
    config->setFaculaHandleType(1);
    
    // 模拟检测过程
    FaculaContext context;
    
    // 使用QTimer模拟异步检测
    QTimer::singleShot(100, [&]() {
        context.detectFacula();
    });
}
#endif
```

## 性能考虑

1. **算法优化** - 优化光斑检测算法的执行效率
2. **多线程处理** - 支持多通道并行检测
3. **内存管理** - 合理管理图像数据的内存使用
4. **缓存机制** - 缓存频繁访问的配置参数

## 维护指南

1. **定期校准传感器参数**
2. **监控光斑检测的准确率**
3. **备份重要的配置文件**
4. **更新算法和配置文档**
5. **性能监控和优化**

光子传感器模块通过模块化的配置管理，为系统提供了精确可靠的光斑检测能力。
