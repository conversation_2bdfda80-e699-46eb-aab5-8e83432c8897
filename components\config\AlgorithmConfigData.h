#pragma once

#include "IConfigData.h"
#include "ConfigTypeRegistry.h"
#include <QMap>
#include <QString>
#include <QVariantMap>
#include <QStringList>

namespace Config {

/**
 * @brief 算法配置数据
 * 
 * 包含所有算法相关的配置参数：
 * - 传统算法参数映射
 * - 图像处理算法参数
 * - 插值和滤波器配置
 * 
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 提供类型安全的参数访问
 * - 支持参数验证和默认值设置
 * - 完全符合开闭原则
 */
class AlgorithmConfigData : public BaseConfigData<AlgorithmConfigData> {
public:
    AlgorithmConfigData();
    ~AlgorithmConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() { return "Algorithm"; }

    // IConfigData接口实现
    QString getTypeName() const override { return staticTypeName(); }
    QString getVersion() const override { return "2.0.0"; }
    QString getDescription() const override { return "算法配置参数，包含传统算法和图像处理参数"; }
    
    QVariantMap toVariantMap() const override;
    bool fromVariantMap(const QVariantMap &data) override;
    bool validate() const override;
    void setDefaults() override;
    
    QStringList getFieldNames() const override;
    QString getFieldType(const QString &fieldName) const override;
    QString getFieldDescription(const QString &fieldName) const override;
    bool hasField(const QString &fieldName) const override;
    QVariant getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool resetField(const QString &fieldName) override;

    // 算法参数访问接口
    /**
     * @brief 获取算法参数
     * @param key 参数名称
     * @param defaultValue 默认值
     * @return 参数值
     */
    int getParameter(const QString &key, int defaultValue = 0) const;

    /**
     * @brief 设置算法参数
     * @param key 参数名称
     * @param value 参数值
     */
    void setParameter(const QString &key, int value);

    /**
     * @brief 获取所有算法参数
     * @return 参数映射表
     */
    const QMap<QString, int> &getAllParameters() const { return parameters; }

    /**
     * @brief 设置所有算法参数
     * @param params 参数映射表
     */
    void setAllParameters(const QMap<QString, int> &params) { parameters = params; }

    // 图像处理参数访问接口
    /**
     * @brief 获取插值类型
     * @return 插值类型 (0-3)
     */
    uint8_t getInterpolationType() const { return interpolation_type; }

    /**
     * @brief 设置插值类型
     * @param type 插值类型 (0-3)
     */
    void setInterpolationType(uint8_t type) { interpolation_type = type; }

    /**
     * @brief 获取滤波器类型列表
     * @return 滤波器类型字符串
     */
    const QString &getFilterTypes() const { return filter_types; }

    /**
     * @brief 设置滤波器类型列表
     * @param types 滤波器类型字符串
     */
    void setFilterTypes(const QString &types) { filter_types = types; }

    /**
     * @brief 获取滤波器强度
     * @return 滤波器强度 (0.0-10.0)
     */
    float getFilterStrength() const { return filter_strength; }

    /**
     * @brief 设置滤波器强度
     * @param strength 滤波器强度 (0.0-10.0)
     */
    void setFilterStrength(float strength) { filter_strength = strength; }

    // 便利方法
    /**
     * @brief 检查参数是否存在
     * @param key 参数名称
     * @return 是否存在
     */
    bool hasParameter(const QString &key) const;

    /**
     * @brief 移除参数
     * @param key 参数名称
     * @return 是否成功移除
     */
    bool removeParameter(const QString &key);

    /**
     * @brief 获取参数数量
     * @return 参数数量
     */
    int getParameterCount() const { return parameters.size(); }

    /**
     * @brief 清空所有参数
     */
    void clearParameters() { parameters.clear(); }

    /**
     * @brief 获取参数名称列表
     * @return 参数名称列表
     */
    QStringList getParameterNames() const;

    /**
     * @brief 导入参数从另一个配置
     * @param other 另一个算法配置
     * @param overwrite 是否覆盖现有参数
     */
    void importParameters(const AlgorithmConfigData &other, bool overwrite = true);

public:
    // 配置数据成员
    QMap<QString, int> parameters;          // 传统算法参数映射 (60+个参数)
    uint8_t interpolation_type;             // 插值类型 (0-3: 最近邻、双线性、双三次、Lanczos)
    QString filter_types;                   // 滤波器类型列表 (逗号分隔)
    float filter_strength;                  // 滤波器强度 (0.0-10.0)

private:
    /**
     * @brief 加载默认算法参数
     */
    void loadDefaultParameters();

    /**
     * @brief 验证参数值范围
     * @param key 参数名称
     * @param value 参数值
     * @return 是否有效
     */
    bool validateParameterValue(const QString &key, int value) const;

    /**
     * @brief 获取参数的有效范围
     * @param key 参数名称
     * @return 范围描述 (min, max)
     */
    QPair<int, int> getParameterRange(const QString &key) const;

    /**
     * @brief 获取参数的默认值
     * @param key 参数名称
     * @return 默认值
     */
    int getParameterDefaultValue(const QString &key) const;

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;
    static const QMap<QString, QPair<int, int>> s_parameterRanges;
    static const QMap<QString, int> s_defaultParameters;
};

}  // namespace Config

// 自动注册算法配置类型
REGISTER_CONFIG_TYPE(Config::AlgorithmConfigData, "Algorithm", "2.0.0", 
                    "算法配置参数，包含传统算法和图像处理参数")
