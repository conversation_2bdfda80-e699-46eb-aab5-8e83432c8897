#include "AdjustProcessConfigManager.h"
#include <QApplication>
#include <QSettings>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDebug>

namespace Config {

AdjustProcessConfigManager::AdjustProcessConfigManager(QObject *parent)
    : QObject(parent)
    , m_fileWatcher(new QFileSystemWatcher(this))
{
    // 设置配置文件路径
    m_configFilePath = QApplication::applicationDirPath() + "/config/modules/lenAdjust/adjust_process_config.json";
    
    // 连接文件监控信号
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged, this, &AdjustProcessConfigManager::onFileChanged);
    
    // 生成默认配置
    generateDefaultConfig();
    
    // 自动加载配置
    loadConfig();
    
    logInfo("AdjustProcessConfigManager initialized");
}

AdjustProcessConfigManager::~AdjustProcessConfigManager() {
    // 保存配置
    saveConfig();
}

ConfigResult AdjustProcessConfigManager::loadConfig() {
    logInfo("Loading adjust process configuration from: " + m_configFilePath);
    
    if (!QFile::exists(m_configFilePath)) {
        logInfo("Config file not found, generating default configuration");
        ConfigResult result = saveConfig(); // 生成默认配置文件
        if (!result.success) {
            return result;
        }
    }
    
    QSettings settings(m_configFilePath, QSettings::IniFormat);
    
    // 读取调节流程参数
    settings.beginGroup("ADJUST_PARAM");
    m_config.facula_ok_times = settings.value("facula_ok_times", 3).toUInt();
    m_config.solid_time = settings.value("solid_time", 0).toUInt();
    m_config.facula_ng_handle = settings.value("facula_ng_handle", 1).toUInt();
    settings.endGroup();
    
    // 验证配置
    ConfigResult validationResult = validateConfig();
    if (!validationResult.success) {
        logError("Configuration validation failed: " + validationResult.message);
        return validationResult;
    }
    
    // 添加文件监控
    if (!m_fileWatcher->files().contains(m_configFilePath)) {
        m_fileWatcher->addPath(m_configFilePath);
    }
    
    logInfo("Adjust process configuration loaded successfully");
    logInfo(QString("  OK times: %1, Solid time: %2ms, NG handle: %3")
                .arg(m_config.facula_ok_times)
                .arg(m_config.solid_time)
                .arg(m_config.facula_ng_handle));
    
    Q_EMIT configChanged();
    return ConfigResult(true);
}

ConfigResult AdjustProcessConfigManager::saveConfig() {
    logInfo("Saving adjust process configuration to: " + m_configFilePath);
    
    // 确保目录存在
    QFileInfo fileInfo(m_configFilePath);
    if (!ensureDirectoryExists(fileInfo.absolutePath())) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create config directory");
    }
    
    QSettings settings(m_configFilePath, QSettings::IniFormat);
    
    // 写入调节流程参数
    settings.beginGroup("ADJUST_PARAM");
    settings.setValue("facula_ok_times", m_config.facula_ok_times);
    settings.setValue("solid_time", m_config.solid_time);
    settings.setValue("facula_ng_handle", m_config.facula_ng_handle);
    settings.endGroup();
    
    settings.sync();
    
    logInfo("Adjust process configuration saved successfully");
    return ConfigResult(true);
}

bool AdjustProcessConfigManager::isConfigValid() const {
    return validateConfig().success;
}

QString AdjustProcessConfigManager::getConfigFilePath() const {
    return m_configFilePath;
}

void AdjustProcessConfigManager::setConfig(const AdjustProcessConfig &config) {
    m_config = config;
    Q_EMIT configChanged();
}

void AdjustProcessConfigManager::setFaculaOkTimes(uint8_t times) {
    if (times >= 1 && times <= 10) { // 合理的判定次数范围
        m_config.facula_ok_times = times;
        Q_EMIT configChanged();
    }
}

void AdjustProcessConfigManager::setSolidTime(uint32_t time) {
    if (time <= 10000) { // 最大10秒固化时间
        m_config.solid_time = time;
        Q_EMIT configChanged();
    }
}

void AdjustProcessConfigManager::setFaculaNgHandle(uint8_t handle) {
    if (handle <= 2) { // 0-停止，1-继续，2-重试
        m_config.facula_ng_handle = handle;
        Q_EMIT configChanged();
    }
}

void AdjustProcessConfigManager::onFileChanged(const QString &path) {
    if (path == m_configFilePath) {
        logInfo("Configuration file changed, reloading...");
        loadConfig();
    }
}

ConfigResult AdjustProcessConfigManager::validateConfig() const {
    // 验证判定次数
    if (m_config.facula_ok_times < 1 || m_config.facula_ok_times > 10) {
        return ConfigResult(false, ErrorType::ValidationError,
                          QString("Facula OK times out of range: %1 (valid: 1-10)")
                          .arg(m_config.facula_ok_times));
    }
    
    // 验证固化时间
    if (m_config.solid_time > 10000) {
        return ConfigResult(false, ErrorType::ValidationError,
                          QString("Solid time too large: %1ms (max: 10000ms)")
                          .arg(m_config.solid_time));
    }
    
    // 验证异常处理方式
    if (m_config.facula_ng_handle > 2) {
        return ConfigResult(false, ErrorType::ValidationError,
                          QString("NG handle type out of range: %1 (valid: 0-2)")
                          .arg(m_config.facula_ng_handle));
    }
    
    return ConfigResult(true);
}

void AdjustProcessConfigManager::generateDefaultConfig() {
    // 使用默认构造函数的值
    m_config = AdjustProcessConfig();
    logInfo("Generated default adjust process configuration");
}

} // namespace Config
