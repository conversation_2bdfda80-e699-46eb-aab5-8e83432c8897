@echo off
REM 编译问题快速修复脚本

echo ========================================
echo 编译问题快速修复工具
echo ========================================

REM 设置编码为UTF-8
chcp 65001 > nul

echo 正在尝试自动修复常见的编译问题...
echo.

echo 1. 更新工具链配置文件...

REM 检查并更新mingw-toolchain.cmake
if not exist "cmake" mkdir cmake

REM 自动检测Qt和MinGW路径
set QT_FOUND=0
set MINGW_FOUND=0

REM 检测Qt路径
for %%P in (D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64 C:\Qt\Qt5.14.2\5.14.2\mingw73_64 D:\Qt\Qt5.14.2\5.14.2\mingw73_64) do (
    if exist "%%P\bin\qmake.exe" (
        set QT_PATH=%%P
        set QT_FOUND=1
        echo ✅ 检测到Qt路径: %%P
        goto :qt_found
    )
)
:qt_found

REM 检测MinGW路径
for %%P in (D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64 C:\Qt\Qt5.14.2\Tools\mingw730_64 D:\Qt\Qt5.14.2\Tools\mingw730_64) do (
    if exist "%%P\bin\gcc.exe" (
        set MINGW_PATH=%%P
        set MINGW_FOUND=1
        echo ✅ 检测到MinGW路径: %%P
        goto :mingw_found
    )
)
:mingw_found

if %QT_FOUND%==0 (
    echo ❌ 未检测到Qt安装，请手动设置路径
    echo 请编辑 cmake/mingw-toolchain.cmake 文件
)

if %MINGW_FOUND%==0 (
    echo ❌ 未检测到MinGW安装，请手动设置路径
    echo 请编辑 cmake/mingw-toolchain.cmake 文件
)

echo.
echo 2. 清理构建缓存...
if exist "build" (
    rmdir /s /q "build"
    echo ✅ 已清理构建目录
)

if exist "CMakeCache.txt" (
    del "CMakeCache.txt"
    echo ✅ 已清理CMake缓存
)

echo.
echo 3. 检查配置模块文件...

REM 检查新创建的配置文件是否存在
set CONFIG_FILES_OK=1

if not exist "components\config\IConfigData.h" (
    echo ❌ 缺少 IConfigData.h
    set CONFIG_FILES_OK=0
)

if not exist "components\config\ConfigTypeRegistry.h" (
    echo ❌ 缺少 ConfigTypeRegistry.h
    set CONFIG_FILES_OK=0
)

if not exist "components\config\DynamicConfigManager.h" (
    echo ❌ 缺少 DynamicConfigManager.h
    set CONFIG_FILES_OK=0
)

if %CONFIG_FILES_OK%==1 (
    echo ✅ 配置模块文件检查通过
) else (
    echo ❌ 配置模块文件不完整，可能影响编译
)

echo.
echo 4. 生成临时编译脚本...

REM 创建临时编译脚本
echo @echo off > temp_build.bat
echo echo 尝试编译配置模块... >> temp_build.bat
echo mkdir build 2^>nul >> temp_build.bat
echo cd build >> temp_build.bat

if %MINGW_FOUND%==1 (
    echo cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug -DENABLE_CONFIG_TESTS=ON .. >> temp_build.bat
) else (
    echo cmake -DCMAKE_BUILD_TYPE=Debug -DENABLE_CONFIG_TESTS=ON .. >> temp_build.bat
)

echo if %%errorlevel%% neq 0 ^( >> temp_build.bat
echo     echo CMake配置失败 >> temp_build.bat
echo     cd .. >> temp_build.bat
echo     exit /b 1 >> temp_build.bat
echo ^) >> temp_build.bat
echo cmake --build . --config Debug >> temp_build.bat
echo cd .. >> temp_build.bat

echo ✅ 已生成临时编译脚本: temp_build.bat

echo.
echo 5. 创建环境设置脚本...

REM 创建环境设置脚本
echo @echo off > setup_env.bat
echo REM 设置编译环境变量 >> setup_env.bat

if %QT_FOUND%==1 (
    echo set PATH=%QT_PATH%\bin;%%PATH%% >> setup_env.bat
)

if %MINGW_FOUND%==1 (
    echo set PATH=%MINGW_PATH%\bin;%%PATH%% >> setup_env.bat
)

echo echo 环境变量已设置 >> setup_env.bat
echo echo Qt路径: %QT_PATH% >> setup_env.bat
echo echo MinGW路径: %MINGW_PATH% >> setup_env.bat

echo ✅ 已生成环境设置脚本: setup_env.bat

echo.
echo ========================================
echo 修复完成
echo ========================================
echo.
echo 下一步操作：
echo 1. 运行 setup_env.bat 设置环境变量
echo 2. 运行 temp_build.bat 尝试编译
echo 3. 如果仍有问题，运行 diagnose-build.bat 进行详细诊断
echo.
echo 或者直接运行 test-compile.bat 进行完整的编译测试
echo.

pause
