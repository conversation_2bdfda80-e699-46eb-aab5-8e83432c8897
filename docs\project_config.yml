# LA-T5 激光工具系统文档管理配置

project:
  name: "LA-T5激光工具系统"
  version: "v1.4.4"
  description: "TOF接收镜片光路耦合软件"
  modules: 
    - "光斑处理"
    - "镜头调节" 
    - "系统配置"
    - "MES集成"
    - "返工处理"

# 角色定义
roles:
  customer: 
    - "设备操作人员"
    - "调试工程师"
    - "质量检验员"
  developer: 
    - "算法工程师"
    - "软件开发工程师"
    - "系统架构师"
  manager: 
    - "项目经理"
    - "产品经理"
    - "技术负责人"
  support: 
    - "技术支持工程师"
    - "客户服务团队"

# 文档ID命名规范
naming:
  module_codes:
    "光斑处理": "SPOT"
    "镜头调节": "LENS"
    "系统配置": "CONFIG"
    "MES集成": "MES"
    "返工处理": "REWORK"
  
  # 功能类型代码
  function_codes:
    "滤波": "FILTER"
    "调节": "ADJUST"
    "检测": "DETECT"
    "校正": "CALIB"
    "优化": "OPT"

# 流程配置 - 使用标准配置
process_config:
  level: "standard"  # 中型项目，标准配置
  required_steps:
    - "问题识别"
    - "需求分析" 
    - "设计方案"
    - "开发实现"
    - "测试验证"
    - "文档更新"
  
  optional_steps:
    - "可行性评估"
    - "影响分析"
    - "代码审查"
    - "性能测试"
    - "用户培训"

# 文档关联规则
linkage_rules:
  inputs_requirements:
    must_have_downstream:
      - "internal/analysis"
      - "outputs/manuals"
    should_have_downstream:
      - "outputs/releases"
      - "support/faq"
  
  inputs_feedback:
    must_have_downstream:
      - "internal/analysis"
      - "support/faq"
    should_have_downstream:
      - "outputs/manuals"
  
  inputs_issues:
    must_have_downstream:
      - "internal/analysis"
      - "internal/development"
      - "internal/testing"

# 自动化配置
automation:
  completeness_check: true
  consistency_check: true
  permission_check: true
  auto_update_trigger: true
  
  # 检查频率
  check_frequency:
    daily: ["permission_check"]
    weekly: ["completeness_check", "consistency_check"]
    monthly: ["full_audit"]

# 质量标准
quality_standards:
  document_completeness: 95
  link_validity: 98
  permission_accuracy: 100
  update_timeliness: 24  # 小时
