#pragma once

#include "ConfigTypes.h"
#include "IModuleConfigManager.h"
#include <QFileSystemWatcher>
#include <QObject>

namespace Config {

/**
 * @brief 光斑配置管理器
 *
 * 专门负责光斑检测和处理相关的配置管理
 * 职责：
 * - 管理光斑中心检测配置
 * - 管理光斑处理算法类型
 * - 提供光斑配置的访问接口
 */
class FaculaConfigManager : public QObject, public IModuleConfigManager {
    Q_OBJECT

  public:
    explicit FaculaConfigManager(QObject *parent = nullptr);
    ~FaculaConfigManager() override;

    // IModuleConfigManager 接口实现
    ConfigResult loadConfig() override;
    ConfigResult saveConfig() override;
    bool         isConfigValid() const override;
    QString      getConfigFilePath() const override;
    QString      getModuleName() const override {
        return "Facula";
    }

    /**
     * @brief 获取光斑配置
     * @return 光斑配置引用
     */
    const FaculaConfig &getConfig() const {
        return m_config;
    }

    /**
     * @brief 设置光斑配置
     * @param config 新的光斑配置
     */
    void setConfig(const FaculaConfig &config);

    /**
     * @brief 获取多通道光斑坐标
     * @return 光斑坐标列表
     */
    const QVector<QPoint> &getFaculaCenterPoints() const {
        return m_config.facula_center_points;
    }

    /**
     * @brief 获取光斑峰值阈值
     * @return 峰值阈值
     */
    uint32_t getPeakThreshold() const {
        return m_config.facula_center_peak_threshold;
    }

    /**
     * @brief 获取光斑处理类型
     * @return 处理类型
     */
    uint8_t getHandleType() const {
        return m_config.facula_handle_type;
    }

    /**
     * @brief 设置光斑处理类型
     * @param handleType 处理类型
     */
    void setHandleType(uint8_t handleType);

    /**
     * @brief 更新多通道配置
     * @param channels 通道配置字符串
     */
    void updateChannels(const QString &channels);

  Q_SIGNALS:
    /**
     * @brief 配置变更信号
     */
    void configChanged();

  private Q_SLOTS:
    /**
     * @brief 配置文件变更处理
     * @param path 文件路径
     */
    void onFileChanged(const QString &path);

  private:
    /**
     * @brief 解析多通道配置字符串
     * @param channels 配置字符串
     * @return 解析后的坐标点列表
     */
    QVector<QPoint> parseChannels(const QString &channels) const;

    /**
     * @brief 验证配置参数
     * @return 验证结果
     */
    ConfigResult validateConfig() const;

    /**
     * @brief 生成默认配置
     */
    void generateDefaultConfig();

  private:
    FaculaConfig        m_config;          // 光斑配置数据
    QFileSystemWatcher *m_fileWatcher;     // 文件监控器
    QString             m_configFilePath;  // 配置文件路径
};

}  // namespace Config
