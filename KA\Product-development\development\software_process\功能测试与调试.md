# 功能测试与调试

## 原则

- 原代码优先测试原则

优先在原程序中添加通过CMakeLists.txt中声明宏，在程序代码中添加测试代码方式进行测试。例如添加模拟信号触发槽函数方式进行测试， 

## 测试方案对比表

| 测试方案 | 适用场景 | 优点 | 缺点 | 实施难度 | 推荐度 |
|----------|----------|------|------|----------|--------|
| **主程序内嵌测试** | 模块功能验证、集成测试 | 真实环境、易于集成、自动化程度高 | 影响主程序体积、调试复杂 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **独立测试程序** | 单元测试、性能测试 | 隔离性好、专业测试框架 | 环境差异、维护成本高 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **日志分析测试** | 运行时监控、问题诊断 | 非侵入式、历史追踪 | 被动检测、分析复杂 | ⭐⭐ | ⭐⭐⭐⭐ |
| **模拟信号测试** | 事件驱动测试、UI测试 | 模拟真实操作、异步测试 | 时序复杂、状态管理难 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **断点调试** | 问题定位、逻辑验证 | 精确控制、状态观察 | 手动操作、无法自动化 | ⭐⭐ | ⭐⭐⭐ |
| **自动化测试** | 回归测试、CI/CD | 高效重复、持续验证 | 初期投入大、维护成本高 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 主程序内嵌测试方案（推荐）

主程序内嵌测试 只能是一些需要手动点击的操作，点击 某个button，加载某个文件。可以添加 模拟信号和硬编码加载 测试程序？需要增加代码的，可以考虑用独立测试程序测试

### 实施方法

#### 1. CMakeLists.txt配置
```cmake
# 测试选项配置
option(ENABLE_CONFIG_TESTS "Enable configuration module tests" OFF)
option(ENABLE_CONFIG_MOCK_SIGNALS "Enable configuration mock signal tests" OFF)
option(ENABLE_CONFIG_VALIDATION "Enable configuration validation tests" OFF)

# 测试宏定义
if (ENABLE_CONFIG_TESTS)
    message(STATUS "ENABLE_CONFIG_TESTS ON")
    add_compile_definitions(ENABLE_CONFIG_TESTS)
endif()
```

#### 2. 主程序测试代码
```cpp
#ifdef ENABLE_CONFIG_TESTS
class ConfigModuleTest : public QObject {
    Q_OBJECT
public slots:
    void testConfigRegistration();
    void testDynamicConfigManager();
    void testConfigSerialization();
};

int main() {
    // 创建测试实例
    ConfigModuleTest *configTest = new ConfigModuleTest(&app);

    // 延迟启动测试
    QTimer::singleShot(2000, configTest, &ConfigModuleTest::testConfigRegistration);
}
#endif
```

#### 3. 模拟信号触发测试
```cpp
void testConfigRegistration() {
    qDebug() << "开始测试配置类型注册";

    // 执行测试逻辑
    auto &registry = Config::ConfigTypeRegistry::getInstance();
    bool isRegistered = registry.isRegistered("Algorithm");
    qDebug() << "算法配置注册状态:" << (isRegistered ? "已注册" : "未注册");

    // 延迟触发下一个测试
    QTimer::singleShot(1000, this, &ConfigModuleTest::testDynamicConfigManager);
}
```

### 优势特点

1. **真实环境测试** - 在实际运行环境中测试
2. **自动化执行** - 程序启动后自动运行测试
3. **集成度高** - 直接测试模块间交互
4. **日志输出** - 通过qDebug输出详细测试信息
5. **条件编译** - 通过宏控制是否包含测试代码

## 日志记录与分析

### 日志级别设计
```cpp
enum class LogLevel {
    DEBUG,    // 调试信息
    INFO,     // 一般信息
    WARNING,  // 警告信息
    ERROR,    // 错误信息
    CRITICAL  // 严重错误
};
```

### 结构化日志格式
```
[2024-01-15 10:30:45.123] [ConfigManager] [INFO] 算法配置加载成功: Algorithm
[2024-01-15 10:30:45.124] [ConfigManager] [DEBUG] 参数数量: 25, 插值类型: 2
[2024-01-15 10:30:45.125] [ConfigManager] [WARNING] 参数 'threshold' 超出范围: 300 (有效范围: 0-255)
```

### 日志分析工具
- **实时监控** - 程序运行时实时查看日志
- **历史分析** - 分析历史日志文件
- **错误统计** - 统计错误类型和频率
- **性能分析** - 分析操作耗时

## 其他测试方案

### 独立测试程序
```cpp
// 独立的测试可执行文件
int main() {
    QCoreApplication app(argc, argv);

    // 运行所有测试
    TestRunner runner;
    runner.addTest(new ConfigModuleTest());
    runner.addTest(new AlgorithmTest());

    return runner.exec();
}
```

### 单元测试框架
```cpp
// Qt Test框架
class TestAlgorithmConfig : public QObject {
    Q_OBJECT
private slots:
    void testParameterSetting();
    void testConfigValidation();
};

QTEST_MAIN(TestAlgorithmConfig)
```

### 性能测试
```cpp
void performanceTest() {
    QElapsedTimer timer;
    timer.start();

    // 执行测试操作
    for (int i = 0; i < 10000; ++i) {
        config.setParameter("test", i);
    }

    qint64 elapsed = timer.elapsed();
    qDebug() << "操作耗时:" << elapsed << "ms";
}
```

## 调试技巧

### 断点调试
- **条件断点** - 只在特定条件下停止
- **数据断点** - 监控变量值变化
- **函数断点** - 在函数入口处停止

### 内存调试
- **内存泄漏检测** - 使用Valgrind或Application Verifier
- **内存使用监控** - 监控内存分配和释放
- **智能指针使用** - 避免手动内存管理

### 多线程调试
- **线程安全检查** - 检测竞态条件
- **死锁检测** - 识别潜在的死锁情况
- **线程同步验证** - 验证锁的正确使用