#include "ConfigTypeRegistry.h"
#include <QDebug>
#include <QSet>
#include <algorithm>

namespace Config {

// 静态成员初始化
ConfigTypeRegistry* ConfigTypeRegistry::s_instance = nullptr;
QMutex ConfigTypeRegistry::s_instanceMutex;

ConfigTypeRegistry &ConfigTypeRegistry::getInstance() {
    QMutexLocker locker(&s_instanceMutex);
    if (!s_instance) {
        s_instance = new ConfigTypeRegistry();
    }
    return *s_instance;
}

bool ConfigTypeRegistry::unregisterConfigType(const QString &typeName) {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_typeInfos.find(typeName);
    if (it == m_typeInfos.end()) {
        logWarning(QString("Config type '%1' not found for unregistration").arg(typeName));
        return false;
    }
    
    // 检查是否有其他类型依赖于此类型
    for (const auto &pair : m_typeInfos) {
        if (pair.second.dependencies.contains(typeName)) {
            logError(QString("Cannot unregister config type '%1': it is required by '%2'")
                    .arg(typeName, pair.first));
            return false;
        }
    }
    
    m_typeInfos.erase(it);
    logInfo(QString("Unregistered config type: %1").arg(typeName));
    return true;
}

std::unique_ptr<IConfigData> ConfigTypeRegistry::createConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_typeInfos.find(typeName);
    if (it == m_typeInfos.end()) {
        logError(QString("Config type '%1' not registered").arg(typeName));
        return nullptr;
    }
    
    try {
        auto config = it->second.factory();
        if (!config) {
            logError(QString("Factory function returned null for config type '%1'").arg(typeName));
            return nullptr;
        }
        
        // 验证创建的对象类型是否正确
        if (config->getTypeName() != typeName) {
            logWarning(QString("Created config type name mismatch: expected '%1', got '%2'")
                      .arg(typeName, config->getTypeName()));
        }
        
        logInfo(QString("Created config instance: %1").arg(typeName));
        return config;
    } catch (const std::exception &e) {
        logError(QString("Exception creating config '%1': %2").arg(typeName, e.what()));
        return nullptr;
    } catch (...) {
        logError(QString("Unknown exception creating config '%1'").arg(typeName));
        return nullptr;
    }
}

bool ConfigTypeRegistry::isRegistered(const QString &typeName) const {
    QMutexLocker locker(&m_mutex);
    return m_typeInfos.find(typeName) != m_typeInfos.end();
}

QStringList ConfigTypeRegistry::getRegisteredTypes() const {
    QMutexLocker locker(&m_mutex);
    
    QStringList types;
    for (const auto &pair : m_typeInfos) {
        types.append(pair.first);
    }
    
    return types;
}

ConfigTypeRegistry::TypeInfo ConfigTypeRegistry::getTypeInfo(const QString &typeName) const {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_typeInfos.find(typeName);
    if (it != m_typeInfos.end()) {
        return it->second;
    }
    
    return TypeInfo(); // 返回空的TypeInfo
}

std::map<QString, ConfigTypeRegistry::TypeInfo> ConfigTypeRegistry::getAllTypeInfos() const {
    QMutexLocker locker(&m_mutex);
    return m_typeInfos;
}

bool ConfigTypeRegistry::validateDependencies(const QString &typeName) const {
    QMutexLocker locker(&m_mutex);
    
    auto it = m_typeInfos.find(typeName);
    if (it == m_typeInfos.end()) {
        return false;
    }
    
    // 检查所有依赖是否都已注册
    for (const QString &dependency : it->second.dependencies) {
        if (m_typeInfos.find(dependency) == m_typeInfos.end()) {
            logError(QString("Dependency '%1' for config type '%2' is not registered")
                    .arg(dependency, typeName));
            return false;
        }
    }
    
    // 检查是否存在循环依赖
    QSet<QString> visited;
    QStringList path;
    if (checkCircularDependency(typeName, visited, path)) {
        logError(QString("Circular dependency detected for config type '%1': %2")
                .arg(typeName, path.join(" -> ")));
        return false;
    }
    
    return true;
}

QStringList ConfigTypeRegistry::getDependencyOrder() const {
    QMutexLocker locker(&m_mutex);
    
    QStringList result;
    QSet<QString> visited;
    QSet<QString> visiting;
    
    std::function<bool(const QString&)> visit = [&](const QString &typeName) -> bool {
        if (visiting.contains(typeName)) {
            // 检测到循环依赖
            return false;
        }
        
        if (visited.contains(typeName)) {
            return true;
        }
        
        visiting.insert(typeName);
        
        auto it = m_typeInfos.find(typeName);
        if (it != m_typeInfos.end()) {
            for (const QString &dependency : it->second.dependencies) {
                if (!visit(dependency)) {
                    return false;
                }
            }
        }
        
        visiting.remove(typeName);
        visited.insert(typeName);
        result.append(typeName);
        
        return true;
    };
    
    for (const auto &pair : m_typeInfos) {
        if (!visited.contains(pair.first)) {
            if (!visit(pair.first)) {
                logError("Circular dependency detected in configuration types");
                return QStringList();
            }
        }
    }
    
    return result;
}

void ConfigTypeRegistry::clear() {
    QMutexLocker locker(&m_mutex);
    m_typeInfos.clear();
    logInfo("Cleared all registered config types");
}

QVariantMap ConfigTypeRegistry::getStatistics() const {
    QMutexLocker locker(&m_mutex);
    
    QVariantMap stats;
    stats["total_types"] = static_cast<int>(m_typeInfos.size());
    stats["registered_types"] = getRegisteredTypes();
    
    QVariantMap versions;
    QVariantMap descriptions;
    QVariantMap dependencies;
    
    for (const auto &pair : m_typeInfos) {
        const TypeInfo &info = pair.second;
        versions[pair.first] = info.version;
        descriptions[pair.first] = info.description;
        dependencies[pair.first] = info.dependencies;
    }
    
    stats["versions"] = versions;
    stats["descriptions"] = descriptions;
    stats["dependencies"] = dependencies;
    
    return stats;
}

void ConfigTypeRegistry::logInfo(const QString &message) const {
    qDebug() << "[ConfigTypeRegistry]" << message;
}

void ConfigTypeRegistry::logError(const QString &message) const {
    qCritical() << "[ConfigTypeRegistry] ERROR:" << message;
}

void ConfigTypeRegistry::logWarning(const QString &message) const {
    qWarning() << "[ConfigTypeRegistry] WARNING:" << message;
}

bool ConfigTypeRegistry::checkCircularDependency(const QString &typeName, 
                                                QSet<QString> &visited, 
                                                QStringList &path) const {
    if (path.contains(typeName)) {
        path.append(typeName); // 添加循环点
        return true;
    }
    
    if (visited.contains(typeName)) {
        return false;
    }
    
    visited.insert(typeName);
    path.append(typeName);
    
    auto it = m_typeInfos.find(typeName);
    if (it != m_typeInfos.end()) {
        for (const QString &dependency : it->second.dependencies) {
            if (checkCircularDependency(dependency, visited, path)) {
                return true;
            }
        }
    }
    
    path.removeLast();
    return false;
}

}  // namespace Config
