# 配置测试程序 CMakeLists.txt

cmake_minimum_required(VERSION 3.16)

# 设置项目名称
set(TEST_PROJECT_NAME "config_test")

# 查找Qt5
find_package(Qt5 REQUIRED COMPONENTS Core)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../components/config
    ${CMAKE_CURRENT_SOURCE_DIR}/../components/lensAdjust
    ${CMAKE_CURRENT_SOURCE_DIR}/../sensor/photonSensor
    ${CMAKE_CURRENT_SOURCE_DIR}/../qtDebug
)

# 源文件
set(TEST_SOURCES
    simple_config_test.cpp
    ../components/config/ConfigManager.cpp
    ../components/config/ConfigDeployer.cpp
)

# 创建可执行文件
add_executable(${TEST_PROJECT_NAME} ${TEST_SOURCES})

# 链接Qt5库
target_link_libraries(${TEST_PROJECT_NAME} 
    Qt5::Core
)

# 设置C++标准
set_property(TARGET ${TEST_PROJECT_NAME} PROPERTY CXX_STANDARD 11)

# 输出目录
set_target_properties(${TEST_PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)
