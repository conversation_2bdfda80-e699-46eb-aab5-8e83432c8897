# 配置管理模块 CMakeLists.txt
# Configuration Management Module

cmake_minimum_required(VERSION 3.16)

# 项目名称
project(ConfigModule VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt组件
find_package(Qt5 REQUIRED COMPONENTS Core Widgets)

# 设置Qt自动处理
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_SOURCE_DIR}/common)

# 源文件列表
set(CONFIG_SOURCES
    ConfigManager.cpp
    ConfigManagerImpl.cpp
    ConfigManagerGenerate.cpp
    ConfigManagerSave.cpp
    ConfigInitializer.cpp
    IModuleConfigManager.cpp
    FaculaConfigManager.cpp
    AdjustProcessConfigManager.cpp
    AlgorithmConfigManager.cpp
    ConfigService.cpp
    DynamicConfigManager.cpp
    ConfigTypeRegistry.cpp
    IConfigData.cpp
    BaseConfigProvider.cpp
    AlgorithmConfigData.cpp
)

# 头文件列表
set(CONFIG_HEADERS
    ConfigManager.h
    ConfigInitializer.h
    ConfigTypes.h
    IModuleConfigManager.h
    FaculaConfigManager.h
    AdjustProcessConfigManager.h
    AlgorithmConfigManager.h
    IConfigProvider.h
    ConfigService.h
    DynamicConfigManager.h
    ConfigTypeRegistry.h
    IConfigData.h
    BaseConfigProvider.h
    AlgorithmConfigData.h
)

# 创建静态库
add_library(ConfigModule STATIC
    ${CONFIG_SOURCES}
    ${CONFIG_HEADERS}
)

# 链接Qt库
target_link_libraries(ConfigModule
    Qt5::Core
    Qt5::Widgets
)

# 设置库的包含目录
target_include_directories(ConfigModule PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 编译选项
target_compile_definitions(ConfigModule PRIVATE
    QT_NO_KEYWORDS
    QT_DEPRECATED_WARNINGS
)

# 安装规则
install(TARGETS ConfigModule
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${CONFIG_HEADERS}
    DESTINATION include/config
)

# 导出配置
set(ConfigModule_FOUND TRUE PARENT_SCOPE)
set(ConfigModule_INCLUDE_DIRS ${CMAKE_CURRENT_SOURCE_DIR} PARENT_SCOPE)
set(ConfigModule_LIBRARIES ConfigModule PARENT_SCOPE)
