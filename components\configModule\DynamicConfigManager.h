#pragma once

#include "ConfigTypeRegistry.h"
#include "ConfigTypes.h"
#include "IConfigData.h"
#include <QMutex>
#include <QObject>
#include <map>
#include <memory>

namespace Config {

/**
 * @brief 动态配置管理器
 *
 * 负责管理所有配置对象的生命周期和访问：
 * - 动态创建和注册配置对象
 * - 提供类型安全的配置访问接口
 * - 支持配置的加载、保存和验证
 * - 线程安全的配置操作
 *
 * 设计特点：
 * - 基于注册机制的动态配置管理
 * - 支持运行时添加新配置类型
 * - 提供统一的配置访问接口
 * - 完全符合开闭原则
 */
class DynamicConfigManager : public QObject {
    Q_OBJECT

  public:
    explicit DynamicConfigManager(QObject *parent = nullptr);
    ~DynamicConfigManager() override;

    /**
     * @brief 注册配置对象
     * @param typeName 配置类型名称
     * @return 注册是否成功
     */
    bool registerConfig(const QString &typeName);

    /**
     * @brief 注册配置对象（使用现有实例）
     * @param config 配置对象实例
     * @return 注册是否成功
     */
    bool registerConfig(std::unique_ptr<IConfigData> config);

    /**
     * @brief 注销配置对象
     * @param typeName 配置类型名称
     * @return 注销是否成功
     */
    bool unregisterConfig(const QString &typeName);

    /**
     * @brief 获取配置对象
     * @tparam T 配置类型
     * @param typeName 配置类型名称
     * @return 配置对象指针，失败时返回nullptr
     */
    template <typename T> T *getConfig(const QString &typeName) {
        static_assert(std::is_base_of_v<IConfigData, T>, "T must inherit from IConfigData");

        QMutexLocker locker(&m_mutex);

        auto it = m_configs.find(typeName);
        if (it == m_configs.end()) {
            logWarning(QString("Config '%1' not found").arg(typeName));
            return nullptr;
        }

        T *ptr = dynamic_cast<T *>(it->second.get());
        if (!ptr) {
            logError(QString("Failed to cast config '%1' to requested type").arg(typeName));
            return nullptr;
        }

        return ptr;
    }

    /**
     * @brief 获取配置对象（通用接口）
     * @param typeName 配置类型名称
     * @return 配置对象指针，失败时返回nullptr
     */
    IConfigData *getConfig(const QString &typeName);

    /**
     * @brief 检查配置是否存在
     * @param typeName 配置类型名称
     * @return 是否存在
     */
    bool hasConfig(const QString &typeName) const;

    /**
     * @brief 获取所有已注册的配置类型
     * @return 配置类型名称列表
     */
    QStringList getAllConfigs() const;

    /**
     * @brief 加载指定配置
     * @param typeName 配置类型名称
     * @param filePath 配置文件路径（可选）
     * @return 加载结果
     */
    ConfigResult loadConfig(const QString &typeName, const QString &filePath = QString());

    /**
     * @brief 保存指定配置
     * @param typeName 配置类型名称
     * @param filePath 配置文件路径（可选）
     * @return 保存结果
     */
    ConfigResult saveConfig(const QString &typeName, const QString &filePath = QString());

    /**
     * @brief 初始化所有已注册的配置
     * 检查配置文件是否存在，不存在则生成默认配置
     * @return 初始化结果
     */
    ConfigResult initializeAllConfigs();

    /**
     * @brief 加载所有配置
     * @return 加载结果
     */
    ConfigResult loadAllConfigs();

    /**
     * @brief 保存所有配置
     * @return 保存结果
     */
    ConfigResult saveAllConfigs();

    /**
     * @brief 验证指定配置
     * @param typeName 配置类型名称
     * @return 验证结果
     */
    ConfigResult validateConfig(const QString &typeName);

    /**
     * @brief 验证所有配置
     * @return 验证结果
     */
    ConfigResult validateAllConfigs();

    /**
     * @brief 重置指定配置为默认值
     * @param typeName 配置类型名称
     * @return 重置是否成功
     */
    bool resetConfig(const QString &typeName);

    /**
     * @brief 重置所有配置为默认值
     * @return 重置是否成功
     */
    bool resetAllConfigs();

    /**
     * @brief 克隆配置对象
     * @param typeName 配置类型名称
     * @return 克隆的配置对象，失败时返回nullptr
     */
    std::unique_ptr<IConfigData> cloneConfig(const QString &typeName);

    /**
     * @brief 比较两个配置是否相等
     * @param typeName1 第一个配置类型名称
     * @param typeName2 第二个配置类型名称
     * @return 是否相等
     */
    bool compareConfigs(const QString &typeName1, const QString &typeName2);

    /**
     * @brief 获取配置统计信息
     * @return 统计信息映射表
     */
    QVariantMap getStatistics() const;

    /**
     * @brief 清空所有配置
     */
    void clear();

    /**
     * @brief 获取配置文件路径
     * @param typeName 配置类型名称
     * @return 配置文件路径
     */
    QString getConfigFilePath(const QString &typeName) const;

    /**
     * @brief 设置配置文件路径
     * @param typeName 配置类型名称
     * @param filePath 配置文件路径
     */
    void setConfigFilePath(const QString &typeName, const QString &filePath);

  Q_SIGNALS:
    /**
     * @brief 配置注册信号
     * @param typeName 配置类型名称
     */
    void configRegistered(const QString &typeName);

    /**
     * @brief 配置注销信号
     * @param typeName 配置类型名称
     */
    void configUnregistered(const QString &typeName);

    /**
     * @brief 配置加载完成信号
     * @param typeName 配置类型名称
     * @param success 是否成功
     */
    void configLoaded(const QString &typeName, bool success);

    /**
     * @brief 配置保存完成信号
     * @param typeName 配置类型名称
     * @param success 是否成功
     */
    void configSaved(const QString &typeName, bool success);

    /**
     * @brief 配置变更信号
     * @param typeName 配置类型名称
     */
    void configChanged(const QString &typeName);

  private:
    /**
     * @brief 记录日志信息
     * @param message 日志消息
     */
    void logInfo(const QString &message) const;

    /**
     * @brief 记录错误信息
     * @param message 错误消息
     */
    void logError(const QString &message) const;

    /**
     * @brief 记录警告信息
     * @param message 警告消息
     */
    void logWarning(const QString &message) const;

    /**
     * @brief 确保配置目录存在
     * @param filePath 文件路径
     * @return 是否成功
     */
    bool ensureConfigDirectory(const QString &filePath) const;

    /**
     * @brief 从JSON文件加载配置
     * @param config 配置对象
     * @param filePath 文件路径
     * @return 加载结果
     */
    ConfigResult loadConfigFromJson(IConfigData *config, const QString &filePath);

    /**
     * @brief 保存配置到JSON文件
     * @param config 配置对象
     * @param filePath 文件路径
     * @return 保存结果
     */
    ConfigResult saveConfigToJson(const IConfigData *config, const QString &filePath);

    /**
     * @brief 保存配置到带注释的JSON文件
     * @param config 配置对象
     * @param filePath 文件路径
     * @return 保存结果
     */
    ConfigResult saveConfigToJsonWithComments(const IConfigData *config, const QString &filePath);

    /**
     * @brief 生成带注释的JSON字符串
     * @param config 配置对象
     * @param data 配置数据
     * @return 带注释的JSON字符串
     */
    QString generateJsonWithComments(const IConfigData *config, const QVariantMap &data);

    /**
     * @brief 格式化嵌套对象
     * @param obj 对象数据
     * @param indentLevel 缩进级别
     * @return 格式化后的字符串
     */
    QString formatNestedObject(const QVariantMap &obj, int indentLevel);

    /**
     * @brief 格式化值
     * @param value 值
     * @return 格式化后的字符串
     */
    QString formatValue(const QVariant &value);

  private:
    mutable QMutex                                  m_mutex;            // 线程安全锁
    std::map<QString, std::unique_ptr<IConfigData>> m_configs;          // 配置对象映射表
    std::map<QString, QString>                      m_configFilePaths;  // 配置文件路径映射表
    QString                                         m_baseConfigDir;    // 基础配置目录
};

}  // namespace Config
