#include "DataHandlerFactory.h"
#include "../loaders/JsonLoader.h"
#include <QFileInfo>
#include <QDebug>

namespace SaveLoad {

// 静态成员初始化
QMap<QString, DataHandlerFactory::FactoryInfo> DataHandlerFactory::s_factories;
bool DataHandlerFactory::s_initialized = false;

// 工厂方法
std::unique_ptr<IDataHandler> DataHandlerFactory::createHandler(const QString& format) {
    ensureInitialized();
    
    QString normalizedFormat = normalizeFormat(format);
    
    if (!s_factories.contains(normalizedFormat)) {
        qWarning() << "No factory registered for format:" << format;
        return nullptr;
    }
    
    const FactoryInfo& info = s_factories[normalizedFormat];
    
    if (info.factory) {
        return info.factory->createHandler();
    } else if (info.factoryFunc) {
        return info.factoryFunc();
    }
    
    qWarning() << "Invalid factory info for format:" << format;
    return nullptr;
}

std::unique_ptr<IDataHandler> DataHandlerFactory::createHandlerForFile(const QString& filePath) {
    ensureInitialized();
    
    QString extension = extractExtension(filePath);
    if (extension.isEmpty()) {
        qWarning() << "Cannot determine file format from path:" << filePath;
        return nullptr;
    }
    
    return createHandler(extension);
}

// 注册处理器工厂
void DataHandlerFactory::registerHandlerFactory(const QString& format, 
                                               std::shared_ptr<IDataHandlerFactory> factory) {
    if (!factory) {
        qWarning() << "Cannot register null factory for format:" << format;
        return;
    }
    
    QString normalizedFormat = normalizeFormat(format);
    s_factories[normalizedFormat] = FactoryInfo(factory);
    
    qDebug() << "Registered factory for format:" << format << "as" << normalizedFormat;
}

void DataHandlerFactory::registerHandlerFactory(const QString& format,
                                               std::function<std::unique_ptr<IDataHandler>()> factoryFunc) {
    if (!factoryFunc) {
        qWarning() << "Cannot register null factory function for format:" << format;
        return;
    }
    
    QString normalizedFormat = normalizeFormat(format);
    QStringList extensions = QStringList() << normalizedFormat;
    s_factories[normalizedFormat] = FactoryInfo(factoryFunc, format, extensions);
    
    qDebug() << "Registered factory function for format:" << format << "as" << normalizedFormat;
}

// 查询支持的格式
QStringList DataHandlerFactory::supportedFormats() {
    ensureInitialized();
    return s_factories.keys();
}

bool DataHandlerFactory::isFormatSupported(const QString& format) {
    ensureInitialized();
    QString normalizedFormat = normalizeFormat(format);
    return s_factories.contains(normalizedFormat);
}

bool DataHandlerFactory::canHandleFile(const QString& filePath) {
    ensureInitialized();
    
    QString extension = extractExtension(filePath);
    if (extension.isEmpty()) {
        return false;
    }
    
    return isFormatSupported(extension);
}

// 格式检测
QString DataHandlerFactory::detectFormat(const QString& filePath) {
    ensureInitialized();
    
    QString extension = extractExtension(filePath);
    if (extension.isEmpty()) {
        return QString();
    }
    
    QString normalizedFormat = normalizeFormat(extension);
    if (s_factories.contains(normalizedFormat)) {
        return normalizedFormat;
    }
    
    return QString();
}

QStringList DataHandlerFactory::detectPossibleFormats(const QString& filePath) {
    QStringList possibleFormats;
    
    QString detectedFormat = detectFormat(filePath);
    if (!detectedFormat.isEmpty()) {
        possibleFormats.append(detectedFormat);
    }
    
    // 可以添加更多的启发式检测逻辑
    // 例如，检查文件内容的前几个字节
    
    return possibleFormats;
}

// 获取格式信息
QString DataHandlerFactory::getFormatName(const QString& format) {
    ensureInitialized();
    
    QString normalizedFormat = normalizeFormat(format);
    if (s_factories.contains(normalizedFormat)) {
        return s_factories[normalizedFormat].formatName;
    }
    
    return format; // 返回原始格式名作为后备
}

QStringList DataHandlerFactory::getFormatExtensions(const QString& format) {
    ensureInitialized();
    
    QString normalizedFormat = normalizeFormat(format);
    if (s_factories.contains(normalizedFormat)) {
        return s_factories[normalizedFormat].extensions;
    }
    
    return QStringList();
}

// 工厂管理
void DataHandlerFactory::unregisterHandlerFactory(const QString& format) {
    QString normalizedFormat = normalizeFormat(format);
    s_factories.remove(normalizedFormat);
    qDebug() << "Unregistered factory for format:" << format;
}

void DataHandlerFactory::clearAllFactories() {
    s_factories.clear();
    s_initialized = false;
    qDebug() << "Cleared all registered factories";
}

// 初始化默认工厂
void DataHandlerFactory::initializeDefaultFactories() {
    if (s_initialized) {
        return;
    }
    
    qDebug() << "Initializing default data handler factories...";
    
    // 注册JSON工厂
    registerHandlerFactory("json", std::make_shared<JsonLoaderFactory>());
    
    // 可以在这里注册其他默认工厂
    // registerHandlerFactory("xml", std::make_shared<XmlLoaderFactory>());
    // registerHandlerFactory("txt", std::make_shared<TxtLoaderFactory>());
    
    s_initialized = true;
    qDebug() << "Default factories initialized. Supported formats:" << supportedFormats();
}

// 调试和诊断
void DataHandlerFactory::dumpRegisteredFactories() {
    ensureInitialized();
    
    qDebug() << "=== Registered Data Handler Factories ===";
    for (auto it = s_factories.begin(); it != s_factories.end(); ++it) {
        const QString& format = it.key();
        const FactoryInfo& info = it.value();
        
        qDebug() << QString("Format: %1").arg(format);
        qDebug() << QString("  Name: %1").arg(info.formatName);
        qDebug() << QString("  Extensions: %1").arg(info.extensions.join(", "));
        qDebug() << QString("  Factory Type: %1").arg(info.factory ? "IDataHandlerFactory" : "Function");
    }
    qDebug() << "==========================================";
}

QStringList DataHandlerFactory::getRegisteredFormats() {
    ensureInitialized();
    return s_factories.keys();
}

// 内部工具方法
QString DataHandlerFactory::normalizeFormat(const QString& format) {
    return format.toLower().trimmed();
}

QString DataHandlerFactory::extractExtension(const QString& filePath) {
    QFileInfo fileInfo(filePath);
    return fileInfo.suffix().toLower();
}

void DataHandlerFactory::ensureInitialized() {
    if (!s_initialized) {
        initializeDefaultFactories();
    }
}

} // namespace SaveLoad
