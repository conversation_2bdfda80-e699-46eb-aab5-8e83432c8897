# 测试目录结构说明

本目录包含项目的所有测试文件，按照功能和类型进行分类组织。

## 目录结构

```
test/
├── README.md                    # 本说明文件
├── CMakeLists.txt              # 测试模块构建配置
├── config/                     # 配置系统相关测试
│   ├── config_integration_test.cpp    # 配置系统集成测试
│   └── simple_config_test.cpp         # 简单配置测试
├── unit/                       # 单元测试
│   └── adjust_config_test.cpp         # 调节配置单元测试
└── integration/                # 集成测试
    └── config_system_test.cpp         # 配置系统集成测试应用
```

## 测试分类

### 1. 配置系统测试 (config/)
- **config_integration_test.cpp**: 配置系统的集成测试，测试配置加载、保存、验证等功能
- **simple_config_test.cpp**: 简单的配置功能测试

### 2. 单元测试 (unit/)
- **adjust_config_test.cpp**: 调节流程配置数据的单元测试，测试配置参数的设置和获取

### 3. 集成测试 (integration/)
- **config_system_test.cpp**: 完整的配置系统测试应用程序，支持命令行参数，可用于部署和验证

## 运行测试

### 编译测试
```bash
cd build/Debug
ninja test_targets  # 编译所有测试目标
```

### 运行特定测试
```bash
# 运行配置系统集成测试
./bin/config_system_test --test --verbose

# 运行单元测试
./bin/adjust_config_test
```

## 添加新测试

1. **单元测试**: 将新的单元测试文件放在 `unit/` 目录下
2. **集成测试**: 将集成测试文件放在 `integration/` 目录下
3. **配置测试**: 将配置相关的测试放在 `config/` 目录下
4. **更新CMakeLists.txt**: 在相应的CMakeLists.txt中添加新的测试目标

## 测试命名规范

- 单元测试: `{模块名}_test.cpp`
- 集成测试: `{系统名}_integration_test.cpp`
- 配置测试: `{配置类型}_config_test.cpp`

## 注意事项

- 所有测试文件应包含适当的头文件和依赖
- 测试应该是独立的，不依赖于特定的执行顺序
- 使用Qt测试框架或标准C++测试框架编写测试
- 确保测试覆盖主要功能和边界情况