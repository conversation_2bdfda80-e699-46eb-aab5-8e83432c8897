#pragma once

#include <QString>
#include <QStringList>
#include <QJsonObject>
#include "ConfigTypes.h"

namespace Config {

/**
 * @brief 配置文件部署管理器
 * 
 * 负责在应用程序启动时将源配置文件复制到执行目录，
 * 并处理配置文件的版本管理和更新
 */
class ConfigDeployer {
public:
    /**
     * @brief 部署结果结构
     */
    struct DeployResult {
        bool success = false;
        QString message;
        QStringList deployedFiles;
        QStringList skippedFiles;
        QStringList errorFiles;
    };

    /**
     * @brief 部署策略枚举
     */
    enum class DeployStrategy {
        OverwriteAll,      // 覆盖所有文件
        SkipExisting,      // 跳过已存在的文件
        UpdateIfNewer,     // 仅当源文件更新时覆盖
        BackupAndOverwrite // 备份后覆盖
    };

public:
    ConfigDeployer();
    ~ConfigDeployer() = default;

    /**
     * @brief 部署所有配置文件到执行目录
     * @param strategy 部署策略
     * @return 部署结果
     */
    DeployResult deployAllConfigs(DeployStrategy strategy = DeployStrategy::SkipExisting);

    /**
     * @brief 部署指定模块的配置文件
     * @param moduleName 模块名称
     * @param strategy 部署策略
     * @return 部署结果
     */
    DeployResult deployModuleConfig(const QString &moduleName, DeployStrategy strategy = DeployStrategy::SkipExisting);

    /**
     * @brief 检查配置文件是否需要更新
     * @param sourceFile 源文件路径
     * @param targetFile 目标文件路径
     * @return 是否需要更新
     */
    bool needsUpdate(const QString &sourceFile, const QString &targetFile) const;

    /**
     * @brief 创建配置文件备份
     * @param filePath 文件路径
     * @return 备份文件路径
     */
    QString createBackup(const QString &filePath) const;

    /**
     * @brief 验证配置文件完整性
     * @param filePath 配置文件路径
     * @return 验证结果
     */
    ConfigResult validateConfigFile(const QString &filePath) const;

    /**
     * @brief 获取源配置目录路径
     */
    QString getSourceConfigDir() const;

    /**
     * @brief 获取目标配置目录路径
     */
    QString getTargetConfigDir() const;

    /**
     * @brief 获取所有需要部署的配置文件列表
     */
    QStringList getConfigFileList() const;

private:
    /**
     * @brief 复制单个配置文件
     */
    bool copyConfigFile(const QString &sourceFile, const QString &targetFile, DeployStrategy strategy);

    /**
     * @brief 确保目录存在
     */
    bool ensureDirectoryExists(const QString &dirPath) const;

    /**
     * @brief 比较文件版本
     */
    int compareFileVersions(const QString &file1, const QString &file2) const;

    /**
     * @brief 从JSON文件读取版本信息
     */
    QString getConfigVersion(const QString &filePath) const;

private:
    QString m_sourceConfigDir;  // 源配置目录（项目中的配置文件）
    QString m_targetConfigDir;  // 目标配置目录（执行文件目录）
};

} // namespace Config
