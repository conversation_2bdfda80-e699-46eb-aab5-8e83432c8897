#include "ConfigTester.h"
#include "ConfigDeployer.h"
#include "ConfigService.h"
#include "DynamicConfigManager.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QElapsedTimer>
#include <QFile>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonObject>

namespace Config {

ConfigTester::ConfigTester() {
    m_verbose = true;  // 默认开启详细输出
}

ConfigTester::TestResult ConfigTester::runAllTests() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Starting comprehensive configuration tests...";

    // 运行各项测试
    TestResult loadResult          = testConfigLoading();
    TestResult validationResult    = testConfigValidation();
    TestResult compatibilityResult = testBackwardCompatibility();
    TestResult performanceResult   = testConfigPerformance();
    TestResult integrationResult   = testConfigIntegration();

    // 合并测试结果
    result.passedTests.append(loadResult.passedTests);
    result.passedTests.append(validationResult.passedTests);
    result.passedTests.append(compatibilityResult.passedTests);
    result.passedTests.append(performanceResult.passedTests);
    result.passedTests.append(integrationResult.passedTests);

    result.failedTests.append(loadResult.failedTests);
    result.failedTests.append(validationResult.failedTests);
    result.failedTests.append(compatibilityResult.failedTests);
    result.failedTests.append(performanceResult.failedTests);
    result.failedTests.append(integrationResult.failedTests);

    result.warnings.append(loadResult.warnings);
    result.warnings.append(validationResult.warnings);
    result.warnings.append(compatibilityResult.warnings);
    result.warnings.append(performanceResult.warnings);
    result.warnings.append(integrationResult.warnings);

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    result.message = QString("Tests completed in %1ms - Passed: %2, Failed: %3, Warnings: %4")
                         .arg(result.totalTime)
                         .arg(result.passedTests.size())
                         .arg(result.failedTests.size())
                         .arg(result.warnings.size());

    qDebug() << "[ConfigTester]" << result.message;

    return result;
}

ConfigTester::TestResult ConfigTester::testConfigLoading() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing configuration loading...";

    // 测试配置管理器初始化
    ConfigManager *manager = ConfigManager::getInstance();
    if (!manager) {
        result.failedTests.append("ConfigManager initialization failed");
        result.success = false;
        return result;
    }
    result.passedTests.append("ConfigManager initialization");

    // 测试配置文件部署
    ConfigDeployer               deployer;
    ConfigDeployer::DeployResult deployResult = deployer.deployAllConfigs(ConfigDeployer::DeployStrategy::SkipExisting);
    if (deployResult.success) {
        result.passedTests.append("Configuration deployment");
        qDebug() << "[ConfigTester] Deployed configs:" << deployResult.deployedFiles.size();
    } else {
        result.failedTests.append("Configuration deployment failed: " + deployResult.message);
    }

    // 测试各个配置文件加载
    QStringList configFiles = getTestConfigFiles();
    for (const QString &configFile : configFiles) {
        QStringList errors;
        if (testSingleConfigLoad(configFile, errors)) {
            result.passedTests.append("Load " + QFileInfo(configFile).baseName());
        } else {
            result.failedTests.append("Load " + QFileInfo(configFile).baseName() + ": " + errors.join(", "));
        }
    }

    // 测试完整配置加载
    ConfigResult loadResult = manager->loadAllConfigs();
    if (loadResult.success) {
        result.passedTests.append("Complete configuration loading");
    } else {
        result.failedTests.append("Complete configuration loading failed: " + loadResult.message);
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

ConfigTester::TestResult ConfigTester::testConfigValidation() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing configuration validation...";

    ConfigManager *manager = ConfigManager::getInstance();

    // 测试光斑配置验证
    const FaculaConfig &faculaConfig = manager->getFaculaConfig();
    QStringList         errors;

    // 验证光斑中心阈值
    if (faculaConfig.facula_center_peak_threshold >= 100 && faculaConfig.facula_center_peak_threshold <= 2000) {
        result.passedTests.append("Facula peak threshold validation");
    } else {
        result.failedTests.append("Facula peak threshold out of range: " + QString::number(faculaConfig.facula_center_peak_threshold));
    }

    // 验证光斑处理类型
    if (faculaConfig.facula_handle_type >= 0 && faculaConfig.facula_handle_type <= 2) {
        result.passedTests.append("Facula handle type validation");
    } else {
        result.failedTests.append("Facula handle type invalid: " + QString::number(faculaConfig.facula_handle_type));
    }

    // 验证通道配置格式
    if (!faculaConfig.facula_center_channels.isEmpty()) {
        result.passedTests.append("Facula channels format validation");
    } else {
        result.warnings.append("Facula channels configuration is empty");
    }

    // 测试算法配置验证
    const AlgorithmConfig &algorithmConfig = manager->getAlgorithmConfig();

    // 验证算法参数范围
    if (!algorithmConfig.parameters.isEmpty()) {
        result.passedTests.append("Algorithm parameters validation");
    } else {
        result.failedTests.append("Algorithm parameters are empty");
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

ConfigTester::TestResult ConfigTester::testBackwardCompatibility() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing backward compatibility...";

    ConfigManager *manager = ConfigManager::getInstance();

    // 测试兼容性参数
    const FaculaConfig &faculaConfig = manager->getFaculaConfig();

    // 检查兼容性参数是否存在
    if (faculaConfig.facula_center_loc_x >= 0 && faculaConfig.facula_center_loc_y >= 0) {
        result.passedTests.append("Legacy coordinate parameters");
    } else {
        result.warnings.append("Legacy coordinate parameters not set");
    }

    // 测试配置迁移功能
    QStringList migrationErrors;
    if (testConfigMigration(migrationErrors)) {
        result.passedTests.append("Configuration migration");
    } else {
        result.failedTests.append("Configuration migration failed: " + migrationErrors.join(", "));
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

ConfigTester::TestResult ConfigTester::testConfigPerformance() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing configuration performance...";

    ConfigManager *manager = ConfigManager::getInstance();

    // 测试配置加载性能
    QElapsedTimer loadTimer;
    loadTimer.start();

    for (int i = 0; i < 10; ++i) {
        ConfigResult loadResult = manager->loadAllConfigs();
        if (!loadResult.success) {
            result.failedTests.append("Performance test iteration " + QString::number(i));
        }
    }

    double avgLoadTime = loadTimer.elapsed() / 10.0;

    if (avgLoadTime < 100.0) {  // 平均加载时间小于100ms
        result.passedTests.append("Configuration loading performance (avg: " + QString::number(avgLoadTime, 'f', 2) + "ms)");
    } else {
        result.warnings.append("Configuration loading is slow (avg: " + QString::number(avgLoadTime, 'f', 2) + "ms)");
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

ConfigTester::TestResult ConfigTester::testConfigIntegration() {
    TestResult    result;
    QElapsedTimer timer;
    timer.start();

    qDebug() << "[ConfigTester] Testing configuration integration...";

    ConfigManager *manager = ConfigManager::getInstance();

    // 测试配置数据一致性
    const FaculaConfig &   faculaConfig    = manager->getFaculaConfig();
    const AlgorithmConfig &algorithmConfig = manager->getAlgorithmConfig();
    const HardwareConfig & hardwareConfig  = manager->getHardwareConfig();

    // 检查配置数据是否已加载
    if (!faculaConfig.facula_center_channels.isEmpty()) {
        result.passedTests.append("Facula config integration");
    } else {
        result.failedTests.append("Facula config not properly integrated");
    }

    if (!algorithmConfig.parameters.isEmpty()) {
        result.passedTests.append("Algorithm config integration");
    } else {
        result.failedTests.append("Algorithm config not properly integrated");
    }

    if (hardwareConfig.xy_radius_limit > 0) {
        result.passedTests.append("Hardware config integration");
    } else {
        result.failedTests.append("Hardware config not properly integrated");
    }

    result.totalTime = timer.elapsed();
    result.success   = result.failedTests.isEmpty();

    return result;
}

bool ConfigTester::testSingleConfigLoad(const QString &configPath, QStringList &errors) {
    if (!QFile::exists(configPath)) {
        errors.append("File not found: " + configPath);
        return false;
    }

    QFile file(configPath);
    if (!file.open(QIODevice::ReadOnly)) {
        errors.append("Cannot open file: " + configPath);
        return false;
    }

    if (configPath.endsWith(".json")) {
        QJsonParseError parseError;
        QJsonDocument   doc = QJsonDocument::fromJson(file.readAll(), &parseError);
        if (parseError.error != QJsonParseError::NoError) {
            errors.append("JSON parse error: " + parseError.errorString());
            return false;
        }
    }

    return true;
}

bool ConfigTester::testConfigMigration(QStringList &errors) {
    ConfigManager *manager = ConfigManager::getInstance();
    ConfigResult   result  = manager->migrateFromLegacyConfig();

    if (!result.success) {
        errors.append(result.message);
        return false;
    }

    return true;
}

QStringList ConfigTester::getTestConfigFiles() const {
    QStringList files;
    QString     configDir = QApplication::applicationDirPath() + "/config";

    files << configDir + "/system/system_config.json";
    files << configDir + "/modules/lenAdjust/facula_config.json";
    files << configDir + "/modules/lenAdjust/algorithm_config.json";
    files << configDir + "/modules/lenAdjust/hardware_config.json";

    return files;
}

}  // namespace Config
