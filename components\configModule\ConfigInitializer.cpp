#include "ConfigInitializer.h"
#include "ConfigService.h"
#include "DynamicConfigManager.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>

namespace Config {

ConfigInitializer::ConfigInitializer(QObject *parent) : QObject(parent) {
}

ConfigResult ConfigInitializer::initializeConfigs() {
    logInfo("Starting configuration initialization...");
    Q_EMIT initializationProgress("Starting configuration initialization...", 0);

    // 1. 创建配置目录
    if (!createConfigDirectory()) {
        Q_EMIT initializationError("Failed to create configuration directory");
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create configuration directory");
    }
    Q_EMIT initializationProgress("Configuration directory created", 20);

    // 2. 初始化配置服务
    ConfigService &       configService  = ConfigService::getInstance();
    DynamicConfigManager &dynamicManager = configService.getDynamicManager();
    ConfigResult          result         = dynamicManager.initializeAllConfigs();
    if (!result.success) {
        Q_EMIT initializationError("Failed to initialize configurations: " + result.message);
        return result;
    }
    Q_EMIT initializationProgress("Configurations initialized", 40);

    // 3. 加载所有配置
    result = dynamicManager.loadAllConfigs();
    if (!result.success) {
        Q_EMIT initializationError("Failed to load configurations: " + result.message);
        return result;
    }
    Q_EMIT initializationProgress("Configurations loaded", 70);

    // 4. 验证配置完整性
    result = dynamicManager.validateAllConfigs();
    if (!result.success) {
        logWarning("Configuration validation failed: " + result.message);
        // 验证失败不阻止初始化，只记录警告
    }
    Q_EMIT initializationProgress("Configuration validation completed", 90);

    Q_EMIT initializationProgress("Configuration initialization completed", 100);

    logInfo("Configuration initialization completed successfully");
    Q_EMIT initializationComplete();
    return ConfigResult(true);
}

ConfigResult ConfigInitializer::checkConfigIntegrity() {
    logInfo("Checking configuration file integrity...");

    ConfigService &       configService  = ConfigService::getInstance();
    DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 验证所有已注册的配置
    ConfigResult result = dynamicManager.validateAllConfigs();
    if (!result.success) {
        return result;
    }

    logInfo("Configuration integrity check passed");
    return ConfigResult(true);
}

ConfigResult ConfigInitializer::validateConfigFiles() {
    logInfo("Validating configuration files...");

    ConfigService &       configService  = ConfigService::getInstance();
    DynamicConfigManager &dynamicManager = configService.getDynamicManager();

    // 尝试加载所有配置文件
    ConfigResult result = dynamicManager.loadAllConfigs();
    if (!result.success) {
        return result;
    }

    // 验证配置数据
    result = dynamicManager.validateAllConfigs();
    if (!result.success) {
        return result;
    }

    logInfo("Configuration files validation completed successfully");
    return ConfigResult(true);
}

bool ConfigInitializer::createConfigDirectory() {
    QString configDir = getConfigDirectory();
    QDir    dir;

    if (!dir.exists(configDir)) {
        if (!dir.mkpath(configDir)) {
            logError("Failed to create configuration directory: " + configDir);
            return false;
        }
        logInfo("Created configuration directory: " + configDir);
    }

    return true;
}

QString ConfigInitializer::getConfigDirectory() const {
    return QApplication::applicationDirPath() + "/config";
}

void ConfigInitializer::logInfo(const QString &message) {
    qDebug() << "[ConfigInitializer]" << message;
}

void ConfigInitializer::logError(const QString &message) {
    qCritical() << "[ConfigInitializer]" << message;
}

void ConfigInitializer::logWarning(const QString &message) {
    qWarning() << "[ConfigInitializer]" << message;
}

}  // namespace Config
