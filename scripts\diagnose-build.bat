@echo off
REM 编译问题诊断脚本
REM 用于诊断编译环境问题

echo ========================================
echo 编译环境诊断工具
echo ========================================

REM 设置编码为UTF-8
chcp 65001 > nul

echo 1. 检查编译工具...
echo.

REM 检查CMake
echo [CMake]
cmake --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ CMake未安装或不在PATH中
) else (
    echo ✅ CMake可用
)
echo.

REM 检查常见的编译器
echo [编译器检查]

REM 检查gcc
gcc --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ gcc未找到
) else (
    echo ✅ gcc可用
)

REM 检查g++
g++ --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ g++未找到
) else (
    echo ✅ g++可用
)

REM 检查MinGW特定的编译器
x86_64-w64-mingw32-gcc --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ x86_64-w64-mingw32-gcc未找到
) else (
    echo ✅ x86_64-w64-mingw32-gcc可用
)

x86_64-w64-mingw32-g++ --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ x86_64-w64-mingw32-g++未找到
) else (
    echo ✅ x86_64-w64-mingw32-g++可用
)

echo.
echo 2. 检查Qt安装...
echo.

REM 检查常见的Qt路径
set QT_PATHS=D:\Programs\Qt\Qt5.14.2\5.14.2\mingw73_64 C:\Qt\Qt5.14.2\5.14.2\mingw73_64 D:\Qt\Qt5.14.2\5.14.2\mingw73_64 C:\Qt\5.14.2\mingw73_64

for %%P in (%QT_PATHS%) do (
    if exist "%%P\bin\qmake.exe" (
        echo ✅ 找到Qt安装: %%P
        "%%P\bin\qmake.exe" --version
        echo.
    )
)

REM 检查MinGW工具链路径
echo 3. 检查MinGW工具链...
echo.

set MINGW_PATHS=D:\Programs\Qt\Qt5.14.2\Tools\mingw730_64 C:\Qt\Qt5.14.2\Tools\mingw730_64 D:\Qt\Qt5.14.2\Tools\mingw730_64 C:\Qt\Tools\mingw730_64 C:\mingw64 D:\mingw64

for %%P in (%MINGW_PATHS%) do (
    if exist "%%P\bin\gcc.exe" (
        echo ✅ 找到MinGW工具链: %%P
        "%%P\bin\gcc.exe" --version | findstr "gcc"
        echo.
    )
)

echo 4. 检查环境变量...
echo.

echo [PATH环境变量]
echo %PATH% | findstr /i qt
if %errorlevel% equ 0 (
    echo ✅ PATH中包含Qt相关路径
) else (
    echo ❌ PATH中未找到Qt相关路径
)

echo %PATH% | findstr /i mingw
if %errorlevel% equ 0 (
    echo ✅ PATH中包含MinGW相关路径
) else (
    echo ❌ PATH中未找到MinGW相关路径
)

echo.
echo 5. 检查项目配置文件...
echo.

if exist ".vscode\c_cpp_properties.json" (
    echo ✅ 找到VSCode C++配置文件
    echo Qt路径配置：
    findstr "Qt" .vscode\c_cpp_properties.json
) else (
    echo ❌ 未找到VSCode C++配置文件
)

if exist "cmake\mingw-toolchain.cmake" (
    echo ✅ 找到MinGW工具链配置文件
) else (
    echo ❌ 未找到MinGW工具链配置文件
)

echo.
echo 6. 建议的解决方案...
echo.

echo 如果遇到 "CreateProcess: No such file or directory" 错误：
echo.
echo 方案1：更新工具链配置
echo   - 编辑 cmake/mingw-toolchain.cmake
echo   - 更新MINGW_PATH和QT_PATH为实际安装路径
echo.
echo 方案2：添加环境变量
echo   - 将MinGW的bin目录添加到系统PATH
echo   - 将Qt的bin目录添加到系统PATH
echo.
echo 方案3：使用完整路径
echo   - 在CMakeLists.txt中指定完整的编译器路径
echo   - 使用CMAKE_C_COMPILER和CMAKE_CXX_COMPILER变量
echo.
echo 方案4：重新安装Qt
echo   - 确保安装时选择了MinGW组件
echo   - 使用Qt Maintenance Tool检查安装
echo.

echo ========================================
echo 诊断完成
echo ========================================

pause
