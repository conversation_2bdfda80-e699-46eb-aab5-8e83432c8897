{"_comment": "Facula configuration for lens adjustment module", "_version": "1.0", "_description": "Configuration for facula detection and adjustment parameters", "facula_center": {"_comment": "Multi-channel facula center configuration, format: x1,y1;x2,y2;x3,y3", "facula_center_channels": "2,2;1,2;3,2", "facula_center_peak_threshold": 800, "_examples": {"single_channel": "2,2", "dual_channel": "2,2;3,2", "triple_channel": "2,2;1,2;3,2"}}, "adjust_param": {"_comment": "Facula adjustment parameters", "facula_ok_times": 3, "solid_time": 5000, "facula_ng_handle": 0, "facula_handle_type": 0, "_descriptions": {"facula_ok_times": "Number of consecutive successful detections required", "solid_time": "Solidification time in milliseconds", "facula_ng_handle": "NG handling method: 0-manual, 1-continue", "facula_handle_type": "Processing type: 0-original, 1-processed facula adjustment"}}, "_migration_info": {"migrated_from": "clen_config.ini", "migration_date": "auto-generated", "original_sections": ["ADJUST_PARAM", "FACULA_HANDLE"]}}