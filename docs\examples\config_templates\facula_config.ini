# 光斑调节配置文件
# Facula Adjustment Configuration File
# 包含光斑中心配置、调节参数、判定标准和图像处理参数

[FACULA_CENTER]
# 多通道光斑中心配置 (新格式)
facula_center_channels=2,2;1,2;3,2  # 支持多通道，格式：x1,y1;x2,y2;x3,y3
facula_center_peak_threshold=800     # 多通道模式下的peak阈值

# 兼容性配置 (旧格式，如果没有多通道配置则使用此配置)
facula_center_loc_x=2  # 光斑中心坐标x (兼容性)
facula_center_loc_y=2  # 光斑中心坐标y (兼容性)

[ADJUST_PARAM]
# 调节参数
facula_ok_times=3      # 判定次数
solid_time=0           # 固化时间/ms
facula_ng_handle=1     # 光斑判定异常处理方式：0-手动处理；1-继续执行

[FACULA_HANDLE]
# 光斑处理类型
facula_handle_type=1   # 0-原光斑调节(跳过图像处理); 1-处理后光斑调节(使用图像处理算法)

[FACULA_PROCESSING]
# 图像处理配置
interpolation_type=0          # 插值类型: 0=None; 1=Bilinear; 2=Bicubic; 3=Nonlinear; 4=NearestNeighbor
filter_types=6                # 滤波器类型列表: 0=None(无滤波), 1=Kalman, 2=Convolution, 3=Median, 4=Gaussian, 5=Bilateral, 6=WeightedAverage
interpolation_offset=0.5      # 插值偏移量 (0.0-1.0)

# 滤波器参数配置
kalman_strength=1.0           # 卡尔曼滤波强度
convolution_kernel_size=3     # 卷积核大小
convolution_preset=sharpen    # 卷积预设: sharpen, blur, edge_detect, emboss, identity
median_kernel_size=3          # 中值滤波核大小
median_preset=noise_reduction # 中值滤波预设: noise_reduction, edge_preserve, artifact_removal, light_smooth
gaussian_sigma=1.0            # 高斯滤波标准差
gaussian_kernel_size=5        # 高斯滤波核大小
gaussian_preset=medium_blur   # 高斯滤波预设: light_blur, medium_blur, heavy_blur, noise_reduction
bilateral_sigma_color=75.0    # 双边滤波颜色标准差
bilateral_sigma_space=75.0    # 双边滤波空间标准差
bilateral_kernel_size=5       # 双边滤波核大小
bilateral_preset=smooth       # 双边滤波预设: smooth, detail_preserve, noise_reduce, edge_enhance
weighted_avg_kernel_size=3    # 加权均值滤波核大小
weighted_avg_preset=center_weighted  # 加权均值滤波预设: uniform, gaussian, center_weighted, edge_enhance, smooth
filter_strength=1.0           # 全局滤波强度 (0.0-1.0)

# 配置说明：
# 1. facula_center_channels: 多通道配置字符串
#    - 格式：x1,y1;x2,y2;x3,y3
#    - 每个通道用分号分隔
#    - 每个通道的x,y坐标用逗号分隔
#    - 示例：2,2;1,2;3,2 表示三个通道：(2,2), (1,2), (3,2)
#
# 2. facula_center_peak_threshold: 多通道模式下的peak阈值
#    - 当光斑中心在任一配置通道内且peak值大于此阈值时，判定为成功
#    - 默认值：800
#
# 3. 三种配置模式：
#    - 没有配置：默认使用中心通道 (兼容性模式)
#    - 配置一个通道：光斑中心只能在这个通道
#    - 配置多个通道：光斑中心在任一通道内且peak值满足要求即可，跳过后续判定
#
# 4. 多通道判定优势：
#    - 解决反光镜导致的光斑中心偏移问题
#    - 提高判定效率，成功时跳过后续对称性判定
#    - 保持完全向后兼容性
