# MinGW工具链配置文件
# 用于解决编译器路径问题

set(CMAKE_SYSTEM_NAME Windows)
set(CMAKE_SYSTEM_PROCESSOR x86_64)

# 设置编译器路径 - 根据实际安装路径调整
set(MINGW_PATH "D:/Programs/Qt/Qt5.14.2/Tools/mingw730_64")

# 检查编译器是否存在
if(EXISTS "${MINGW_PATH}/bin/gcc.exe")
    set(CMAKE_C_COMPILER "${MINGW_PATH}/bin/gcc.exe")
    set(CMAKE_CXX_COMPILER "${MINGW_PATH}/bin/g++.exe")
    set(CMAKE_RC_COMPILER "${MINGW_PATH}/bin/windres.exe")
    message(STATUS "Found MinGW at: ${MINGW_PATH}")
else()
    # 尝试其他可能的路径
    set(POSSIBLE_PATHS
        "C:/Qt/Qt5.14.2/Tools/mingw730_64"
        "C:/Qt/Tools/mingw730_64"
        "D:/Qt/Qt5.14.2/Tools/mingw730_64"
        "D:/Qt/Tools/mingw730_64"
        "C:/mingw64"
        "D:/mingw64"
    )
    
    foreach(PATH ${POSSIBLE_PATHS})
        if(EXISTS "${PATH}/bin/gcc.exe")
            set(CMAKE_C_COMPILER "${PATH}/bin/gcc.exe")
            set(CMAKE_CXX_COMPILER "${PATH}/bin/g++.exe")
            set(CMAKE_RC_COMPILER "${PATH}/bin/windres.exe")
            set(MINGW_PATH "${PATH}")
            message(STATUS "Found MinGW at: ${PATH}")
            break()
        endif()
    endforeach()
    
    if(NOT CMAKE_C_COMPILER)
        message(FATAL_ERROR "MinGW compiler not found! Please install Qt with MinGW or set MINGW_PATH manually.")
    endif()
endif()

# 设置Qt路径
set(QT_PATH "D:/Programs/Qt/Qt5.14.2/5.14.2/mingw73_64")

# 检查Qt是否存在
if(NOT EXISTS "${QT_PATH}")
    set(POSSIBLE_QT_PATHS
        "C:/Qt/Qt5.14.2/5.14.2/mingw73_64"
        "C:/Qt/5.14.2/mingw73_64"
        "D:/Qt/Qt5.14.2/5.14.2/mingw73_64"
        "D:/Qt/5.14.2/mingw73_64"
    )
    
    foreach(PATH ${POSSIBLE_QT_PATHS})
        if(EXISTS "${PATH}")
            set(QT_PATH "${PATH}")
            message(STATUS "Found Qt at: ${PATH}")
            break()
        endif()
    endforeach()
    
    if(NOT EXISTS "${QT_PATH}")
        message(WARNING "Qt path not found! Please set QT_PATH manually.")
    endif()
endif()

# 设置Qt5_DIR
set(Qt5_DIR "${QT_PATH}/lib/cmake/Qt5")

# 设置系统根目录
set(CMAKE_FIND_ROOT_PATH ${MINGW_PATH} ${QT_PATH})

# 设置查找模式
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

# 设置编译器标志
set(CMAKE_CXX_FLAGS_INIT "-Wall -Wextra")
set(CMAKE_CXX_FLAGS_DEBUG_INIT "-g -O0")
set(CMAKE_CXX_FLAGS_RELEASE_INIT "-O3 -DNDEBUG")

# 输出配置信息
message(STATUS "=== MinGW Toolchain Configuration ===")
message(STATUS "MinGW Path: ${MINGW_PATH}")
message(STATUS "Qt Path: ${QT_PATH}")
message(STATUS "C Compiler: ${CMAKE_C_COMPILER}")
message(STATUS "CXX Compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "Qt5_DIR: ${Qt5_DIR}")
message(STATUS "=====================================")

# 验证编译器
if(CMAKE_C_COMPILER)
    execute_process(
        COMMAND ${CMAKE_C_COMPILER} --version
        OUTPUT_VARIABLE GCC_VERSION_OUTPUT
        ERROR_QUIET
        OUTPUT_STRIP_TRAILING_WHITESPACE
    )
    if(GCC_VERSION_OUTPUT)
        string(REGEX MATCH "[0-9]+\\.[0-9]+\\.[0-9]+" GCC_VERSION "${GCC_VERSION_OUTPUT}")
        message(STATUS "GCC Version: ${GCC_VERSION}")
    endif()
endif()
