#pragma once

#include <QString>

namespace Config {

/**
 * @brief 错误类型枚举
 */
enum class ErrorType {
    None = 0,
    FileNotFound,
    FileReadError,
    FileWriteError,
    ParseError,
    ValidationError,
    PermissionError,
    ModuleNotFound,
    UnknownError
};

/**
 * @brief 配置操作结果
 */
struct ConfigResult {
    bool success = false;
    ErrorType errorType = ErrorType::None;
    QString message;
    
    ConfigResult() = default;
    ConfigResult(bool s) : success(s) {}
    ConfigResult(bool s, ErrorType type, const QString& msg) 
        : success(s), errorType(type), message(msg) {}
};

}  // namespace Config
