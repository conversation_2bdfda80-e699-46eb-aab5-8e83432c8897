#include "ConfigManager.h"
#include <QJsonArray>
#include <QJsonObject>

namespace Config {

// 从JSON对象加载系统配置
ConfigResult ConfigManager::loadSystemConfigFromJson(const QJsonObject &obj) {
    QJsonObject sensorBoard = obj["sensor_board"].toObject();
    m_systemConfig.version  = sensorBoard["version"].toString("2.6.1.6");

    QJsonObject mes            = obj["mes"].toObject();
    m_systemConfig.userid      = mes["userid"].toString("admin");
    m_systemConfig.op          = mes["op"].toString("105");
    m_systemConfig.work_number = mes["work_number"].toString("10001");
    m_systemConfig.work_domain = mes["work_domain"].toString("001");

    QJsonObject device                 = obj["device"].toObject();
    m_systemConfig.sensor_device       = static_cast<uint8_t>(device["sensor_device"].toInt(4));
    m_systemConfig.sensor_device_baud  = static_cast<uint32_t>(device["sensor_device_baud"].toInt(230400));
    m_systemConfig.station_number      = static_cast<uint8_t>(device["station_number"].toInt(1));
    m_systemConfig.clens_machine_brand = static_cast<uint8_t>(device["clens_machine_brand"].toInt(3));

    logInfo("Loaded system configuration from JSON");
    return ConfigResult(true);
}

// 从JSON对象加载光斑配置
ConfigResult ConfigManager::loadFaculaConfigFromJson(const QJsonObject &obj) {
    QJsonObject faculaCenter                    = obj["facula_center"].toObject();
    m_faculaConfig.facula_center_channels       = faculaCenter["facula_center_channels"].toString("2,2");
    m_faculaConfig.facula_center_peak_threshold = static_cast<uint32_t>(faculaCenter["facula_center_peak_threshold"].toInt(800));

    // 解析多通道配置
    m_faculaConfig.facula_center_points = parseFaculaCenterChannels(m_faculaConfig.facula_center_channels);

    // 设置兼容性字段
    if (!m_faculaConfig.facula_center_points.isEmpty()) {
        m_faculaConfig.facula_center_loc_x = static_cast<uint8_t>(m_faculaConfig.facula_center_points.first().x());
        m_faculaConfig.facula_center_loc_y = static_cast<uint8_t>(m_faculaConfig.facula_center_points.first().y());
    }

    QJsonObject adjustParam           = obj["adjust_param"].toObject();
    m_faculaConfig.facula_ok_times    = static_cast<uint8_t>(adjustParam["facula_ok_times"].toInt(3));
    m_faculaConfig.solid_time         = static_cast<uint32_t>(adjustParam["solid_time"].toInt(0));
    m_faculaConfig.facula_ng_handle   = static_cast<uint8_t>(adjustParam["facula_ng_handle"].toInt(1));
    m_faculaConfig.facula_handle_type = static_cast<uint8_t>(adjustParam["facula_handle_type"].toInt(1));

    logInfo("Loaded facula configuration from JSON");
    return ConfigResult(true);
}

// 从JSON对象加载硬件配置
ConfigResult ConfigManager::loadHardwareConfigFromJson(const QJsonObject &obj) {
    QJsonObject hardware             = obj["hardware"].toObject();
    m_hardwareConfig.xy_radius_limit = static_cast<uint32_t>(hardware["xy_radius_limit"].toInt(1500));
    m_hardwareConfig.z_radius_limit  = static_cast<uint32_t>(hardware["z_radius_limit"].toInt(1400));
    m_hardwareConfig.x_step_dist     = static_cast<uint8_t>(hardware["x_step_dist"].toInt(10));
    m_hardwareConfig.y_step_dist     = static_cast<uint8_t>(hardware["y_step_dist"].toInt(10));
    m_hardwareConfig.z_step_dist     = static_cast<uint8_t>(hardware["z_step_dist"].toInt(10));

    logInfo("Loaded hardware configuration from JSON");
    return ConfigResult(true);
}

// 从JSON对象加载算法配置
ConfigResult ConfigManager::loadAlgorithmConfigFromJson(const QJsonObject &obj) {
    // 加载传统算法参数
    QJsonObject parameters = obj["parameters"].toObject();
    m_algorithmConfig.parameters.clear();

    for (auto it = parameters.begin(); it != parameters.end(); ++it) {
        m_algorithmConfig.parameters[it.key()] = it.value().toInt();
    }

    // 加载图像处理算法参数
    QJsonObject imageProcessing            = obj["image_processing"].toObject();
    m_algorithmConfig.interpolation_type   = static_cast<uint8_t>(imageProcessing["interpolation_type"].toInt(0));
    m_algorithmConfig.filter_types         = imageProcessing["filter_types"].toString("6");
    m_algorithmConfig.interpolation_offset = static_cast<float>(imageProcessing["interpolation_offset"].toDouble(0.5));
    m_algorithmConfig.kalman_strength      = static_cast<float>(imageProcessing["kalman_strength"].toDouble(1.0));

    QJsonObject convolution                   = imageProcessing["convolution"].toObject();
    m_algorithmConfig.convolution_kernel_size = static_cast<uint8_t>(convolution["kernel_size"].toInt(3));
    m_algorithmConfig.convolution_preset      = convolution["preset"].toString("sharpen");

    QJsonObject median                   = imageProcessing["median"].toObject();
    m_algorithmConfig.median_kernel_size = static_cast<uint8_t>(median["kernel_size"].toInt(3));
    m_algorithmConfig.median_preset      = median["preset"].toString("noise_reduction");

    QJsonObject gaussian                   = imageProcessing["gaussian"].toObject();
    m_algorithmConfig.gaussian_sigma       = static_cast<float>(gaussian["sigma"].toDouble(1.0));
    m_algorithmConfig.gaussian_kernel_size = static_cast<uint8_t>(gaussian["kernel_size"].toInt(5));
    m_algorithmConfig.gaussian_preset      = gaussian["preset"].toString("medium_blur");

    QJsonObject bilateral                   = imageProcessing["bilateral"].toObject();
    m_algorithmConfig.bilateral_sigma_color = static_cast<float>(bilateral["sigma_color"].toDouble(75.0));
    m_algorithmConfig.bilateral_sigma_space = static_cast<float>(bilateral["sigma_space"].toDouble(75.0));
    m_algorithmConfig.bilateral_kernel_size = static_cast<uint8_t>(bilateral["kernel_size"].toInt(5));
    m_algorithmConfig.bilateral_preset      = bilateral["preset"].toString("smooth");

    QJsonObject weightedAvg                    = imageProcessing["weighted_avg"].toObject();
    m_algorithmConfig.weighted_avg_kernel_size = static_cast<uint8_t>(weightedAvg["kernel_size"].toInt(3));
    m_algorithmConfig.weighted_avg_preset      = weightedAvg["preset"].toString("center_weighted");

    m_algorithmConfig.filter_strength = static_cast<float>(imageProcessing["filter_strength"].toDouble(1.0));

    logInfo("Loaded algorithm configuration from JSON");
    return ConfigResult(true);
}

// 保存系统配置到JSON对象
void ConfigManager::saveSystemConfigToJson(QJsonObject &obj) {
    QJsonObject sensorBoard;
    sensorBoard["version"] = m_systemConfig.version;
    obj["sensor_board"]    = sensorBoard;

    QJsonObject mes;
    mes["userid"]      = m_systemConfig.userid;
    mes["op"]          = m_systemConfig.op;
    mes["work_number"] = m_systemConfig.work_number;
    mes["work_domain"] = m_systemConfig.work_domain;
    obj["mes"]         = mes;

    QJsonObject device;
    device["sensor_device"]       = m_systemConfig.sensor_device;
    device["sensor_device_baud"]  = static_cast<int>(m_systemConfig.sensor_device_baud);
    device["station_number"]      = m_systemConfig.station_number;
    device["clens_machine_brand"] = m_systemConfig.clens_machine_brand;
    obj["device"]                 = device;

    // 添加注释信息
    obj["_comment"] = "System configuration for LA-T5 project";
    obj["_version"] = "1.0";
}

// 保存光斑配置到JSON对象
void ConfigManager::saveFaculaConfigToJson(QJsonObject &obj) {
    QJsonObject faculaCenter;
    faculaCenter["facula_center_channels"]       = m_faculaConfig.facula_center_channels;
    faculaCenter["facula_center_peak_threshold"] = static_cast<int>(m_faculaConfig.facula_center_peak_threshold);
    faculaCenter["_comment"]                     = "Multi-channel facula center configuration, format: x1,y1;x2,y2;x3,y3";
    obj["facula_center"]                         = faculaCenter;

    QJsonObject adjustParam;
    adjustParam["facula_ok_times"]    = m_faculaConfig.facula_ok_times;
    adjustParam["solid_time"]         = static_cast<int>(m_faculaConfig.solid_time);
    adjustParam["facula_ng_handle"]   = m_faculaConfig.facula_ng_handle;
    adjustParam["facula_handle_type"] = m_faculaConfig.facula_handle_type;
    adjustParam["_comment"]           = "Facula adjustment parameters";
    obj["adjust_param"]               = adjustParam;

    // 添加注释信息
    obj["_comment"] = "Facula configuration for lens adjustment module";
    obj["_version"] = "1.0";
}

// 保存硬件配置到JSON对象
void ConfigManager::saveHardwareConfigToJson(QJsonObject &obj) {
    QJsonObject hardware;
    hardware["xy_radius_limit"] = static_cast<int>(m_hardwareConfig.xy_radius_limit);
    hardware["z_radius_limit"]  = static_cast<int>(m_hardwareConfig.z_radius_limit);
    hardware["x_step_dist"]     = m_hardwareConfig.x_step_dist;
    hardware["y_step_dist"]     = m_hardwareConfig.y_step_dist;
    hardware["z_step_dist"]     = m_hardwareConfig.z_step_dist;
    hardware["_comment"]        = "Hardware configuration for lens adjustment devices";
    obj["hardware"]             = hardware;

    // 添加注释信息
    obj["_comment"] = "Hardware configuration for lens adjustment module";
    obj["_version"] = "1.0";
}

// 保存算法配置到JSON对象
void ConfigManager::saveAlgorithmConfigToJson(QJsonObject &obj) {
    // 保存传统算法参数
    QJsonObject parameters;
    for (auto it = m_algorithmConfig.parameters.begin(); it != m_algorithmConfig.parameters.end(); ++it) {
        parameters[it.key()] = it.value();
    }
    obj["parameters"] = parameters;

    // 保存图像处理算法参数
    QJsonObject imageProcessing;
    imageProcessing["interpolation_type"]   = m_algorithmConfig.interpolation_type;
    imageProcessing["filter_types"]         = m_algorithmConfig.filter_types;
    imageProcessing["interpolation_offset"] = m_algorithmConfig.interpolation_offset;
    imageProcessing["kalman_strength"]      = m_algorithmConfig.kalman_strength;
    imageProcessing["filter_strength"]      = m_algorithmConfig.filter_strength;

    QJsonObject convolution;
    convolution["kernel_size"]     = m_algorithmConfig.convolution_kernel_size;
    convolution["preset"]          = m_algorithmConfig.convolution_preset;
    convolution["_comment"]        = "Convolution filter parameters";
    imageProcessing["convolution"] = convolution;

    QJsonObject median;
    median["kernel_size"]     = m_algorithmConfig.median_kernel_size;
    median["preset"]          = m_algorithmConfig.median_preset;
    median["_comment"]        = "Median filter parameters";
    imageProcessing["median"] = median;

    QJsonObject gaussian;
    gaussian["sigma"]           = m_algorithmConfig.gaussian_sigma;
    gaussian["kernel_size"]     = m_algorithmConfig.gaussian_kernel_size;
    gaussian["preset"]          = m_algorithmConfig.gaussian_preset;
    gaussian["_comment"]        = "Gaussian filter parameters";
    imageProcessing["gaussian"] = gaussian;

    QJsonObject bilateral;
    bilateral["sigma_color"]     = m_algorithmConfig.bilateral_sigma_color;
    bilateral["sigma_space"]     = m_algorithmConfig.bilateral_sigma_space;
    bilateral["kernel_size"]     = m_algorithmConfig.bilateral_kernel_size;
    bilateral["preset"]          = m_algorithmConfig.bilateral_preset;
    bilateral["_comment"]        = "Bilateral filter parameters";
    imageProcessing["bilateral"] = bilateral;

    QJsonObject weightedAvg;
    weightedAvg["kernel_size"]      = m_algorithmConfig.weighted_avg_kernel_size;
    weightedAvg["preset"]           = m_algorithmConfig.weighted_avg_preset;
    weightedAvg["_comment"]         = "Weighted average filter parameters";
    imageProcessing["weighted_avg"] = weightedAvg;

    imageProcessing["_comment"] = "Image processing algorithm parameters";
    obj["image_processing"]     = imageProcessing;

    // 添加注释信息
    obj["_comment"] = "Algorithm configuration for lens adjustment module";
    obj["_version"] = "1.0";
}

}  // namespace Config
