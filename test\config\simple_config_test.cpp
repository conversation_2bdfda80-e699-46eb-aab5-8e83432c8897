/**
 * @file simple_config_test.cpp
 * @brief 简化的配置系统测试 - 验证主软件配置加载
 * 
 * 编译命令：
 * g++ -std=c++11 -I../components/config -I../components/lensAdjust -I../sensor/photonSensor -I$QTDIR/include -L$QTDIR/lib -lQt5Core simple_config_test.cpp -o simple_config_test
 */

#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <iostream>

// 包含必要的头文件
#include "../components/config/ConfigManager.h"
#include "../components/config/ConfigDeployer.h"

void printHeader() {
    std::cout << "========================================" << std::endl;
    std::cout << "  光路调节配置系统简化测试" << std::endl;
    std::cout << "  Simple Config System Test" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
}

bool testConfigDeployment() {
    std::cout << "📦 测试配置文件部署..." << std::endl;
    
    Config::ConfigDeployer deployer;
    
    std::cout << "  源配置目录: " << deployer.getSourceConfigDir().toStdString() << std::endl;
    std::cout << "  目标配置目录: " << deployer.getTargetConfigDir().toStdString() << std::endl;
    
    Config::ConfigDeployer::DeployResult result = deployer.deployAllConfigs(
        Config::ConfigDeployer::DeployStrategy::SkipExisting
    );
    
    std::cout << "  📊 部署结果:" << std::endl;
    std::cout << "    ✅ 已部署: " << result.deployedFiles.size() << " 个文件" << std::endl;
    std::cout << "    ⏭️  已跳过: " << result.skippedFiles.size() << " 个文件" << std::endl;
    std::cout << "    ❌ 错误: " << result.errorFiles.size() << " 个文件" << std::endl;
    
    if (!result.deployedFiles.isEmpty()) {
        std::cout << "  📄 已部署的文件:" << std::endl;
        for (const QString &file : result.deployedFiles) {
            std::cout << "    - " << file.toStdString() << std::endl;
        }
    }
    
    if (!result.skippedFiles.isEmpty()) {
        std::cout << "  ⏭️  已跳过的文件:" << std::endl;
        for (const QString &file : result.skippedFiles) {
            std::cout << "    - " << file.toStdString() << std::endl;
        }
    }
    
    if (!result.errorFiles.isEmpty()) {
        std::cout << "  ❌ 错误文件:" << std::endl;
        for (const QString &file : result.errorFiles) {
            std::cout << "    - " << file.toStdString() << std::endl;
        }
        return false;
    }
    
    std::cout << "  ✅ 配置文件部署成功" << std::endl;
    return true;
}

bool testConfigLoading() {
    std::cout << "\n📖 测试配置文件加载..." << std::endl;
    
    Config::ConfigManager *manager = Config::ConfigManager::getInstance();
    if (!manager) {
        std::cout << "  ❌ 无法获取配置管理器实例" << std::endl;
        return false;
    }
    
    Config::ConfigResult result = manager->loadAllConfigs();
    if (!result.success) {
        std::cout << "  ❌ 配置加载失败: " << result.message.toStdString() << std::endl;
        return false;
    }
    
    std::cout << "  ✅ 配置加载成功" << std::endl;
    return true;
}

bool testConfigData() {
    std::cout << "\n🔍 测试配置数据..." << std::endl;
    
    Config::ConfigManager *manager = Config::ConfigManager::getInstance();
    
    // 测试光斑配置
    const Config::FaculaConfig &faculaConfig = manager->getFaculaConfig();
    std::cout << "  📊 光斑配置:" << std::endl;
    std::cout << "    通道配置: " << faculaConfig.facula_center_channels.toStdString() << std::endl;
    std::cout << "    峰值阈值: " << faculaConfig.facula_center_peak_threshold << std::endl;
    std::cout << "    处理类型: " << faculaConfig.facula_handle_type << std::endl;
    std::cout << "    中心坐标: (" << faculaConfig.facula_center_loc_x << ", " << faculaConfig.facula_center_loc_y << ")" << std::endl;
    
    // 测试算法配置
    const Config::AlgorithmConfig &algorithmConfig = manager->getAlgorithmConfig();
    std::cout << "  ⚙️ 算法配置:" << std::endl;
    std::cout << "    参数数量: " << algorithmConfig.parameters.size() << std::endl;
    
    // 显示几个关键参数
    auto it = algorithmConfig.parameters.find("find_origin_raduis");
    if (it != algorithmConfig.parameters.end()) {
        std::cout << "    搜索半径: " << it.value().toString().toStdString() << std::endl;
    }
    
    it = algorithmConfig.parameters.find("peak_ok_threshold");
    if (it != algorithmConfig.parameters.end()) {
        std::cout << "    峰值阈值: " << it.value().toString().toStdString() << std::endl;
    }
    
    it = algorithmConfig.parameters.find("z_move_step");
    if (it != algorithmConfig.parameters.end()) {
        std::cout << "    Z轴步长: " << it.value().toString().toStdString() << std::endl;
    }
    
    // 测试硬件配置
    const Config::HardwareConfig &hardwareConfig = manager->getHardwareConfig();
    std::cout << "  🔧 硬件配置:" << std::endl;
    std::cout << "    XY轴限制: " << hardwareConfig.xy_radius_limit << " 微米" << std::endl;
    std::cout << "    Z轴限制: " << hardwareConfig.z_radius_limit << " 微米" << std::endl;
    std::cout << "    X轴步距: " << hardwareConfig.x_step_dist << " 微米/脉冲" << std::endl;
    std::cout << "    Y轴步距: " << hardwareConfig.y_step_dist << " 微米/脉冲" << std::endl;
    std::cout << "    Z轴步距: " << hardwareConfig.z_step_dist << " 微米/脉冲" << std::endl;
    
    std::cout << "  ✅ 配置数据验证成功" << std::endl;
    return true;
}

bool testConfigFiles() {
    std::cout << "\n📁 测试配置文件存在性..." << std::endl;
    
    QString configDir = QCoreApplication::applicationDirPath() + "/config";
    
    QStringList requiredFiles = {
        "system/system_config.json",
        "modules/lenAdjust/facula_config.json",
        "modules/lenAdjust/algorithm_config.json",
        "modules/lenAdjust/hardware_config.json"
    };
    
    bool allFilesExist = true;
    
    for (const QString &file : requiredFiles) {
        QString fullPath = configDir + "/" + file;
        if (QFile::exists(fullPath)) {
            std::cout << "  ✅ " << file.toStdString() << std::endl;
        } else {
            std::cout << "  ❌ " << file.toStdString() << " (文件不存在)" << std::endl;
            allFilesExist = false;
        }
    }
    
    if (allFilesExist) {
        std::cout << "  ✅ 所有必需的配置文件都存在" << std::endl;
    } else {
        std::cout << "  ❌ 部分配置文件缺失" << std::endl;
    }
    
    return allFilesExist;
}

void simulateLensAdjustUsage() {
    std::cout << "\n🎯 模拟光路调节功能使用..." << std::endl;
    
    Config::ConfigManager *manager = Config::ConfigManager::getInstance();
    const Config::FaculaConfig &faculaConfig = manager->getFaculaConfig();
    const Config::AlgorithmConfig &algorithmConfig = manager->getAlgorithmConfig();
    
    // 模拟光斑检测
    std::cout << "  🔍 模拟光斑检测:" << std::endl;
    QStringList channels = faculaConfig.facula_center_channels.split(';');
    for (int i = 0; i < channels.size() && i < 3; ++i) {
        QStringList coords = channels[i].split(',');
        if (coords.size() == 2) {
            int x = coords[0].toInt();
            int y = coords[1].toInt();
            int intensity = 700 + (i * 50); // 模拟强度
            bool detected = intensity > faculaConfig.facula_center_peak_threshold;
            
            std::cout << "    通道" << (i+1) << " (" << x << "," << y << "): "
                      << "强度=" << intensity 
                      << (detected ? " ✅" : " ❌") << std::endl;
        }
    }
    
    // 模拟算法执行
    std::cout << "  ⚙️ 模拟算法执行:" << std::endl;
    auto it = algorithmConfig.parameters.find("find_origin_raduis");
    if (it != algorithmConfig.parameters.end()) {
        std::cout << "    使用搜索半径: " << it.value().toString().toStdString() << " 微米" << std::endl;
    }
    
    it = algorithmConfig.parameters.find("z_move_step");
    if (it != algorithmConfig.parameters.end()) {
        std::cout << "    使用Z轴步长: " << it.value().toString().toStdString() << " 微米" << std::endl;
    }
    
    std::cout << "    算法执行完成，调节结果: X=+25μm, Y=-15μm, Z=+8μm" << std::endl;
    
    std::cout << "  ✅ 光路调节功能模拟完成" << std::endl;
}

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    printHeader();
    
    bool success = true;
    
    // 1. 测试配置文件部署
    success &= testConfigDeployment();
    
    // 2. 测试配置文件存在性
    success &= testConfigFiles();
    
    // 3. 测试配置加载
    success &= testConfigLoading();
    
    // 4. 测试配置数据
    success &= testConfigData();
    
    // 5. 模拟光路调节功能使用
    simulateLensAdjustUsage();
    
    std::cout << "\n========================================" << std::endl;
    if (success) {
        std::cout << "🎉 所有测试通过！配置系统工作正常。" << std::endl;
        std::cout << "💡 提示：可以修改配置文件后重新运行测试。" << std::endl;
    } else {
        std::cout << "❌ 部分测试失败，请检查配置系统。" << std::endl;
    }
    std::cout << "========================================" << std::endl;
    
    return success ? 0 : 1;
}
