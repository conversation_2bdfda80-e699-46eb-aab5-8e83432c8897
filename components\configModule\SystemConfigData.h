#pragma once

#include "ConfigTypeRegistry.h"
#include "IConfigData.h"
#include <QString>
#include <QVariantMap>

namespace Config {

/**
 * @brief 系统配置数据
 *
 * 管理系统级别的所有配置参数：
 * - 传感器板卡版本信息
 * - MES系统配置
 * - 设备配置信息
 *
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在configModule中定义和管理
 * - 专门用于系统级配置
 * - 支持参数验证和默认值设置
 */
class SystemConfigData : public BaseConfigData<SystemConfigData> {
  public:
    SystemConfigData();
    ~SystemConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() {
        return "System";
    }

    // IConfigData接口实现
    QString getTypeName() const override {
        return staticTypeName();
    }
    QString getVersion() const override {
        return "1.0.0";
    }
    QString getDescription() const override {
        return "系统级配置，包含版本信息、MES系统配置和设备信息";
    }

    QVariantMap toVariantMap() const override;
    bool        fromVariantMap(const QVariantMap &data) override;
    bool        validate() const override;
    void        setDefaults() override;

    // 传感器板卡版本信息
    QString getSensorBoardVersion() const {
        return sensor_board_version;
    }
    void setSensorBoardVersion(const QString &version) {
        sensor_board_version = version;
    }

    // MES系统配置
    QString getUserId() const {
        return userid;
    }
    void setUserId(const QString &id) {
        userid = id;
    }

    QString getOp() const {
        return op;
    }
    void setOp(const QString &operation) {
        op = operation;
    }

    QString getWorkNumber() const {
        return work_number;
    }
    void setWorkNumber(const QString &number) {
        work_number = number;
    }

    QString getWorkDomain() const {
        return work_domain;
    }
    void setWorkDomain(const QString &domain) {
        work_domain = domain;
    }

    // 设备配置信息
    uint8_t getSensorDevice() const {
        return sensor_device;
    }
    void setSensorDevice(uint8_t device) {
        sensor_device = device;
    }

    uint32_t getSensorDeviceBaud() const {
        return sensor_device_baud;
    }
    void setSensorDeviceBaud(uint32_t baud) {
        sensor_device_baud = baud;
    }

    uint8_t getStationNumber() const {
        return station_number;
    }
    void setStationNumber(uint8_t number) {
        station_number = number;
    }

    uint8_t getClensMachineBrand() const {
        return clens_machine_brand;
    }
    void setClensMachineBrand(uint8_t brand) {
        clens_machine_brand = brand;
    }

    // 配置字段名称列表
    QStringList getFieldNames() const override;
    QString     getFieldType(const QString &fieldName) const override;
    QString     getFieldDescription(const QString &fieldName) const override;

    // IConfigData接口实现
    bool     hasField(const QString &fieldName) const override;
    QVariant getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool     setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool     resetField(const QString &fieldName) override;

    // 配置验证
    bool isValidSensorDevice(uint8_t device) const;
    bool isValidMachineBrand(uint8_t brand) const;
    bool isValidBaudRate(uint32_t baud) const;

    // 配置摘要
    QString getConfigSummary() const;
    bool    isDefaultConfig() const;

  private:
    // 传感器板卡版本
    QString sensor_board_version;

    // MES系统配置
    QString userid;
    QString op;
    QString work_number;
    QString work_domain;

    // 设备配置
    uint8_t  sensor_device;
    uint32_t sensor_device_baud;
    uint8_t  station_number;
    uint8_t  clens_machine_brand;

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;

    // 有效值定义
    static const QList<uint8_t>  s_validSensorDevices;
    static const QList<uint8_t>  s_validMachineBrands;
    static const QList<uint32_t> s_validBaudRates;
};

}  // namespace Config
