#ifndef DATACONTAINER_H
#define DATACONTAINER_H

#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QVariant>
#include <QStringList>
#include <QPoint>
#include <QVector>

namespace SaveLoad {

/**
 * @brief 统一数据容器类
 * 
 * 内部使用JSON作为统一数据格式，提供类型安全的数据访问接口
 * 支持基本类型、复杂对象、数组等多种数据结构
 */
class DataContainer {
public:
    DataContainer();
    explicit DataContainer(const QJsonObject& jsonObj);
    explicit DataContainer(const QJsonDocument& jsonDoc);
    DataContainer(const DataContainer& other);
    DataContainer& operator=(const DataContainer& other);
    
    // 基本类型访问
    template<typename T>
    T getValue(const QString& key, const T& defaultValue = T{}) const;
    
    template<typename T>
    void setValue(const QString& key, const T& value);
    
    // 特化模板声明
    QString getString(const QString& key, const QString& defaultValue = QString()) const;
    void setString(const QString& key, const QString& value);
    
    int getInt(const QString& key, int defaultValue = 0) const;
    void setInt(const QString& key, int value);
    
    double getDouble(const QString& key, double defaultValue = 0.0) const;
    void setDouble(const QString& key, double value);
    
    bool getBool(const QString& key, bool defaultValue = false) const;
    void setBool(const QString& key, bool value);
    
    // Qt特定类型支持
    QPoint getPoint(const QString& key, const QPoint& defaultValue = QPoint()) const;
    void setPoint(const QString& key, const QPoint& value);
    
    QVector<QPoint> getPointVector(const QString& key) const;
    void setPointVector(const QString& key, const QVector<QPoint>& value);
    
    // 复杂对象访问
    DataContainer getObject(const QString& key) const;
    void setObject(const QString& key, const DataContainer& object);
    
    // 数组访问
    QList<DataContainer> getArray(const QString& key) const;
    void setArray(const QString& key, const QList<DataContainer>& array);
    
    QStringList getStringArray(const QString& key) const;
    void setStringArray(const QString& key, const QStringList& array);
    
    QList<int> getIntArray(const QString& key) const;
    void setIntArray(const QString& key, const QList<int>& array);
    
    // 通用QVariant访问
    QVariant getVariant(const QString& key, const QVariant& defaultValue = QVariant()) const;
    void setVariant(const QString& key, const QVariant& value);
    
    // 容器操作
    bool contains(const QString& key) const;
    void remove(const QString& key);
    void clear();
    bool isEmpty() const;
    int size() const;
    
    QStringList keys() const;
    QStringList keys(const QString& prefix) const; // 获取指定前缀的键
    
    // 合并操作
    void merge(const DataContainer& other, bool overwrite = true);
    DataContainer merged(const DataContainer& other, bool overwrite = true) const;
    
    // 路径访问（支持嵌套访问，如 "parent.child.value"）
    QVariant getValueByPath(const QString& path, const QVariant& defaultValue = QVariant()) const;
    void setValueByPath(const QString& path, const QVariant& value);
    bool containsPath(const QString& path) const;
    
    // JSON转换
    QJsonObject toJsonObject() const;
    QJsonDocument toJsonDocument() const;
    QString toJsonString(bool compact = false) const;
    
    static DataContainer fromJsonObject(const QJsonObject& obj);
    static DataContainer fromJsonDocument(const QJsonDocument& doc);
    static DataContainer fromJsonString(const QString& jsonStr, bool* ok = nullptr);
    
    // 调试和诊断
    void dump() const; // 打印内容到调试输出
    QString toString() const; // 返回可读的字符串表示
    
    // 验证
    bool isValid() const;
    QStringList validate() const; // 返回验证错误列表
    
    // 操作符重载
    bool operator==(const DataContainer& other) const;
    bool operator!=(const DataContainer& other) const;
    
    // 下标访问
    QVariant operator[](const QString& key) const;
    
private:
    QJsonObject m_data;
    
    // 内部工具方法
    QJsonValue variantToJsonValue(const QVariant& variant) const;
    QVariant jsonValueToVariant(const QJsonValue& value) const;
    
    QStringList splitPath(const QString& path) const;
    QJsonValue getValueByPathInternal(const QStringList& pathParts, const QJsonValue& current) const;
    void setValueByPathInternal(const QStringList& pathParts, const QVariant& value, QJsonObject& current);
};

// 模板特化实现
template<typename T>
T DataContainer::getValue(const QString& key, const T& defaultValue) const {
    if (!m_data.contains(key)) {
        return defaultValue;
    }
    
    QVariant variant = jsonValueToVariant(m_data[key]);
    if (variant.canConvert<T>()) {
        return variant.value<T>();
    }
    
    return defaultValue;
}

template<typename T>
void DataContainer::setValue(const QString& key, const T& value) {
    QVariant variant = QVariant::fromValue(value);
    m_data[key] = variantToJsonValue(variant);
}

} // namespace SaveLoad

// 注册Qt元类型
Q_DECLARE_METATYPE(SaveLoad::DataContainer)

#endif // DATACONTAINER_H
