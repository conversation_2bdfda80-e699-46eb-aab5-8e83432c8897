#include "../common/DataHandlerFactory.h"
#include "../common/DataContainer.h"
#include <QCoreApplication>
#include <QDebug>
#include <QDir>

using namespace SaveLoad;

/**
 * @brief JSON加载器使用示例
 * 
 * 演示如何使用新的saveLoad模块进行JSON配置文件的读写
 */
void demonstrateJsonLoader() {
    qDebug() << "=== JSON Loader Demonstration ===";
    
    // 1. 创建JSON处理器
    auto handler = DataHandlerFactory::createHandler("json");
    if (!handler) {
        qDebug() << "Failed to create JSON handler";
        return;
    }
    
    qDebug() << "Created JSON handler:" << handler->getFormatName();
    
    // 2. 创建示例配置数据
    DataContainer config;
    
    // 系统配置
    DataContainer systemConfig;
    systemConfig.setString("version", "2.6.1.6");
    config.setObject("sensor_board", systemConfig);
    
    // MES配置
    DataContainer mesConfig;
    mesConfig.setString("userid", "admin");
    mesConfig.setString("op", "105");
    mesConfig.setString("work_number", "10001");
    mesConfig.setString("work_domain", "001");
    config.setObject("mes", mesConfig);
    
    // 设备配置
    DataContainer deviceConfig;
    deviceConfig.setInt("sensor_device", 4);
    deviceConfig.setInt("sensor_device_baud", 230400);
    deviceConfig.setInt("station_number", 1);
    deviceConfig.setInt("clens_machine_brand", 3);
    config.setObject("device", deviceConfig);
    
    // 光斑配置
    DataContainer faculaConfig;
    faculaConfig.setString("facula_center_channels", "2,2;1,2;3,2");
    faculaConfig.setInt("facula_center_peak_threshold", 800);
    faculaConfig.setInt("facula_ok_times", 3);
    faculaConfig.setInt("solid_time", 5000);
    faculaConfig.setInt("facula_ng_handle", 0);
    faculaConfig.setInt("facula_handle_type", 0);
    config.setObject("facula_center", faculaConfig);
    
    // 3. 保存到文件
    QString configFile = "example_config.json";
    DataResult saveResult = handler->save(configFile, config);
    
    if (saveResult.isSuccess()) {
        qDebug() << "Successfully saved configuration to" << configFile;
    } else {
        qDebug() << "Failed to save configuration:" << saveResult.message;
        return;
    }
    
    // 4. 从文件加载
    DataResult loadResult = handler->load(configFile);
    
    if (loadResult.isSuccess()) {
        qDebug() << "Successfully loaded configuration from" << configFile;
        
        // 获取加载的数据
        DataContainer loadedConfig = handler->getDataContainer();
        
        // 5. 访问配置数据
        qDebug() << "\n=== Configuration Data ===";
        
        // 访问系统版本
        DataContainer loadedSystemConfig = loadedConfig.getObject("sensor_board");
        QString version = loadedSystemConfig.getString("version");
        qDebug() << "System version:" << version;
        
        // 访问MES配置
        DataContainer loadedMesConfig = loadedConfig.getObject("mes");
        QString userid = loadedMesConfig.getString("userid");
        QString op = loadedMesConfig.getString("op");
        qDebug() << "MES - User ID:" << userid << ", OP:" << op;
        
        // 访问设备配置
        DataContainer loadedDeviceConfig = loadedConfig.getObject("device");
        int sensorDevice = loadedDeviceConfig.getInt("sensor_device");
        int baudRate = loadedDeviceConfig.getInt("sensor_device_baud");
        qDebug() << "Device - Sensor:" << sensorDevice << ", Baud Rate:" << baudRate;
        
        // 访问光斑配置
        DataContainer loadedFaculaConfig = loadedConfig.getObject("facula_center");
        QString channels = loadedFaculaConfig.getString("facula_center_channels");
        int threshold = loadedFaculaConfig.getInt("facula_center_peak_threshold");
        qDebug() << "Facula - Channels:" << channels << ", Threshold:" << threshold;
        
        // 6. 演示点向量解析
        QVector<QPoint> points = loadedFaculaConfig.getPointVector("facula_center_channels");
        qDebug() << "Parsed points:";
        for (const QPoint& point : points) {
            qDebug() << "  Point:" << point.x() << "," << point.y();
        }
        
    } else {
        qDebug() << "Failed to load configuration:" << loadResult.message;
        return;
    }
    
    // 7. 验证配置
    ValidationResult validation = handler->validate();
    if (validation.isValid()) {
        qDebug() << "\nConfiguration validation: PASSED";
    } else {
        qDebug() << "\nConfiguration validation: FAILED";
        for (const QString& error : validation.errors) {
            qDebug() << "  Error:" << error;
        }
    }
    
    if (validation.hasWarnings()) {
        qDebug() << "Validation warnings:";
        for (const QString& warning : validation.warnings) {
            qDebug() << "  Warning:" << warning;
        }
    }
    
    // 8. 演示字符串操作
    qDebug() << "\n=== String Operations ===";
    QString jsonString = handler->saveToString(config);
    qDebug() << "JSON string length:" << jsonString.length();
    
    // 从字符串加载
    auto stringHandler = DataHandlerFactory::createHandler("json");
    DataResult stringLoadResult = stringHandler->loadFromString(jsonString);
    
    if (stringLoadResult.isSuccess()) {
        qDebug() << "Successfully loaded from string";
        DataContainer stringConfig = stringHandler->getDataContainer();
        qDebug() << "Loaded config keys:" << stringConfig.keys();
    }
    
    qDebug() << "\n=== Demonstration Complete ===";
}

/**
 * @brief 演示配置迁移场景
 */
void demonstrateConfigMigration() {
    qDebug() << "\n=== Configuration Migration Demonstration ===";
    
    // 模拟从旧格式迁移到新格式的场景
    auto handler = DataHandlerFactory::createHandler("json");
    if (!handler) {
        qDebug() << "Failed to create JSON handler";
        return;
    }
    
    // 创建旧格式的配置数据（模拟从INI文件读取的数据）
    DataContainer legacyConfig;
    legacyConfig.setString("version", "V1.0.0");
    legacyConfig.setString("userid", "001");
    legacyConfig.setString("op", "OP001");
    legacyConfig.setString("work_number", "WN001");
    legacyConfig.setInt("sensor_device", 4);
    legacyConfig.setInt("sensor_device_baud", 115200);
    legacyConfig.setString("facula_center_channels", "2,2;1,2;3,2");
    legacyConfig.setInt("facula_center_peak_threshold", 800);
    legacyConfig.setInt("facula_ok_time", 3);
    legacyConfig.setInt("solid_time", 5000);
    legacyConfig.setInt("facula_ng_handle", 0);
    legacyConfig.setInt("facula_handle_type", 0);
    
    // 转换为新的结构化格式
    DataContainer newConfig;
    
    // 系统配置
    DataContainer sensorBoard;
    sensorBoard.setString("version", legacyConfig.getString("version"));
    newConfig.setObject("sensor_board", sensorBoard);
    
    // MES配置
    DataContainer mes;
    mes.setString("userid", legacyConfig.getString("userid"));
    mes.setString("op", legacyConfig.getString("op"));
    mes.setString("work_number", legacyConfig.getString("work_number"));
    mes.setString("work_domain", "001"); // 默认值
    newConfig.setObject("mes", mes);
    
    // 设备配置
    DataContainer device;
    device.setInt("sensor_device", legacyConfig.getInt("sensor_device"));
    device.setInt("sensor_device_baud", legacyConfig.getInt("sensor_device_baud"));
    device.setInt("station_number", 1); // 默认值
    device.setInt("clens_machine_brand", 1); // 默认值
    newConfig.setObject("device", device);
    
    // 光斑配置
    DataContainer faculaCenter;
    faculaCenter.setString("facula_center_channels", legacyConfig.getString("facula_center_channels"));
    faculaCenter.setInt("facula_center_peak_threshold", legacyConfig.getInt("facula_center_peak_threshold"));
    newConfig.setObject("facula_center", faculaCenter);
    
    DataContainer adjustParam;
    adjustParam.setInt("facula_ok_times", legacyConfig.getInt("facula_ok_time"));
    adjustParam.setInt("solid_time", legacyConfig.getInt("solid_time"));
    adjustParam.setInt("facula_ng_handle", legacyConfig.getInt("facula_ng_handle"));
    adjustParam.setInt("facula_handle_type", legacyConfig.getInt("facula_handle_type"));
    newConfig.setObject("adjust_param", adjustParam);
    
    // 添加迁移信息
    DataContainer migrationInfo;
    migrationInfo.setString("migrated_from", "clen_config.ini");
    migrationInfo.setString("migration_date", QDateTime::currentDateTime().toString(Qt::ISODate));
    migrationInfo.setStringArray("original_sections", QStringList() << "SENSOR_BOARD" << "MES" << "DEVICE" << "ADJUST_PARAM");
    newConfig.setObject("_migration_info", migrationInfo);
    
    // 保存迁移后的配置
    QString migratedFile = "migrated_config.json";
    DataResult saveResult = handler->save(migratedFile, newConfig);
    
    if (saveResult.isSuccess()) {
        qDebug() << "Successfully migrated configuration to" << migratedFile;
        qDebug() << "New configuration structure:";
        qDebug() << newConfig.toJsonString(false);
    } else {
        qDebug() << "Failed to save migrated configuration:" << saveResult.message;
    }
    
    qDebug() << "=== Migration Demonstration Complete ===";
}

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    // 初始化工厂
    DataHandlerFactory::initializeDefaultFactories();
    
    // 显示支持的格式
    qDebug() << "Supported formats:" << DataHandlerFactory::supportedFormats();
    
    // 运行演示
    demonstrateJsonLoader();
    demonstrateConfigMigration();
    
    // 清理示例文件
    QFile::remove("example_config.json");
    QFile::remove("migrated_config.json");
    
    return 0;
}
