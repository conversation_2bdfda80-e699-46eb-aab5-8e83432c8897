# saveLoad模块重构方案

## 现状分析

### 当前模块结构
```
saveLoad/
├── loadFile/                    # 文件加载模块
│   ├── ILoad.h/.cpp            # 加载接口
│   ├── loadXml.h/.cpp          # XML加载实现
│   └── loadTxt.h/.cpp          # TXT加载实现
└── saveFile/                    # 文件保存模块
    ├── ISaveFile.h/.cpp        # 保存接口
    ├── saveExcel.h/.cpp        # Excel保存实现
    ├── saveHtml.h/.cpp         # HTML保存实现
    └── saveTxt.h/.cpp          # TXT保存实现
```

### 当前功能特点
1. **支持格式**：XML、TXT、Excel、HTML
2. **接口设计**：基于抽象接口的多态设计
3. **数据结构**：主要使用QMap<QString, T>存储键值对
4. **使用场景**：配置文件读写、数据导出

### 存在的问题
1. **缺少JSON支持**：现代配置管理需要JSON格式
2. **接口不统一**：加载和保存接口设计不一致
3. **类型支持有限**：主要支持基本类型，缺少复杂对象支持
4. **错误处理不完善**：缺少统一的错误处理机制
5. **配置验证缺失**：没有配置格式和内容验证

## 重构目标

### 1. 统一接口设计
- 统一ILoad和ISaveFile接口设计
- 支持多种数据格式的统一访问
- 提供类型安全的数据访问方法

### 2. 扩展格式支持
- 添加JSON格式支持（重点）
- 保持现有XML、TXT等格式的兼容性
- 支持格式自动检测和转换

### 3. 增强数据类型支持
- 支持复杂对象序列化/反序列化
- 支持嵌套数据结构
- 支持数组和列表类型

### 4. 完善错误处理
- 统一的错误码和错误信息
- 详细的错误日志记录
- 异常安全的操作保证

### 5. 添加配置验证
- JSON Schema验证支持
- 配置完整性检查
- 数据类型验证

## 重构设计

### 新的模块结构
```
saveLoad/
├── common/                      # 公共组件
│   ├── IDataHandler.h/.cpp     # 统一数据处理接口
│   ├── DataTypes.h             # 数据类型定义
│   ├── ErrorHandler.h/.cpp     # 错误处理
│   └── FormatDetector.h/.cpp   # 格式检测器
├── loaders/                     # 加载器实现
│   ├── JsonLoader.h/.cpp       # JSON加载器
│   ├── XmlLoader.h/.cpp        # XML加载器（重构）
│   ├── TxtLoader.h/.cpp        # TXT加载器（重构）
│   └── LoaderFactory.h/.cpp    # 加载器工厂
├── savers/                      # 保存器实现
│   ├── JsonSaver.h/.cpp        # JSON保存器
│   ├── XmlSaver.h/.cpp         # XML保存器（重构）
│   ├── TxtSaver.h/.cpp         # TXT保存器（重构）
│   ├── ExcelSaver.h/.cpp       # Excel保存器（重构）
│   ├── HtmlSaver.h/.cpp        # HTML保存器（重构）
│   └── SaverFactory.h/.cpp     # 保存器工厂
├── validators/                  # 验证器
│   ├── IValidator.h/.cpp       # 验证接口
│   ├── JsonValidator.h/.cpp    # JSON验证器
│   └── SchemaValidator.h/.cpp  # Schema验证器
└── utils/                       # 工具类
    ├── TypeConverter.h/.cpp    # 类型转换器
    ├── PathUtils.h/.cpp        # 路径工具
    └── BackupManager.h/.cpp    # 备份管理器
```

### 核心接口设计

#### 1. 统一数据处理接口
```cpp
class IDataHandler {
public:
    virtual ~IDataHandler() = default;
    
    // 基本操作
    virtual DataResult load(const QString& filePath) = 0;
    virtual DataResult save(const QString& filePath, const DataContainer& data) = 0;
    
    // 格式支持
    virtual QStringList supportedFormats() const = 0;
    virtual bool canHandle(const QString& filePath) const = 0;
    
    // 数据访问
    virtual QVariant getValue(const QString& key) const = 0;
    virtual void setValue(const QString& key, const QVariant& value) = 0;
    virtual bool hasKey(const QString& key) const = 0;
    
    // 验证
    virtual ValidationResult validate() const = 0;
    virtual ValidationResult validateWithSchema(const QString& schemaPath) const = 0;
};
```

#### 2. 数据容器
```cpp
class DataContainer {
public:
    // 基本类型访问
    template<typename T>
    T getValue(const QString& key, const T& defaultValue = T{}) const;
    
    template<typename T>
    void setValue(const QString& key, const T& value);
    
    // 复杂对象访问
    DataContainer getObject(const QString& key) const;
    void setObject(const QString& key, const DataContainer& object);
    
    QList<DataContainer> getArray(const QString& key) const;
    void setArray(const QString& key, const QList<DataContainer>& array);
    
    // 工具方法
    QStringList keys() const;
    bool contains(const QString& key) const;
    void merge(const DataContainer& other);
    
private:
    QJsonObject m_data;  // 内部使用JSON作为统一格式
};
```

#### 3. 错误处理
```cpp
enum class DataErrorType {
    None,
    FileNotFound,
    ParseError,
    ValidationError,
    PermissionError,
    FormatNotSupported,
    SchemaError
};

class DataResult {
public:
    bool success;
    DataErrorType errorType;
    QString message;
    QString details;
    
    DataResult(bool success = true) : success(success), errorType(DataErrorType::None) {}
    DataResult(DataErrorType error, const QString& msg) : success(false), errorType(error), message(msg) {}
    
    static DataResult Success() { return DataResult(true); }
    static DataResult Error(DataErrorType type, const QString& message);
};
```

### JSON支持实现

#### JsonLoader类
```cpp
class JsonLoader : public IDataHandler {
public:
    DataResult load(const QString& filePath) override;
    DataResult save(const QString& filePath, const DataContainer& data) override;
    
    QStringList supportedFormats() const override { return {"json"}; }
    bool canHandle(const QString& filePath) const override;
    
    // JSON特有功能
    DataResult loadFromString(const QString& jsonString);
    QString saveToString(const DataContainer& data) const;
    
    // 格式化选项
    void setIndented(bool indented) { m_indented = indented; }
    void setCompact(bool compact) { m_compact = compact; }
    
private:
    bool m_indented = true;
    bool m_compact = false;
    
    DataResult parseJsonObject(const QJsonObject& obj, DataContainer& container);
    QJsonObject containerToJsonObject(const DataContainer& container) const;
};
```

### 工厂模式实现

#### DataHandlerFactory
```cpp
class DataHandlerFactory {
public:
    static std::unique_ptr<IDataHandler> createLoader(const QString& format);
    static std::unique_ptr<IDataHandler> createSaver(const QString& format);
    static std::unique_ptr<IDataHandler> createHandler(const QString& filePath);
    
    // 注册自定义处理器
    static void registerLoader(const QString& format, std::function<std::unique_ptr<IDataHandler>()> factory);
    static void registerSaver(const QString& format, std::function<std::unique_ptr<IDataHandler>()> factory);
    
    // 支持的格式
    static QStringList supportedFormats();
    
private:
    static QMap<QString, std::function<std::unique_ptr<IDataHandler>()>> s_loaderFactories;
    static QMap<QString, std::function<std::unique_ptr<IDataHandler>()>> s_saverFactories;
};
```

## 实施计划

### 阶段1：核心接口和基础设施
1. 设计并实现IDataHandler统一接口
2. 实现DataContainer数据容器
3. 实现错误处理和日志系统
4. 创建格式检测器

### 阶段2：JSON支持实现
1. 实现JsonLoader和JsonSaver
2. 添加JSON Schema验证支持
3. 实现JSON格式化和压缩选项
4. 编写JSON处理的单元测试

### 阶段3：现有格式重构
1. 重构XmlLoader，使其符合新接口
2. 重构其他现有格式处理器
3. 实现格式转换功能
4. 保持向后兼容性

### 阶段4：工厂模式和扩展性
1. 实现DataHandlerFactory
2. 添加插件式格式支持
3. 实现配置验证器
4. 完善错误处理和恢复机制

### 阶段5：集成和测试
1. 与ConfigManager集成
2. 更新使用saveLoad的代码
3. 编写完整的单元测试和集成测试
4. 性能优化和内存管理

## 向后兼容性

### 兼容策略
1. **保留原有接口**：在过渡期保留ILoad和ISaveFile接口
2. **适配器模式**：为旧接口提供新实现的适配器
3. **渐进式迁移**：逐步将使用方迁移到新接口
4. **配置迁移**：自动将旧格式配置转换为新格式

### 迁移路径
```cpp
// 旧代码
ILoad* loader = new CLoadXml();
QMap<QString, int> params;
loader->readParam("config.xml", &params);

// 新代码（推荐）
auto handler = DataHandlerFactory::createHandler("config.json");
DataContainer data;
handler->load("config.json");
int value = data.getValue<int>("param_name", 0);

// 兼容代码（过渡期）
auto adapter = std::make_unique<LoaderAdapter>(DataHandlerFactory::createHandler("config.xml"));
QMap<QString, int> params;
adapter->readParam("config.xml", &params);
```

## 风险控制

### 主要风险
1. **性能影响**：新的抽象层可能影响性能
2. **内存使用**：DataContainer可能增加内存使用
3. **兼容性问题**：现有代码的兼容性
4. **学习成本**：开发人员需要学习新接口

### 缓解措施
1. **性能测试**：在每个阶段进行性能基准测试
2. **内存优化**：使用写时复制和引用计数优化内存
3. **渐进迁移**：分阶段迁移，保持系统稳定性
4. **文档和培训**：提供详细的迁移指南和示例

## 预期收益

### 技术收益
1. **统一接口**：简化配置文件处理
2. **JSON支持**：支持现代配置管理需求
3. **类型安全**：减少类型转换错误
4. **扩展性**：易于添加新格式支持

### 维护收益
1. **代码复用**：减少重复代码
2. **错误处理**：统一的错误处理机制
3. **测试覆盖**：更好的测试覆盖率
4. **文档完善**：清晰的接口文档

### 用户收益
1. **配置灵活性**：支持多种配置格式
2. **错误诊断**：更好的错误信息
3. **数据验证**：配置正确性保证
4. **性能提升**：优化的数据处理
