#pragma once

#include "../../config/IConfigData.h"
#include "../../config/ConfigTypeRegistry.h"
#include <QString>
#include <QVariantMap>
#include <QStringList>

namespace LensAdjust {

/**
 * @brief 镜头调节流程配置数据
 * 
 * 管理镜头调节流程的所有配置参数：
 * - 光斑判定参数
 * - 流程控制参数
 * - 异常处理配置
 * 
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在lensAdjust模块中定义和管理
 * - 专门用于光路调节流程控制
 * - 支持参数验证和默认值设置
 */
class AdjustProcessConfigData : public Config::BaseConfigData<AdjustProcessConfigData> {
public:
    AdjustProcessConfigData();
    ~AdjustProcessConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() { return "AdjustProcess"; }

    // IConfigData接口实现
    QString getTypeName() const override { return staticTypeName(); }
    QString getVersion() const override { return "1.0.0"; }
    QString getDescription() const override { return "镜头调节流程配置，控制光路调节的流程参数"; }
    
    QVariantMap toVariantMap() const override;
    bool fromVariantMap(const QVariantMap &data) override;
    bool validate() const override;
    void setDefaults() override;
    
    QStringList getFieldNames() const override;
    QString getFieldType(const QString &fieldName) const override;
    QString getFieldDescription(const QString &fieldName) const override;
    bool hasField(const QString &fieldName) const override;
    QVariant getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool resetField(const QString &fieldName) override;

    // 流程参数访问接口
    /**
     * @brief 获取光斑判定次数
     * @return 光斑判定次数
     */
    uint8_t getFaculaOkTimes() const { return facula_ok_times; }

    /**
     * @brief 设置光斑判定次数
     * @param times 光斑判定次数 (1-10)
     */
    void setFaculaOkTimes(uint8_t times) { facula_ok_times = times; }

    /**
     * @brief 获取固化时间
     * @return 固化时间(毫秒)
     */
    uint32_t getSolidTime() const { return solid_time; }

    /**
     * @brief 设置固化时间
     * @param time 固化时间(毫秒) (0-10000)
     */
    void setSolidTime(uint32_t time) { solid_time = time; }

    /**
     * @brief 获取异常处理方式
     * @return 异常处理方式：0-停止，1-继续，2-重试
     */
    uint8_t getFaculaNgHandle() const { return facula_ng_handle; }

    /**
     * @brief 设置异常处理方式
     * @param handle 异常处理方式：0-停止，1-继续，2-重试
     */
    void setFaculaNgHandle(uint8_t handle) { facula_ng_handle = handle; }

    /**
     * @brief 获取异常处理方式的描述
     * @return 异常处理方式描述
     */
    QString getFaculaNgHandleDescription() const;

    /**
     * @brief 检查配置是否为默认值
     * @return 是否为默认值
     */
    bool isDefaultConfig() const;

    /**
     * @brief 获取配置摘要信息
     * @return 配置摘要字符串
     */
    QString getConfigSummary() const;

public:
    // 配置数据成员
    uint8_t  facula_ok_times;   // 光斑判定次数 (1-10)
    uint32_t solid_time;        // 固化时间(毫秒) (0-10000)
    uint8_t  facula_ng_handle;  // 异常处理方式：0-停止，1-继续，2-重试

private:
    /**
     * @brief 验证光斑判定次数
     * @param times 光斑判定次数
     * @return 是否有效
     */
    bool validateFaculaOkTimes(uint8_t times) const;

    /**
     * @brief 验证固化时间
     * @param time 固化时间
     * @return 是否有效
     */
    bool validateSolidTime(uint32_t time) const;

    /**
     * @brief 验证异常处理方式
     * @param handle 异常处理方式
     * @return 是否有效
     */
    bool validateFaculaNgHandle(uint8_t handle) const;

    // 字段信息映射表
    static const QMap<QString, QString> s_fieldTypes;
    static const QMap<QString, QString> s_fieldDescriptions;
    static const QStringList s_ngHandleDescriptions;
};

}  // namespace LensAdjust

// 自动注册调节流程配置类型
REGISTER_CONFIG_TYPE(LensAdjust::AdjustProcessConfigData, "AdjustProcess", "1.0.0", 
                    "镜头调节流程配置，控制光路调节的流程参数")
