#include "SystemConfigData.h"
#include <QDebug>

namespace Config {

// 静态成员初始化
const QMap<QString, QString> SystemConfigData::s_fieldTypes = {{"sensor_board_version", "QString"},
                                                               {"userid", "QString"},
                                                               {"op", "QString"},
                                                               {"work_number", "QString"},
                                                               {"work_domain", "QString"},
                                                               {"sensor_device", "uint8_t"},
                                                               {"sensor_device_baud", "uint32_t"},
                                                               {"station_number", "uint8_t"},
                                                               {"clens_machine_brand", "uint8_t"}};

const QMap<QString, QString> SystemConfigData::s_fieldDescriptions = {{"sensor_board_version", "传感器板卡版本号"},
                                                                      {"userid", "用户ID，超级管理员：admin；其他示例：001"},
                                                                      {"op", "操作编号，默认105"},
                                                                      {"work_number", "工单号"},
                                                                      {"work_domain", "工作域标识符"},
                                                                      {"sensor_device", "设备类型：1-D4/2-T4/3-D6/4-T5"},
                                                                      {"sensor_device_baud", "设备波特率，通常为230400"},
                                                                      {"station_number", "工站号，默认为1"},
                                                                      {"clens_machine_brand", "镜片调节设备品牌：1-顺拓；2-清河；3-清河视觉"}};

const QList<uint8_t>  SystemConfigData::s_validSensorDevices = {1, 2, 3, 4};
const QList<uint8_t>  SystemConfigData::s_validMachineBrands = {1, 2, 3};
const QList<uint32_t> SystemConfigData::s_validBaudRates     = {9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600};

SystemConfigData::SystemConfigData()
    : sensor_board_version("2.6.1.6"),
      userid("admin"),
      op("105"),
      work_number("10001"),
      work_domain("001"),
      sensor_device(4),
      sensor_device_baud(230400),
      station_number(1),
      clens_machine_brand(3) {
    setDefaults();
}

QVariantMap SystemConfigData::toVariantMap() const {
    QVariantMap map;

    // 传感器板卡信息
    QVariantMap sensorBoard;
    sensorBoard["version"] = sensor_board_version;
    map["sensor_board"]    = sensorBoard;

    // MES系统配置
    QVariantMap mes;
    mes["userid"]      = userid;
    mes["op"]          = op;
    mes["work_number"] = work_number;
    mes["work_domain"] = work_domain;
    map["mes"]         = mes;

    // 设备配置
    QVariantMap device;
    device["sensor_device"]       = sensor_device;
    device["sensor_device_baud"]  = sensor_device_baud;
    device["station_number"]      = station_number;
    device["clens_machine_brand"] = clens_machine_brand;
    map["device"]                 = device;

    // 添加元信息
    map["_type"]    = getTypeName();
    map["_version"] = getVersion();

    return map;
}

bool SystemConfigData::fromVariantMap(const QVariantMap &data) {
    try {
        // 读取传感器板卡信息
        if (data.contains("sensor_board")) {
            QVariantMap sensorBoard = data["sensor_board"].toMap();
            sensor_board_version    = sensorBoard.value("version", "2.6.1.6").toString();
        }

        // 读取MES系统配置
        if (data.contains("mes")) {
            QVariantMap mes = data["mes"].toMap();
            userid          = mes.value("userid", "admin").toString();
            op              = mes.value("op", "105").toString();
            work_number     = mes.value("work_number", "10001").toString();
            work_domain     = mes.value("work_domain", "001").toString();
        }

        // 读取设备配置
        if (data.contains("device")) {
            QVariantMap device  = data["device"].toMap();
            sensor_device       = device.value("sensor_device", 4).toUInt();
            sensor_device_baud  = device.value("sensor_device_baud", 230400).toUInt();
            station_number      = device.value("station_number", 1).toUInt();
            clens_machine_brand = device.value("clens_machine_brand", 3).toUInt();
        }

        logInfo("System config loaded successfully");
        return true;

    } catch (const std::exception &e) {
        logError(QString("Failed to load system config: %1").arg(e.what()));
        return false;
    }
}

bool SystemConfigData::validate() const {
    // 验证传感器设备类型
    if (!isValidSensorDevice(sensor_device)) {
        logError(QString("Invalid sensor device: %1").arg(sensor_device));
        return false;
    }

    // 验证机器品牌
    if (!isValidMachineBrand(clens_machine_brand)) {
        logError(QString("Invalid machine brand: %1").arg(clens_machine_brand));
        return false;
    }

    // 验证波特率
    if (!isValidBaudRate(sensor_device_baud)) {
        logError(QString("Invalid baud rate: %1").arg(sensor_device_baud));
        return false;
    }

    // 验证字符串字段不为空
    if (userid.isEmpty() || op.isEmpty() || work_number.isEmpty()) {
        logError("Required string fields cannot be empty");
        return false;
    }

    return true;
}

void SystemConfigData::setDefaults() {
    sensor_board_version = "2.6.1.6";
    userid               = "admin";
    op                   = "105";
    work_number          = "10001";
    work_domain          = "001";
    sensor_device        = 4;
    sensor_device_baud   = 230400;
    station_number       = 1;
    clens_machine_brand  = 3;

    logInfo("Set system config to default values");
}

QStringList SystemConfigData::getFieldNames() const {
    return s_fieldTypes.keys();
}

QString SystemConfigData::getFieldType(const QString &fieldName) const {
    return s_fieldTypes.value(fieldName, "unknown");
}

QString SystemConfigData::getFieldDescription(const QString &fieldName) const {
    return s_fieldDescriptions.value(fieldName, "No description available");
}

bool SystemConfigData::hasField(const QString &fieldName) const {
    return s_fieldTypes.contains(fieldName);
}

QVariant SystemConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    if (fieldName == "sensor_board_version")
        return sensor_board_version;
    if (fieldName == "userid")
        return userid;
    if (fieldName == "op")
        return op;
    if (fieldName == "work_number")
        return work_number;
    if (fieldName == "work_domain")
        return work_domain;
    if (fieldName == "sensor_device")
        return sensor_device;
    if (fieldName == "sensor_device_baud")
        return sensor_device_baud;
    if (fieldName == "station_number")
        return station_number;
    if (fieldName == "clens_machine_brand")
        return clens_machine_brand;

    return defaultValue;
}

bool SystemConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    if (fieldName == "sensor_board_version") {
        sensor_board_version = value.toString();
        return true;
    }
    if (fieldName == "userid") {
        userid = value.toString();
        return true;
    }
    if (fieldName == "op") {
        op = value.toString();
        return true;
    }
    if (fieldName == "work_number") {
        work_number = value.toString();
        return true;
    }
    if (fieldName == "work_domain") {
        work_domain = value.toString();
        return true;
    }
    if (fieldName == "sensor_device") {
        sensor_device = value.toUInt();
        return true;
    }
    if (fieldName == "sensor_device_baud") {
        sensor_device_baud = value.toUInt();
        return true;
    }
    if (fieldName == "station_number") {
        station_number = value.toUInt();
        return true;
    }
    if (fieldName == "clens_machine_brand") {
        clens_machine_brand = value.toUInt();
        return true;
    }

    return false;
}

bool SystemConfigData::resetField(const QString &fieldName) {
    if (fieldName == "sensor_board_version") {
        sensor_board_version = "2.6.1.6";
        return true;
    }
    if (fieldName == "userid") {
        userid = "admin";
        return true;
    }
    if (fieldName == "op") {
        op = "105";
        return true;
    }
    if (fieldName == "work_number") {
        work_number = "10001";
        return true;
    }
    if (fieldName == "work_domain") {
        work_domain = "001";
        return true;
    }
    if (fieldName == "sensor_device") {
        sensor_device = 4;
        return true;
    }
    if (fieldName == "sensor_device_baud") {
        sensor_device_baud = 230400;
        return true;
    }
    if (fieldName == "station_number") {
        station_number = 1;
        return true;
    }
    if (fieldName == "clens_machine_brand") {
        clens_machine_brand = 3;
        return true;
    }

    return false;
}

bool SystemConfigData::isValidSensorDevice(uint8_t device) const {
    return s_validSensorDevices.contains(device);
}

bool SystemConfigData::isValidMachineBrand(uint8_t brand) const {
    return s_validMachineBrands.contains(brand);
}

bool SystemConfigData::isValidBaudRate(uint32_t baud) const {
    return s_validBaudRates.contains(baud);
}

QString SystemConfigData::getConfigSummary() const {
    return QString("系统配置: 设备=%1, 波特率=%2, 用户=%3, 机器品牌=%4").arg(sensor_device).arg(sensor_device_baud).arg(userid).arg(clens_machine_brand);
}

bool SystemConfigData::isDefaultConfig() const {
    return sensor_board_version == "2.6.1.6" && userid == "admin" && op == "105" && work_number == "10001" && work_domain == "001" && sensor_device == 4 &&
           sensor_device_baud == 230400 && station_number == 1 && clens_machine_brand == 3;
}

}  // namespace Config

// 自动注册系统配置类型
REGISTER_CONFIG_TYPE(Config::SystemConfigData, "System", "1.0.0", "系统级配置，包含版本信息、MES系统配置和设备信息")
