#include "DataContainer.h"
#include <QDebug>
#include <QJsonParseError>
#include <QMetaType>

namespace SaveLoad {

DataContainer::DataContainer() {
    // 空构造函数，创建空的JSON对象
}

DataContainer::DataContainer(const QJsonObject &jsonObj) : m_data(jsonObj) {
}

DataContainer::DataContainer(const QJsonDocument &jsonDoc) {
    if (jsonDoc.isObject()) {
        m_data = jsonDoc.object();
    }
}

DataContainer::DataContainer(const DataContainer &other) : m_data(other.m_data) {
}

DataContainer &DataContainer::operator=(const DataContainer &other) {
    if (this != &other) {
        m_data = other.m_data;
    }
    return *this;
}

// 基本类型访问实现
QString DataContainer::getString(const QString &key, const QString &defaultValue) const {
    return getValue<QString>(key, defaultValue);
}

void DataContainer::setString(const QString &key, const QString &value) {
    setValue<QString>(key, value);
}

int DataContainer::getInt(const QString &key, int defaultValue) const {
    if (!m_data.contains(key)) {
        return defaultValue;
    }

    QJsonValue value = m_data[key];
    if (value.isDouble()) {
        return static_cast<int>(value.toDouble());
    }

    return defaultValue;
}

void DataContainer::setInt(const QString &key, int value) {
    m_data[key] = value;
}

double DataContainer::getDouble(const QString &key, double defaultValue) const {
    if (!m_data.contains(key)) {
        return defaultValue;
    }

    QJsonValue value = m_data[key];
    if (value.isDouble()) {
        return value.toDouble();
    }

    return defaultValue;
}

void DataContainer::setDouble(const QString &key, double value) {
    m_data[key] = value;
}

bool DataContainer::getBool(const QString &key, bool defaultValue) const {
    if (!m_data.contains(key)) {
        return defaultValue;
    }

    QJsonValue value = m_data[key];
    if (value.isBool()) {
        return value.toBool();
    }

    return defaultValue;
}

void DataContainer::setBool(const QString &key, bool value) {
    m_data[key] = value;
}

// Qt特定类型支持
QPoint DataContainer::getPoint(const QString &key, const QPoint &defaultValue) const {
    if (!m_data.contains(key)) {
        return defaultValue;
    }

    QJsonValue value = m_data[key];
    if (value.isString()) {
        // 支持 "x,y" 格式
        QString     str   = value.toString();
        QStringList parts = str.split(',');
        if (parts.size() == 2) {
            bool okX, okY;
            int  x = parts[0].trimmed().toInt(&okX);
            int  y = parts[1].trimmed().toInt(&okY);
            if (okX && okY) {
                return QPoint(x, y);
            }
        }
    } else if (value.isObject()) {
        // 支持 {"x": 1, "y": 2} 格式
        QJsonObject obj = value.toObject();
        if (obj.contains("x") && obj.contains("y")) {
            return QPoint(obj["x"].toInt(), obj["y"].toInt());
        }
    }

    return defaultValue;
}

void DataContainer::setPoint(const QString &key, const QPoint &value) {
    QJsonObject pointObj;
    pointObj["x"] = value.x();
    pointObj["y"] = value.y();
    m_data[key]   = pointObj;
}

QVector<QPoint> DataContainer::getPointVector(const QString &key) const {
    QVector<QPoint> result;

    if (!m_data.contains(key)) {
        return result;
    }

    QJsonValue value = m_data[key];
    if (value.isString()) {
        // 支持 "x1,y1;x2,y2;x3,y3" 格式
        QString     str    = value.toString();
        QStringList points = str.split(';');
        for (const QString &pointStr : points) {
            QStringList coords = pointStr.split(',');
            if (coords.size() == 2) {
                bool okX, okY;
                int  x = coords[0].trimmed().toInt(&okX);
                int  y = coords[1].trimmed().toInt(&okY);
                if (okX && okY) {
                    result.append(QPoint(x, y));
                }
            }
        }
    } else if (value.isArray()) {
        // 支持数组格式
        QJsonArray array = value.toArray();
        for (const QJsonValue &item : array) {
            if (item.isObject()) {
                QJsonObject obj = item.toObject();
                if (obj.contains("x") && obj.contains("y")) {
                    result.append(QPoint(obj["x"].toInt(), obj["y"].toInt()));
                }
            }
        }
    }

    return result;
}

void DataContainer::setPointVector(const QString &key, const QVector<QPoint> &value) {
    QJsonArray array;
    for (const QPoint &point : value) {
        QJsonObject pointObj;
        pointObj["x"] = point.x();
        pointObj["y"] = point.y();
        array.append(pointObj);
    }
    m_data[key] = array;
}

// 复杂对象访问
DataContainer DataContainer::getObject(const QString &key) const {
    if (!m_data.contains(key)) {
        return DataContainer();
    }

    QJsonValue value = m_data[key];
    if (value.isObject()) {
        return DataContainer(value.toObject());
    }

    return DataContainer();
}

void DataContainer::setObject(const QString &key, const DataContainer &object) {
    m_data[key] = object.toJsonObject();
}

// 数组访问
QList<DataContainer> DataContainer::getArray(const QString &key) const {
    QList<DataContainer> result;

    if (!m_data.contains(key)) {
        return result;
    }

    QJsonValue value = m_data[key];
    if (value.isArray()) {
        QJsonArray array = value.toArray();
        for (const QJsonValue &item : array) {
            if (item.isObject()) {
                result.append(DataContainer(item.toObject()));
            }
        }
    }

    return result;
}

void DataContainer::setArray(const QString &key, const QList<DataContainer> &array) {
    QJsonArray jsonArray;
    for (const DataContainer &item : array) {
        jsonArray.append(item.toJsonObject());
    }
    m_data[key] = jsonArray;
}

QStringList DataContainer::getStringArray(const QString &key) const {
    QStringList result;

    if (!m_data.contains(key)) {
        return result;
    }

    QJsonValue value = m_data[key];
    if (value.isArray()) {
        QJsonArray array = value.toArray();
        for (const QJsonValue &item : array) {
            if (item.isString()) {
                result.append(item.toString());
            }
        }
    }

    return result;
}

void DataContainer::setStringArray(const QString &key, const QStringList &array) {
    QJsonArray jsonArray;
    for (const QString &item : array) {
        jsonArray.append(item);
    }
    m_data[key] = jsonArray;
}

// 通用QVariant访问
QVariant DataContainer::getVariant(const QString &key, const QVariant &defaultValue) const {
    if (!m_data.contains(key)) {
        return defaultValue;
    }

    return jsonValueToVariant(m_data[key]);
}

void DataContainer::setVariant(const QString &key, const QVariant &value) {
    m_data[key] = variantToJsonValue(value);
}

// 容器操作
bool DataContainer::contains(const QString &key) const {
    return m_data.contains(key);
}

void DataContainer::remove(const QString &key) {
    m_data.remove(key);
}

void DataContainer::clear() {
    m_data = QJsonObject();
}

bool DataContainer::isEmpty() const {
    return m_data.isEmpty();
}

int DataContainer::size() const {
    return m_data.size();
}

QStringList DataContainer::keys() const {
    return m_data.keys();
}

QStringList DataContainer::keys(const QString &prefix) const {
    QStringList result;
    QStringList allKeys = m_data.keys();

    for (const QString &key : allKeys) {
        if (key.startsWith(prefix)) {
            result.append(key);
        }
    }

    return result;
}

// 合并操作
void DataContainer::merge(const DataContainer &other, bool overwrite) {
    QJsonObject otherData = other.toJsonObject();

    for (auto it = otherData.begin(); it != otherData.end(); ++it) {
        if (!m_data.contains(it.key()) || overwrite) {
            m_data[it.key()] = it.value();
        }
    }
}

DataContainer DataContainer::merged(const DataContainer &other, bool overwrite) const {
    DataContainer result(*this);
    result.merge(other, overwrite);
    return result;
}

// JSON转换
QJsonObject DataContainer::toJsonObject() const {
    return m_data;
}

QJsonDocument DataContainer::toJsonDocument() const {
    return QJsonDocument(m_data);
}

QString DataContainer::toJsonString(bool compact) const {
    QJsonDocument doc(m_data);
    return doc.toJson(compact ? QJsonDocument::Compact : QJsonDocument::Indented);
}

DataContainer DataContainer::fromJsonObject(const QJsonObject &obj) {
    return DataContainer(obj);
}

DataContainer DataContainer::fromJsonDocument(const QJsonDocument &doc) {
    return DataContainer(doc);
}

DataContainer DataContainer::fromJsonString(const QString &jsonStr, bool *ok) {
    QJsonParseError error;
    QJsonDocument   doc = QJsonDocument::fromJson(jsonStr.toUtf8(), &error);

    if (ok) {
        *ok = (error.error == QJsonParseError::NoError);
    }

    if (error.error != QJsonParseError::NoError) {
        return DataContainer();
    }

    return DataContainer(doc);
}

// 调试和诊断
void DataContainer::dump() const {
    qDebug() << "DataContainer contents:";
    qDebug() << toJsonString(false);
}

QString DataContainer::toString() const {
    return toJsonString(false);
}

// 验证
bool DataContainer::isValid() const {
    return !m_data.isEmpty();
}

QStringList DataContainer::validate() const {
    QStringList errors;
    // 基本验证逻辑，可以根据需要扩展
    return errors;
}

// 操作符重载
bool DataContainer::operator==(const DataContainer &other) const {
    return m_data == other.m_data;
}

bool DataContainer::operator!=(const DataContainer &other) const {
    return m_data != other.m_data;
}

QVariant DataContainer::operator[](const QString &key) const {
    return getVariant(key);
}

// 内部工具方法
QJsonValue DataContainer::variantToJsonValue(const QVariant &variant) const {
    switch (variant.type()) {
    case QVariant::Bool:
        return QJsonValue(variant.toBool());
    case QVariant::Int:
    case QVariant::UInt:
        return QJsonValue(variant.toInt());
    case QVariant::LongLong:
    case QVariant::ULongLong:
        return QJsonValue(variant.toLongLong());
    case QVariant::Double:
        return QJsonValue(variant.toDouble());
    case QVariant::String:
        return QJsonValue(variant.toString());
    case QVariant::StringList: {
        QJsonArray  array;
        QStringList list = variant.toStringList();
        for (const QString &str : list) {
            array.append(str);
        }
        return array;
    }
    default:
        // 对于其他类型，尝试转换为字符串
        return QJsonValue(variant.toString());
    }
}

QVariant DataContainer::jsonValueToVariant(const QJsonValue &value) const {
    switch (value.type()) {
    case QJsonValue::Bool:
        return QVariant(value.toBool());
    case QJsonValue::Double:
        return QVariant(value.toDouble());
    case QJsonValue::String:
        return QVariant(value.toString());
    case QJsonValue::Array: {
        QJsonArray   array = value.toArray();
        QVariantList list;
        for (const QJsonValue &item : array) {
            list.append(jsonValueToVariant(item));
        }
        return QVariant(list);
    }
    case QJsonValue::Object: {
        QJsonObject obj = value.toObject();
        QVariantMap map;
        for (auto it = obj.begin(); it != obj.end(); ++it) {
            map[it.key()] = jsonValueToVariant(it.value());
        }
        return QVariant(map);
    }
    case QJsonValue::Null:
    case QJsonValue::Undefined:
    default:
        return QVariant();
    }
}

}  // namespace SaveLoad
