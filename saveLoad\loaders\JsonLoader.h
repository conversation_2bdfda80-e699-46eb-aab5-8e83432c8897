#ifndef JSONLOADER_H
#define JSONLOADER_H

#include "../common/IDataHandler.h"
#include "../common/DataContainer.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonParseError>

namespace SaveLoad {

/**
 * @brief JSON格式数据加载器
 * 
 * 实现JSON文件的加载、保存和验证功能
 * 支持格式化输出、压缩输出等选项
 */
class JsonLoader : public IDataHandler {
public:
    JsonLoader();
    virtual ~JsonLoader();
    
    // IDataHandler接口实现
    DataResult load(const QString& filePath) override;
    DataResult save(const QString& filePath, const DataContainer& data) override;
    
    DataResult loadFromString(const QString& content) override;
    QString saveToString(const DataContainer& data) const override;
    
    QStringList supportedFormats() const override;
    bool canHandle(const QString& filePath) const override;
    QString getFormatName() const override;
    
    // 数据访问
    QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const override;
    DataResult setValue(const QString& key, const QVariant& value) override;
    bool hasKey(const QString& key) const override;
    QStringList keys() const override;
    
    DataContainer getDataContainer() const override;
    DataResult setDataContainer(const DataContainer& data) override;
    
    // 验证功能
    ValidationResult validate() const override;
    ValidationResult validateWithSchema(const QString& schemaPath) const override;
    
    // 配置选项
    void setOption(const QString& key, const QVariant& value) override;
    QVariant getOption(const QString& key, const QVariant& defaultValue = QVariant()) const override;
    
    // 错误处理
    QString getLastError() const override;
    void clearError() override;
    
    // JSON特有功能
    void setIndented(bool indented) { setOption("indented", indented); }
    bool isIndented() const { return getOption("indented", true).toBool(); }
    
    void setCompact(bool compact) { setOption("compact", compact); }
    bool isCompact() const { return getOption("compact", false).toBool(); }
    
    void setEncoding(const QString& encoding) { setOption("encoding", encoding); }
    QString getEncoding() const { return getOption("encoding", "UTF-8").toString(); }
    
    // 高级功能
    DataResult loadFromByteArray(const QByteArray& data);
    QByteArray saveToByteArray(const DataContainer& data) const;
    
    // 部分加载（只加载指定的键）
    DataResult loadPartial(const QString& filePath, const QStringList& keys);
    
    // 流式保存（适用于大文件）
    DataResult saveStream(const QString& filePath, const DataContainer& data);
    
protected:
    DataResult setLastError(DataErrorType type, const QString& message, const QString& details = QString()) override;
    
private:
    DataContainer m_data;           // 当前加载的数据
    QString m_lastError;            // 最后的错误信息
    DataErrorType m_lastErrorType;  // 最后的错误类型
    QVariantMap m_options;          // 配置选项
    
    // 内部工具方法
    bool isJsonFile(const QString& filePath) const;
    DataResult parseJsonContent(const QByteArray& content);
    QByteArray formatJsonOutput(const DataContainer& data) const;
    
    // 验证相关
    bool validateJsonSyntax(const QString& content, QString* errorMsg = nullptr) const;
    ValidationResult validateWithJsonSchema(const QString& schemaContent) const;
    
    // 错误处理
    void logError(const QString& operation, const QString& details) const;
    QString formatParseError(const QJsonParseError& error) const;
};

/**
 * @brief JSON加载器工厂
 */
class JsonLoaderFactory : public IDataHandlerFactory {
public:
    std::unique_ptr<IDataHandler> createHandler() const override;
    QStringList supportedFormats() const override;
    QString getFormatName() const override;
    bool canHandle(const QString& filePath) const override;
};

} // namespace SaveLoad

#endif // JSONLOADER_H
