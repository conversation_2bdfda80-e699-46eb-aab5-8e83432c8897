#include "AdjustProcessConfigData.h"
#include <QDebug>

namespace LensAdjust {

// 静态成员定义
const QMap<QString, QString> AdjustProcessConfigData::s_fieldTypes = {{"facula_ok_times", "uint8"}, {"solid_time", "uint32"}, {"facula_ng_handle", "uint8"}};

const QMap<QString, QString> AdjustProcessConfigData::s_fieldDescriptions = {{"facula_ok_times", "光斑判定次数 (1-10)"},
                                                                             {"solid_time", "固化时间(毫秒) (0-10000)"},
                                                                             {"facula_ng_handle", "异常处理方式：0-停止，1-继续，2-重试"}};

const QStringList AdjustProcessConfigData::s_ngHandleDescriptions = {"停止", "继续", "重试"};

AdjustProcessConfigData::AdjustProcessConfigData() {
    setDefaults();
}

QVariantMap AdjustProcessConfigData::toVariantMap() const {
    QVariantMap data;
    data["facula_ok_times"]  = facula_ok_times;
    data["solid_time"]       = solid_time;
    data["facula_ng_handle"] = facula_ng_handle;
    return data;
}

bool AdjustProcessConfigData::fromVariantMap(const QVariantMap &data) {
    bool ok = true;

    if (data.contains("facula_ok_times")) {
        uint8_t value = data["facula_ok_times"].toUInt(&ok);
        if (ok && validateFaculaOkTimes(value)) {
            facula_ok_times = value;
        } else {
            qWarning() << "Invalid facula_ok_times value:" << data["facula_ok_times"];
            ok = false;
        }
    }

    if (data.contains("solid_time")) {
        uint32_t value = data["solid_time"].toUInt(&ok);
        if (ok && validateSolidTime(value)) {
            solid_time = value;
        } else {
            qWarning() << "Invalid solid_time value:" << data["solid_time"];
            ok = false;
        }
    }

    if (data.contains("facula_ng_handle")) {
        uint8_t value = data["facula_ng_handle"].toUInt(&ok);
        if (ok && validateFaculaNgHandle(value)) {
            facula_ng_handle = value;
        } else {
            qWarning() << "Invalid facula_ng_handle value:" << data["facula_ng_handle"];
            ok = false;
        }
    }

    return ok;
}

bool AdjustProcessConfigData::validate() const {
    return validateFaculaOkTimes(facula_ok_times) && validateSolidTime(solid_time) && validateFaculaNgHandle(facula_ng_handle);
}

void AdjustProcessConfigData::setDefaults() {
    facula_ok_times  = 3;  // 默认3次
    solid_time       = 0;  // 默认0毫秒
    facula_ng_handle = 1;  // 默认继续
}

QStringList AdjustProcessConfigData::getFieldNames() const {
    return s_fieldTypes.keys();
}

QString AdjustProcessConfigData::getFieldType(const QString &fieldName) const {
    return s_fieldTypes.value(fieldName, "unknown");
}

QString AdjustProcessConfigData::getFieldDescription(const QString &fieldName) const {
    return s_fieldDescriptions.value(fieldName, "");
}

bool AdjustProcessConfigData::hasField(const QString &fieldName) const {
    return s_fieldTypes.contains(fieldName);
}

QVariant AdjustProcessConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    if (fieldName == "facula_ok_times") {
        return facula_ok_times;
    } else if (fieldName == "solid_time") {
        return solid_time;
    } else if (fieldName == "facula_ng_handle") {
        return facula_ng_handle;
    }
    return defaultValue;
}

bool AdjustProcessConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    bool ok = true;

    if (fieldName == "facula_ok_times") {
        uint8_t val = value.toUInt(&ok);
        if (ok && validateFaculaOkTimes(val)) {
            facula_ok_times = val;
            return true;
        }
    } else if (fieldName == "solid_time") {
        uint32_t val = value.toUInt(&ok);
        if (ok && validateSolidTime(val)) {
            solid_time = val;
            return true;
        }
    } else if (fieldName == "facula_ng_handle") {
        uint8_t val = value.toUInt(&ok);
        if (ok && validateFaculaNgHandle(val)) {
            facula_ng_handle = val;
            return true;
        }
    }

    return false;
}

bool AdjustProcessConfigData::resetField(const QString &fieldName) {
    if (fieldName == "facula_ok_times") {
        facula_ok_times = 3;
        return true;
    } else if (fieldName == "solid_time") {
        solid_time = 0;
        return true;
    } else if (fieldName == "facula_ng_handle") {
        facula_ng_handle = 1;
        return true;
    }

    return false;
}

QString AdjustProcessConfigData::getFaculaNgHandleDescription() const {
    if (facula_ng_handle < s_ngHandleDescriptions.size()) {
        return s_ngHandleDescriptions[facula_ng_handle];
    }
    return "未知";
}

bool AdjustProcessConfigData::isDefaultConfig() const {
    return facula_ok_times == 3 && solid_time == 0 && facula_ng_handle == 1;
}

QString AdjustProcessConfigData::getConfigSummary() const {
    return QString("光斑判定次数: %1, 固化时间: %2ms, 异常处理: %3").arg(facula_ok_times).arg(solid_time).arg(getFaculaNgHandleDescription());
}

bool AdjustProcessConfigData::validateFaculaOkTimes(uint8_t times) const {
    return times >= 1 && times <= 10;
}

bool AdjustProcessConfigData::validateSolidTime(uint32_t time) const {
    return time <= 10000;
}

bool AdjustProcessConfigData::validateFaculaNgHandle(uint8_t handle) const {
    return handle <= 2;
}

}  // namespace LensAdjust

// 自动注册调节流程配置类型
REGISTER_CONFIG_TYPE(LensAdjust::AdjustProcessConfigData, "AdjustProcess", "1.0.0", "镜头调节流程配置，控制光路调节的流程参数")
