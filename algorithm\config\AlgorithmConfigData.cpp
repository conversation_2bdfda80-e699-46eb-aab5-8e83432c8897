#include "AlgorithmConfigData.h"
#include <QDebug>

namespace Algorithm {

// 静态成员初始化
const QMap<QString, QString> AlgorithmConfigData::s_fieldTypes = {
    // 传统算法参数
    {"facula_threshold_min", "int"},
    {"facula_threshold_max", "int"},
    {"facula_area_min", "int"},
    {"facula_area_max", "int"},
    {"facula_circularity_min", "float"},
    {"facula_circularity_max", "float"},

    // 图像处理参数
    {"interpolation_type", "uint8_t"},
    {"filter_types", "QString"},
    {"interpolation_offset", "float"},
    {"kalman_strength", "float"},
    {"convolution_kernel_size", "uint8_t"},
    {"convolution_preset", "QString"},
    {"median_kernel_size", "uint8_t"},
    {"median_preset", "QString"},
    {"gaussian_sigma", "float"},
    {"gaussian_kernel_size", "uint8_t"},
    {"gaussian_preset", "QString"},
    {"bilateral_sigma_color", "float"},
    {"bilateral_sigma_space", "float"},
    {"bilateral_kernel_size", "uint8_t"},
    {"bilateral_preset", "QString"},
    {"weighted_avg_kernel_size", "uint8_t"},
    {"weighted_avg_preset", "QString"},
    {"filter_strength", "float"},

    // 光斑检测参数
    {"facula_center_channels", "QString"},
    {"facula_center_peak_threshold", "uint32_t"},
    {"facula_center_loc_x", "uint8_t"},
    {"facula_center_loc_y", "uint8_t"},
    {"facula_handle_type", "uint8_t"}};

const QMap<QString, QString> AlgorithmConfigData::s_fieldDescriptions = {
    // 传统算法参数
    {"facula_threshold_min", "光斑检测最小阈值"},
    {"facula_threshold_max", "光斑检测最大阈值"},
    {"facula_area_min", "光斑最小面积"},
    {"facula_area_max", "光斑最大面积"},
    {"facula_circularity_min", "光斑最小圆度"},
    {"facula_circularity_max", "光斑最大圆度"},

    // 图像处理参数
    {"interpolation_type", "插值类型：0-最近邻，1-双线性，2-双三次，3-Lanczos"},
    {"filter_types", "滤波器类型列表，逗号分隔"},
    {"interpolation_offset", "插值偏移量"},
    {"kalman_strength", "卡尔曼滤波强度"},
    {"convolution_kernel_size", "卷积核大小"},
    {"convolution_preset", "卷积预设"},
    {"median_kernel_size", "中值滤波核大小"},
    {"median_preset", "中值滤波预设"},
    {"gaussian_sigma", "高斯滤波标准差"},
    {"gaussian_kernel_size", "高斯滤波核大小"},
    {"gaussian_preset", "高斯滤波预设"},
    {"bilateral_sigma_color", "双边滤波颜色标准差"},
    {"bilateral_sigma_space", "双边滤波空间标准差"},
    {"bilateral_kernel_size", "双边滤波核大小"},
    {"bilateral_preset", "双边滤波预设"},
    {"weighted_avg_kernel_size", "加权均值滤波核大小"},
    {"weighted_avg_preset", "加权均值滤波预设"},
    {"filter_strength", "全局滤波强度"},

    // 光斑检测参数
    {"facula_center_channels", "多通道配置字符串"},
    {"facula_center_peak_threshold", "多通道模式下的peak阈值"},
    {"facula_center_loc_x", "单点X坐标（兼容性）"},
    {"facula_center_loc_y", "单点Y坐标（兼容性）"},
    {"facula_handle_type", "处理类型：0-基础，1-增强，2-高精度"}};

const QMap<QString, QPair<int, int>> AlgorithmConfigData::s_parameterRanges = {{"facula_threshold_min", {0, 255}},
                                                                               {"facula_threshold_max", {0, 255}},
                                                                               {"facula_area_min", {1, 10000}},
                                                                               {"facula_area_max", {1, 10000}},
                                                                               {"facula_circularity_min", {0, 100}},
                                                                               {"facula_circularity_max", {0, 100}}};

const QMap<QString, int> AlgorithmConfigData::s_defaultParameters = {{"facula_threshold_min", 50},
                                                                     {"facula_threshold_max", 200},
                                                                     {"facula_area_min", 10},
                                                                     {"facula_area_max", 1000},
                                                                     {"facula_circularity_min", 70},
                                                                     {"facula_circularity_max", 100}};

AlgorithmConfigData::AlgorithmConfigData()
    : interpolation_type(1),
      filter_types("gaussian,median"),
      interpolation_offset(0.0f),
      kalman_strength(0.5f),
      convolution_kernel_size(3),
      convolution_preset("default"),
      median_kernel_size(3),
      median_preset("default"),
      gaussian_sigma(1.0f),
      gaussian_kernel_size(3),
      gaussian_preset("default"),
      bilateral_sigma_color(75.0f),
      bilateral_sigma_space(75.0f),
      bilateral_kernel_size(5),
      bilateral_preset("default"),
      weighted_avg_kernel_size(3),
      weighted_avg_preset("default"),
      filter_strength(1.0f),
      facula_center_channels("320,240"),
      facula_center_peak_threshold(100),
      facula_center_loc_x(160),
      facula_center_loc_y(120),
      facula_handle_type(1) {
    setDefaults();
}

QVariantMap AlgorithmConfigData::toVariantMap() const {
    QVariantMap map;

    // 传统算法参数
    QVariantMap params;
    for (auto it = parameters.begin(); it != parameters.end(); ++it) {
        params[it.key()] = it.value();
    }
    map["parameters"] = params;

    // 图像处理参数
    QVariantMap imageProcessing;
    imageProcessing["interpolation_type"]       = interpolation_type;
    imageProcessing["filter_types"]             = filter_types;
    imageProcessing["interpolation_offset"]     = interpolation_offset;
    imageProcessing["kalman_strength"]          = kalman_strength;
    imageProcessing["convolution_kernel_size"]  = convolution_kernel_size;
    imageProcessing["convolution_preset"]       = convolution_preset;
    imageProcessing["median_kernel_size"]       = median_kernel_size;
    imageProcessing["median_preset"]            = median_preset;
    imageProcessing["gaussian_sigma"]           = gaussian_sigma;
    imageProcessing["gaussian_kernel_size"]     = gaussian_kernel_size;
    imageProcessing["gaussian_preset"]          = gaussian_preset;
    imageProcessing["bilateral_sigma_color"]    = bilateral_sigma_color;
    imageProcessing["bilateral_sigma_space"]    = bilateral_sigma_space;
    imageProcessing["bilateral_kernel_size"]    = bilateral_kernel_size;
    imageProcessing["bilateral_preset"]         = bilateral_preset;
    imageProcessing["weighted_avg_kernel_size"] = weighted_avg_kernel_size;
    imageProcessing["weighted_avg_preset"]      = weighted_avg_preset;
    imageProcessing["filter_strength"]          = filter_strength;
    map["image_processing"]                     = imageProcessing;

    // 光斑检测参数
    QVariantMap faculaDetection;
    faculaDetection["facula_center_channels"]       = facula_center_channels;
    faculaDetection["facula_center_peak_threshold"] = facula_center_peak_threshold;
    faculaDetection["facula_center_loc_x"]          = facula_center_loc_x;
    faculaDetection["facula_center_loc_y"]          = facula_center_loc_y;
    faculaDetection["facula_handle_type"]           = facula_handle_type;
    map["facula_detection"]                         = faculaDetection;

    // 添加元信息
    map["_type"]    = getTypeName();
    map["_version"] = getVersion();

    return map;
}

bool AlgorithmConfigData::fromVariantMap(const QVariantMap &data) {
    try {
        // 读取传统算法参数
        if (data.contains("parameters")) {
            QVariantMap params = data["parameters"].toMap();
            parameters.clear();
            for (auto it = params.begin(); it != params.end(); ++it) {
                parameters[it.key()] = it.value().toInt();
            }
        }

        // 读取图像处理参数
        if (data.contains("image_processing")) {
            QVariantMap imageProcessing = data["image_processing"].toMap();
            interpolation_type          = imageProcessing.value("interpolation_type", 1).toUInt();
            filter_types                = imageProcessing.value("filter_types", "gaussian,median").toString();
            interpolation_offset        = imageProcessing.value("interpolation_offset", 0.0f).toFloat();
            kalman_strength             = imageProcessing.value("kalman_strength", 0.5f).toFloat();
            convolution_kernel_size     = imageProcessing.value("convolution_kernel_size", 3).toUInt();
            convolution_preset          = imageProcessing.value("convolution_preset", "default").toString();
            median_kernel_size          = imageProcessing.value("median_kernel_size", 3).toUInt();
            median_preset               = imageProcessing.value("median_preset", "default").toString();
            gaussian_sigma              = imageProcessing.value("gaussian_sigma", 1.0f).toFloat();
            gaussian_kernel_size        = imageProcessing.value("gaussian_kernel_size", 3).toUInt();
            gaussian_preset             = imageProcessing.value("gaussian_preset", "default").toString();
            bilateral_sigma_color       = imageProcessing.value("bilateral_sigma_color", 75.0f).toFloat();
            bilateral_sigma_space       = imageProcessing.value("bilateral_sigma_space", 75.0f).toFloat();
            bilateral_kernel_size       = imageProcessing.value("bilateral_kernel_size", 5).toUInt();
            bilateral_preset            = imageProcessing.value("bilateral_preset", "default").toString();
            weighted_avg_kernel_size    = imageProcessing.value("weighted_avg_kernel_size", 3).toUInt();
            weighted_avg_preset         = imageProcessing.value("weighted_avg_preset", "default").toString();
            filter_strength             = imageProcessing.value("filter_strength", 1.0f).toFloat();
        }

        // 读取光斑检测参数
        if (data.contains("facula_detection")) {
            QVariantMap faculaDetection  = data["facula_detection"].toMap();
            facula_center_channels       = faculaDetection.value("facula_center_channels", "320,240").toString();
            facula_center_peak_threshold = faculaDetection.value("facula_center_peak_threshold", 100).toUInt();
            facula_center_loc_x          = faculaDetection.value("facula_center_loc_x", 160).toUInt();
            facula_center_loc_y          = faculaDetection.value("facula_center_loc_y", 120).toUInt();
            facula_handle_type           = faculaDetection.value("facula_handle_type", 1).toUInt();
        }

        logInfo("Algorithm config loaded successfully");
        return true;

    } catch (const std::exception &e) {
        logError(QString("Failed to load algorithm config: %1").arg(e.what()));
        return false;
    }
}

bool AlgorithmConfigData::validate() const {
    // 验证插值类型
    if (interpolation_type > 3) {
        logError(QString("Invalid interpolation type: %1").arg(interpolation_type));
        return false;
    }

    // 验证光斑处理类型
    if (facula_handle_type > 2) {
        logError(QString("Invalid facula handle type: %1").arg(facula_handle_type));
        return false;
    }

    // 验证传统算法参数
    for (auto it = parameters.begin(); it != parameters.end(); ++it) {
        if (!validateParameterValue(it.key(), it.value())) {
            logError(QString("Invalid parameter value: %1 = %2").arg(it.key()).arg(it.value()));
            return false;
        }
    }

    return true;
}

void AlgorithmConfigData::setDefaults() {
    // 设置默认传统算法参数
    parameters = s_defaultParameters;

    // 设置默认图像处理参数
    interpolation_type       = 1;
    filter_types             = "gaussian,median";
    interpolation_offset     = 0.0f;
    kalman_strength          = 0.5f;
    convolution_kernel_size  = 3;
    convolution_preset       = "default";
    median_kernel_size       = 3;
    median_preset            = "default";
    gaussian_sigma           = 1.0f;
    gaussian_kernel_size     = 3;
    gaussian_preset          = "default";
    bilateral_sigma_color    = 75.0f;
    bilateral_sigma_space    = 75.0f;
    bilateral_kernel_size    = 5;
    bilateral_preset         = "default";
    weighted_avg_kernel_size = 3;
    weighted_avg_preset      = "default";
    filter_strength          = 1.0f;

    // 设置默认光斑检测参数
    facula_center_channels       = "320,240";
    facula_center_peak_threshold = 100;
    facula_center_loc_x          = 160;
    facula_center_loc_y          = 120;
    facula_handle_type           = 1;

    logInfo("Set algorithm config to default values");
}

QStringList AlgorithmConfigData::getFieldNames() const {
    return s_fieldTypes.keys();
}

QString AlgorithmConfigData::getFieldType(const QString &fieldName) const {
    return s_fieldTypes.value(fieldName, "unknown");
}

QString AlgorithmConfigData::getFieldDescription(const QString &fieldName) const {
    return s_fieldDescriptions.value(fieldName, "No description available");
}

bool AlgorithmConfigData::hasField(const QString &fieldName) const {
    return s_fieldTypes.contains(fieldName) || parameters.contains(fieldName);
}

QVariant AlgorithmConfigData::getFieldValue(const QString &fieldName, const QVariant &defaultValue) const {
    if (parameters.contains(fieldName)) {
        return parameters[fieldName];
    }

    if (fieldName == "interpolation_type")
        return interpolation_type;
    if (fieldName == "filter_types")
        return filter_types;
    if (fieldName == "interpolation_offset")
        return interpolation_offset;
    if (fieldName == "kalman_strength")
        return kalman_strength;
    if (fieldName == "convolution_kernel_size")
        return convolution_kernel_size;
    if (fieldName == "convolution_preset")
        return convolution_preset;
    if (fieldName == "median_kernel_size")
        return median_kernel_size;
    if (fieldName == "median_preset")
        return median_preset;
    if (fieldName == "gaussian_sigma")
        return gaussian_sigma;
    if (fieldName == "gaussian_kernel_size")
        return gaussian_kernel_size;
    if (fieldName == "gaussian_preset")
        return gaussian_preset;
    if (fieldName == "bilateral_sigma_color")
        return bilateral_sigma_color;
    if (fieldName == "bilateral_sigma_space")
        return bilateral_sigma_space;
    if (fieldName == "bilateral_kernel_size")
        return bilateral_kernel_size;
    if (fieldName == "bilateral_preset")
        return bilateral_preset;
    if (fieldName == "weighted_avg_kernel_size")
        return weighted_avg_kernel_size;
    if (fieldName == "weighted_avg_preset")
        return weighted_avg_preset;
    if (fieldName == "filter_strength")
        return filter_strength;
    if (fieldName == "facula_center_channels")
        return facula_center_channels;
    if (fieldName == "facula_center_peak_threshold")
        return facula_center_peak_threshold;
    if (fieldName == "facula_center_loc_x")
        return facula_center_loc_x;
    if (fieldName == "facula_center_loc_y")
        return facula_center_loc_y;
    if (fieldName == "facula_handle_type")
        return facula_handle_type;

    return defaultValue;
}

bool AlgorithmConfigData::setFieldValue(const QString &fieldName, const QVariant &value) {
    if (s_defaultParameters.contains(fieldName)) {
        parameters[fieldName] = value.toInt();
        return true;
    }

    if (fieldName == "interpolation_type") {
        interpolation_type = value.toUInt();
        return true;
    }
    if (fieldName == "filter_types") {
        filter_types = value.toString();
        return true;
    }
    if (fieldName == "interpolation_offset") {
        interpolation_offset = value.toFloat();
        return true;
    }
    if (fieldName == "kalman_strength") {
        kalman_strength = value.toFloat();
        return true;
    }
    if (fieldName == "convolution_kernel_size") {
        convolution_kernel_size = value.toUInt();
        return true;
    }
    if (fieldName == "convolution_preset") {
        convolution_preset = value.toString();
        return true;
    }
    if (fieldName == "median_kernel_size") {
        median_kernel_size = value.toUInt();
        return true;
    }
    if (fieldName == "median_preset") {
        median_preset = value.toString();
        return true;
    }
    if (fieldName == "gaussian_sigma") {
        gaussian_sigma = value.toFloat();
        return true;
    }
    if (fieldName == "gaussian_kernel_size") {
        gaussian_kernel_size = value.toUInt();
        return true;
    }
    if (fieldName == "gaussian_preset") {
        gaussian_preset = value.toString();
        return true;
    }
    if (fieldName == "bilateral_sigma_color") {
        bilateral_sigma_color = value.toFloat();
        return true;
    }
    if (fieldName == "bilateral_sigma_space") {
        bilateral_sigma_space = value.toFloat();
        return true;
    }
    if (fieldName == "bilateral_kernel_size") {
        bilateral_kernel_size = value.toUInt();
        return true;
    }
    if (fieldName == "bilateral_preset") {
        bilateral_preset = value.toString();
        return true;
    }
    if (fieldName == "weighted_avg_kernel_size") {
        weighted_avg_kernel_size = value.toUInt();
        return true;
    }
    if (fieldName == "weighted_avg_preset") {
        weighted_avg_preset = value.toString();
        return true;
    }
    if (fieldName == "filter_strength") {
        filter_strength = value.toFloat();
        return true;
    }
    if (fieldName == "facula_center_channels") {
        facula_center_channels = value.toString();
        return true;
    }
    if (fieldName == "facula_center_peak_threshold") {
        facula_center_peak_threshold = value.toUInt();
        return true;
    }
    if (fieldName == "facula_center_loc_x") {
        facula_center_loc_x = value.toUInt();
        return true;
    }
    if (fieldName == "facula_center_loc_y") {
        facula_center_loc_y = value.toUInt();
        return true;
    }
    if (fieldName == "facula_handle_type") {
        facula_handle_type = value.toUInt();
        return true;
    }

    return false;
}

bool AlgorithmConfigData::resetField(const QString &fieldName) {
    if (s_defaultParameters.contains(fieldName)) {
        parameters[fieldName] = s_defaultParameters[fieldName];
        return true;
    }

    // 重置为默认值
    if (fieldName == "interpolation_type") {
        interpolation_type = 1;
        return true;
    }
    if (fieldName == "filter_types") {
        filter_types = "gaussian,median";
        return true;
    }
    if (fieldName == "interpolation_offset") {
        interpolation_offset = 0.0f;
        return true;
    }
    if (fieldName == "kalman_strength") {
        kalman_strength = 0.5f;
        return true;
    }
    if (fieldName == "convolution_kernel_size") {
        convolution_kernel_size = 3;
        return true;
    }
    if (fieldName == "convolution_preset") {
        convolution_preset = "default";
        return true;
    }
    if (fieldName == "median_kernel_size") {
        median_kernel_size = 3;
        return true;
    }
    if (fieldName == "median_preset") {
        median_preset = "default";
        return true;
    }
    if (fieldName == "gaussian_sigma") {
        gaussian_sigma = 1.0f;
        return true;
    }
    if (fieldName == "gaussian_kernel_size") {
        gaussian_kernel_size = 3;
        return true;
    }
    if (fieldName == "gaussian_preset") {
        gaussian_preset = "default";
        return true;
    }
    if (fieldName == "bilateral_sigma_color") {
        bilateral_sigma_color = 75.0f;
        return true;
    }
    if (fieldName == "bilateral_sigma_space") {
        bilateral_sigma_space = 75.0f;
        return true;
    }
    if (fieldName == "bilateral_kernel_size") {
        bilateral_kernel_size = 5;
        return true;
    }
    if (fieldName == "bilateral_preset") {
        bilateral_preset = "default";
        return true;
    }
    if (fieldName == "weighted_avg_kernel_size") {
        weighted_avg_kernel_size = 3;
        return true;
    }
    if (fieldName == "weighted_avg_preset") {
        weighted_avg_preset = "default";
        return true;
    }
    if (fieldName == "filter_strength") {
        filter_strength = 1.0f;
        return true;
    }
    if (fieldName == "facula_center_channels") {
        facula_center_channels = "320,240";
        return true;
    }
    if (fieldName == "facula_center_peak_threshold") {
        facula_center_peak_threshold = 100;
        return true;
    }
    if (fieldName == "facula_center_loc_x") {
        facula_center_loc_x = 160;
        return true;
    }
    if (fieldName == "facula_center_loc_y") {
        facula_center_loc_y = 120;
        return true;
    }
    if (fieldName == "facula_handle_type") {
        facula_handle_type = 1;
        return true;
    }

    return false;
}

int AlgorithmConfigData::getParameter(const QString &key, int defaultValue) const {
    return parameters.value(key, defaultValue);
}

void AlgorithmConfigData::setParameter(const QString &key, int value) {
    parameters[key] = value;
}

bool AlgorithmConfigData::hasParameter(const QString &key) const {
    return parameters.contains(key);
}

bool AlgorithmConfigData::removeParameter(const QString &key) {
    return parameters.remove(key) > 0;
}

QStringList AlgorithmConfigData::getParameterNames() const {
    return parameters.keys();
}

void AlgorithmConfigData::importParameters(const AlgorithmConfigData &other, bool overwrite) {
    for (auto it = other.parameters.begin(); it != other.parameters.end(); ++it) {
        if (overwrite || !parameters.contains(it.key())) {
            parameters[it.key()] = it.value();
        }
    }
}

void AlgorithmConfigData::loadDefaultParameters() {
    parameters = s_defaultParameters;
}

bool AlgorithmConfigData::validateParameterValue(const QString &key, int value) const {
    if (s_parameterRanges.contains(key)) {
        QPair<int, int> range = s_parameterRanges[key];
        return value >= range.first && value <= range.second;
    }
    return true;  // 如果没有范围限制，则认为有效
}

QPair<int, int> AlgorithmConfigData::getParameterRange(const QString &key) const {
    return s_parameterRanges.value(key, {INT_MIN, INT_MAX});
}

int AlgorithmConfigData::getParameterDefaultValue(const QString &key) const {
    return s_defaultParameters.value(key, 0);
}

}  // namespace Algorithm

// 自动注册算法配置类型
REGISTER_CONFIG_TYPE(Algorithm::AlgorithmConfigData, "Algorithm", "2.1.0", "算法模块配置参数，包含传统算法、图像处理和光斑检测参数")
