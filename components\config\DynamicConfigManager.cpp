#include "DynamicConfigManager.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>

namespace Config {

DynamicConfigManager::DynamicConfigManager(QObject *parent) : QObject(parent), m_baseConfigDir(QApplication::applicationDirPath() + "/config/modules/") {
    // 确保基础配置目录存在
    QDir dir;
    if (!dir.exists(m_baseConfigDir)) {
        dir.mkpath(m_baseConfigDir);
        logInfo(QString("Created base config directory: %1").arg(m_baseConfigDir));
    }
}

DynamicConfigManager::~DynamicConfigManager() {
    clear();
}

bool DynamicConfigManager::registerConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    if (m_configs.find(typeName) != m_configs.end()) {
        logWarning(QString("Config '%1' already registered").arg(typeName));
        return false;
    }

    // 使用注册器创建配置对象
    auto config = ConfigTypeRegistry::getInstance().createConfig(typeName);
    if (!config) {
        logError(QString("Failed to create config '%1'").arg(typeName));
        return false;
    }

    // 设置默认值
    config->setDefaults();

    // 设置默认配置文件路径
    QString filePath            = m_baseConfigDir + typeName.toLower() + "/" + typeName.toLower() + "_config.json";
    m_configFilePaths[typeName] = filePath;

    m_configs[typeName] = std::move(config);

    logInfo(QString("Registered config: %1").arg(typeName));
    Q_EMIT configRegistered(typeName);

    return true;
}

bool DynamicConfigManager::registerConfig(std::unique_ptr<IConfigData> config) {
    if (!config) {
        logError("Cannot register null config");
        return false;
    }

    QString typeName = config->getTypeName();

    QMutexLocker locker(&m_mutex);

    if (m_configs.find(typeName) != m_configs.end()) {
        logWarning(QString("Config '%1' already registered").arg(typeName));
        return false;
    }

    // 设置默认配置文件路径
    QString filePath            = m_baseConfigDir + typeName.toLower() + "/" + typeName.toLower() + "_config.json";
    m_configFilePaths[typeName] = filePath;

    m_configs[typeName] = std::move(config);

    logInfo(QString("Registered config: %1").arg(typeName));
    Q_EMIT configRegistered(typeName);

    return true;
}

bool DynamicConfigManager::unregisterConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        logWarning(QString("Config '%1' not found for unregistration").arg(typeName));
        return false;
    }

    m_configs.erase(it);
    m_configFilePaths.erase(typeName);

    logInfo(QString("Unregistered config: %1").arg(typeName));
    Q_EMIT configUnregistered(typeName);

    return true;
}

IConfigData *DynamicConfigManager::getConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        logWarning(QString("Config '%1' not found").arg(typeName));
        return nullptr;
    }

    return it->second.get();
}

bool DynamicConfigManager::hasConfig(const QString &typeName) const {
    QMutexLocker locker(&m_mutex);
    return m_configs.find(typeName) != m_configs.end();
}

QStringList DynamicConfigManager::getAllConfigs() const {
    QMutexLocker locker(&m_mutex);

    QStringList configs;
    for (const auto &pair : m_configs) {
        configs.append(pair.first);
    }

    return configs;
}

ConfigResult DynamicConfigManager::loadConfig(const QString &typeName, const QString &filePath) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        QString msg = QString("Config '%1' not registered").arg(typeName);
        logError(msg);
        return ConfigResult(false, ErrorType::ModuleNotFound, msg);
    }

    QString configFile = filePath.isEmpty() ? getConfigFilePath(typeName) : filePath;

    ConfigResult result = loadConfigFromJson(it->second.get(), configFile);

    Q_EMIT configLoaded(typeName, result.success);

    if (result.success) {
        logInfo(QString("Loaded config: %1 from %2").arg(typeName, configFile));
        Q_EMIT configChanged(typeName);
    } else {
        logError(QString("Failed to load config: %1 - %2").arg(typeName, result.message));
    }

    return result;
}

ConfigResult DynamicConfigManager::saveConfig(const QString &typeName, const QString &filePath) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        QString msg = QString("Config '%1' not registered").arg(typeName);
        logError(msg);
        return ConfigResult(false, ErrorType::ModuleNotFound, msg);
    }

    QString configFile = filePath.isEmpty() ? getConfigFilePath(typeName) : filePath;

    ConfigResult result = saveConfigToJson(it->second.get(), configFile);

    Q_EMIT configSaved(typeName, result.success);

    if (result.success) {
        logInfo(QString("Saved config: %1 to %2").arg(typeName, configFile));
    } else {
        logError(QString("Failed to save config: %1 - %2").arg(typeName, result.message));
    }

    return result;
}

ConfigResult DynamicConfigManager::loadAllConfigs() {
    QStringList configs      = getAllConfigs();
    int         successCount = 0;
    QString     errorMessages;

    for (const QString &typeName : configs) {
        ConfigResult result = loadConfig(typeName);
        if (result.success) {
            successCount++;
        } else {
            errorMessages += QString("%1: %2; ").arg(typeName, result.message);
        }
    }

    bool    allSuccess = (successCount == configs.size());
    QString message    = QString("Loaded %1/%2 configs").arg(successCount).arg(configs.size());

    if (!allSuccess) {
        message += QString(". Errors: %1").arg(errorMessages);
    }

    logInfo(message);
    return ConfigResult(allSuccess, allSuccess ? ErrorType::None : ErrorType::UnknownError, message);
}

ConfigResult DynamicConfigManager::saveAllConfigs() {
    QStringList configs      = getAllConfigs();
    int         successCount = 0;
    QString     errorMessages;

    for (const QString &typeName : configs) {
        ConfigResult result = saveConfig(typeName);
        if (result.success) {
            successCount++;
        } else {
            errorMessages += QString("%1: %2; ").arg(typeName, result.message);
        }
    }

    bool    allSuccess = (successCount == configs.size());
    QString message    = QString("Saved %1/%2 configs").arg(successCount).arg(configs.size());

    if (!allSuccess) {
        message += QString(". Errors: %1").arg(errorMessages);
    }

    logInfo(message);
    return ConfigResult(allSuccess, allSuccess ? ErrorType::None : ErrorType::UnknownError, message);
}

ConfigResult DynamicConfigManager::validateConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        QString msg = QString("Config '%1' not registered").arg(typeName);
        logError(msg);
        return ConfigResult(false, ErrorType::ModuleNotFound, msg);
    }

    bool    isValid = it->second->validate();
    QString message = isValid ? QString("Config '%1' is valid").arg(typeName) : QString("Config '%1' validation failed").arg(typeName);

    if (isValid) {
        logInfo(message);
    } else {
        logError(message);
    }

    return ConfigResult(isValid, isValid ? ErrorType::None : ErrorType::ValidationError, message);
}

ConfigResult DynamicConfigManager::validateAllConfigs() {
    QStringList configs    = getAllConfigs();
    int         validCount = 0;
    QString     errorMessages;

    for (const QString &typeName : configs) {
        ConfigResult result = validateConfig(typeName);
        if (result.success) {
            validCount++;
        } else {
            errorMessages += QString("%1: %2; ").arg(typeName, result.message);
        }
    }

    bool    allValid = (validCount == configs.size());
    QString message  = QString("Validated %1/%2 configs").arg(validCount).arg(configs.size());

    if (!allValid) {
        message += QString(". Errors: %1").arg(errorMessages);
    }

    logInfo(message);
    return ConfigResult(allValid, allValid ? ErrorType::None : ErrorType::ValidationError, message);
}

bool DynamicConfigManager::resetConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        logError(QString("Config '%1' not registered").arg(typeName));
        return false;
    }

    it->second->setDefaults();
    logInfo(QString("Reset config to defaults: %1").arg(typeName));
    Q_EMIT configChanged(typeName);

    return true;
}

bool DynamicConfigManager::resetAllConfigs() {
    QStringList configs    = getAllConfigs();
    bool        allSuccess = true;

    for (const QString &typeName : configs) {
        if (!resetConfig(typeName)) {
            allSuccess = false;
        }
    }

    logInfo(QString("Reset %1 configs to defaults").arg(configs.size()));
    return allSuccess;
}

void DynamicConfigManager::logInfo(const QString &message) const {
    qDebug() << "[DynamicConfigManager]" << message;
}

void DynamicConfigManager::logError(const QString &message) const {
    qCritical() << "[DynamicConfigManager] ERROR:" << message;
}

void DynamicConfigManager::logWarning(const QString &message) const {
    qWarning() << "[DynamicConfigManager] WARNING:" << message;
}

std::unique_ptr<IConfigData> DynamicConfigManager::cloneConfig(const QString &typeName) {
    QMutexLocker locker(&m_mutex);

    auto it = m_configs.find(typeName);
    if (it == m_configs.end()) {
        logError(QString("Config '%1' not registered").arg(typeName));
        return nullptr;
    }

    return it->second->clone();
}

bool DynamicConfigManager::compareConfigs(const QString &typeName1, const QString &typeName2) {
    QMutexLocker locker(&m_mutex);

    auto it1 = m_configs.find(typeName1);
    auto it2 = m_configs.find(typeName2);

    if (it1 == m_configs.end() || it2 == m_configs.end()) {
        logError("One or both configs not found for comparison");
        return false;
    }

    return it1->second->equals(*it2->second);
}

QVariantMap DynamicConfigManager::getStatistics() const {
    QMutexLocker locker(&m_mutex);

    QVariantMap stats;
    stats["total_configs"]      = static_cast<int>(m_configs.size());
    stats["registered_configs"] = getAllConfigs();
    stats["base_config_dir"]    = m_baseConfigDir;

    QVariantMap configPaths;
    for (const auto &pair : m_configFilePaths) {
        configPaths[pair.first] = pair.second;
    }
    stats["config_file_paths"] = configPaths;

    return stats;
}

void DynamicConfigManager::clear() {
    QMutexLocker locker(&m_mutex);
    m_configs.clear();
    m_configFilePaths.clear();
    logInfo("Cleared all configs");
}

QString DynamicConfigManager::getConfigFilePath(const QString &typeName) const {
    auto it = m_configFilePaths.find(typeName);
    if (it != m_configFilePaths.end()) {
        return it->second;
    }

    // 返回默认路径
    return m_baseConfigDir + typeName.toLower() + "/" + typeName.toLower() + "_config.json";
}

void DynamicConfigManager::setConfigFilePath(const QString &typeName, const QString &filePath) {
    QMutexLocker locker(&m_mutex);
    m_configFilePaths[typeName] = filePath;
    logInfo(QString("Set config file path for '%1': %2").arg(typeName, filePath));
}

bool DynamicConfigManager::ensureConfigDirectory(const QString &filePath) const {
    QFileInfo fileInfo(filePath);
    QDir      dir = fileInfo.absoluteDir();

    if (!dir.exists()) {
        if (!dir.mkpath(dir.absolutePath())) {
            logError(QString("Failed to create directory: %1").arg(dir.absolutePath()));
            return false;
        }
        logInfo(QString("Created directory: %1").arg(dir.absolutePath()));
    }

    return true;
}

ConfigResult DynamicConfigManager::loadConfigFromJson(IConfigData *config, const QString &filePath) {
    if (!QFile::exists(filePath)) {
        QString msg = QString("Config file not found: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileNotFound, msg);
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        QString msg = QString("Cannot open config file: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileReadError, msg);
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError parseError;
    QJsonDocument   doc = QJsonDocument::fromJson(data, &parseError);

    if (parseError.error != QJsonParseError::NoError) {
        QString msg = QString("JSON parse error: %1").arg(parseError.errorString());
        return ConfigResult(false, ErrorType::ParseError, msg);
    }

    QVariantMap variantMap = doc.object().toVariantMap();

    if (!config->fromVariantMap(variantMap)) {
        QString msg = "Failed to load config from variant map";
        return ConfigResult(false, ErrorType::ParseError, msg);
    }

    return ConfigResult(true);
}

ConfigResult DynamicConfigManager::saveConfigToJson(const IConfigData *config, const QString &filePath) {
    if (!ensureConfigDirectory(filePath)) {
        QString msg = QString("Failed to create config directory for: %1").arg(filePath);
        return ConfigResult(false, ErrorType::PermissionError, msg);
    }

    QVariantMap   variantMap = config->toVariantMap();
    QJsonObject   jsonObj    = QJsonObject::fromVariantMap(variantMap);
    QJsonDocument doc(jsonObj);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        QString msg = QString("Cannot open config file for writing: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileWriteError, msg);
    }

    qint64 bytesWritten = file.write(doc.toJson());
    file.close();

    if (bytesWritten == -1) {
        QString msg = QString("Failed to write config file: %1").arg(filePath);
        return ConfigResult(false, ErrorType::FileWriteError, msg);
    }

    return ConfigResult(true);
}

}  // namespace Config
