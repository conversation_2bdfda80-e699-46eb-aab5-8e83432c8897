# 1. **项目背景与需求**
## 1.1 背景
对于测距芯片，准直后光斑的位置会对测距能力和精度造成影响，需要调节接收镜片使光斑落在光敏面固定位置
## 1.2 需求
- ~~调节电压—> 某一距离下不同芯片peak处于同一范围~~
    
    [VI4300_SPAD工作电压自适应方案介绍202112.pptx](VI4300_SPAD工作电压自适应方案介绍202112.pptx)
    
    - 同一距离，不同芯片，芯片供电电压不同，peak不同
- 调焦
# 2. 分析
## 2.1
# 3. **项目方案与测试**
## ~~3.1 VSPAD 自适应~~
[https://whimsical.com/vapad-WuLpTqpjFFyEdUYTUnNqeM](https://whimsical.com/vapad-WuLpTqpjFFyEdUYTUnNqeM)
## 3.2 光斑调节-机台
### 3.2.1 流程与方案
- 应用流程与程序架构
    
    [https://whimsical.com/JDC4xo5XutpaWp3T4SxP33](https://whimsical.com/JDC4xo5XutpaWp3T4SxP33)
    
### 3.2.2 机台资料
    
- 
### 3.2.3 sensor固件
- 指令交互
    
    ```C++
    //协议 YC
    cache[0] = 0xA5;
    cache[1] = kD2H|kDFF;
    cache[2] = 0xAD;	
    cache[4] = 16;//? data数据的字节数？
    cache[5] = 0x00;//0;
    cache[3] = cache[0]^cache[1]^cache[2]^cache[4]^cache[5];
    for(m=0; m<8; m++) {
    		cache[6+4*m] = histBuff[m];
    		cache[6+4*m+1] = histBuff[m]>>8;	
    		cache[6+4*m+2] = 0;//histBuff[m]>>16;
    		cache[6+4*m+3] = 0;//histBuff[m]>>24;	
    					
    		cache[3] ^= cache[6+4*m]^cache[6+4*m+1]^cache[6+4*m+1]^cache[6+4*m+1]; //xor		
    	}
    ```
    
### 3.2.4 光斑处理
1. 位置
    1. 光斑中心
        1. 最大值在MP中心
        2. 边缘光斑对称
    2. 目标区域
2. 光斑最优
    1. 求PEAK值最大
3. 机台速度优化
    1. 与peak值联系
4. 光斑优化
    - 插值
        - ~~线性插值~~
    - 卷积锐化
    
### 3.2.5 数据存储
- 光斑数据
- 机台数据
    - 初始坐标
    - 结束坐标
    - 各轴位移量
- 运行数据
    - 测试结果
        - 正常
        - 异常
            - 异常步骤
                
            - 异常原因
                - 单次测量error次数
                - 总timeout次数
                    - timeout时间
                - 光斑寻找失败
                - 光斑寻找超时
    - MP数据
        - **最后一阵MP原始数据**
    - 总测试时间
# 4. 镜片与调节效果
## 4.0 镜片与圆筒尺寸
|   |   |   |   |
|---|---|---|---|
|项目|镜片尺寸|圆筒尺寸|间隙|
|D4|6.0mm|7.89mm|0.945mm|
|||||
## 4.1 手动调节
### 4.1.1 4300
- 通道分布（正常分布，实际芯片贴反）
    
    ![Untitled 1 15.jpeg](images/Untitled 1 15.jpeg)
    
    ![Untitled 2 9.jpeg](images/Untitled 2 9.jpeg)
    
    ![Untitled 3 8.jpeg](images/Untitled 3 8.jpeg)
    
- 各轴与光斑情况
    - X
        
    - Y
        
    - Z
        
- 实际光斑情况
    - 左右各两个最强值
        
        ![Untitled 4 25.png](images/Untitled 4 25.png)
        
### 4..1.2 4302
- MP通道
    
    ![Untitled 9.jpeg](images/Untitled 9.jpeg)
    
    ![Untitled 1 14.png](images/Untitled 1 14.png)
    
## 4.2 自动调节
## 4.3 点胶
# 5. 测试与**结果**
| 编号                                                               | 空光斑范围 | 限位    |
| ---------------------------------------------------------------- | ----- | ----- |
| [[NovaLenApp_vault/analysis/modules/调焦&VSPAD调节/Untitled/1\|1]]   |       |       |
| [[NovaLenApp_vault/analysis/modules/调焦&VSPAD调节/Untitled/2/2\|2]] | 500um | 900um |
| [[NovaLenApp_vault/analysis/modules/调焦&VSPAD调节/Untitled/3/3]]    |       |       |
| [[NovaLenApp_vault/analysis/modules/调焦&VSPAD调节/Untitled/4/4]]    |       |       |
| [[NovaLenApp_vault/analysis/modules/调焦&VSPAD调节/Untitled/5]]      |       |       |
| [[NovaLenApp_vault/analysis/modules/调焦&VSPAD调节/Untitled/6]]      |       |       |
  
  
# 6. **改进**
- ~~机台RS232端口 公头→母头~~
# 7. 资料
## 7.1 安装文档
[[KA/Computer/development/APP development/1. 框架/support/mes/MES配置-OBDC数据库配置]]
## 7.2 使用文档
[[KA/Computer/development/APP development/1. 框架/output/usage/lenAdjust/TOF接收镜片光路耦合软件使用文档]]
[[KA/Computer/development/APP development/1. 框架/output/usage/lenAdjust/TOF接收镜片软件日志与错误信息说明]]
[[KA/Computer/development/APP development/1. 框架/output/usage/lenAdjust/TOF接收镜片光路耦合软件]]
[[KA/Computer/development/APP development/1. 框架/output/usage/lenRework/TOF接收镜片rework软件使用文档]]
[[KA/Computer/development/APP development/1. 框架/output/usage/lenRework/TOF接收镜片rework软件]]
[[KA/Computer/development/APP development/1. 框架/output/usage/lenMEMD/TOF接收镜片手动录入MES软件使用文档]]
[[KA/Computer/development/APP development/1. 框架/output/usage/lenMEMD/TOF接收镜片手动录入MES软件]]
[[TOF光路耦合异常记录]]