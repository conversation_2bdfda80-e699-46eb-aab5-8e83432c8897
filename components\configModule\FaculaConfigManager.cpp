#include "FaculaConfigManager.h"
#include <QApplication>
#include <QSettings>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDebug>

namespace Config {

FaculaConfigManager::FaculaConfigManager(QObject *parent)
    : QObject(parent)
    , m_fileWatcher(new QFileSystemWatcher(this))
{
    // 设置配置文件路径
    m_configFilePath = QApplication::applicationDirPath() + "/config/modules/lenAdjust/facula_config.json";
    
    // 连接文件监控信号
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged, this, &FaculaConfigManager::onFileChanged);
    
    // 生成默认配置
    generateDefaultConfig();
    
    // 自动加载配置
    loadConfig();
    
    logInfo("FaculaConfigManager initialized");
}

FaculaConfigManager::~FaculaConfigManager() {
    // 保存配置
    saveConfig();
}

ConfigResult FaculaConfigManager::loadConfig() {
    logInfo("Loading facula configuration from: " + m_configFilePath);
    
    if (!QFile::exists(m_configFilePath)) {
        logInfo("Config file not found, generating default configuration");
        ConfigResult result = saveConfig(); // 生成默认配置文件
        if (!result.success) {
            return result;
        }
    }
    
    QSettings settings(m_configFilePath, QSettings::IniFormat);
    
    // 读取光斑中心配置
    settings.beginGroup("FACULA_CENTER");
    m_config.facula_center_channels = settings.value("facula_center_channels", "2,2").toString();
    m_config.facula_center_peak_threshold = settings.value("facula_center_peak_threshold", 800).toUInt();
    m_config.facula_center_loc_x = settings.value("facula_center_loc_x", 2).toUInt();
    m_config.facula_center_loc_y = settings.value("facula_center_loc_y", 2).toUInt();
    settings.endGroup();
    
    // 读取光斑处理类型
    settings.beginGroup("FACULA_HANDLE");
    m_config.facula_handle_type = settings.value("facula_handle_type", 1).toUInt();
    settings.endGroup();
    
    // 解析多通道配置
    m_config.facula_center_points = parseChannels(m_config.facula_center_channels);
    
    // 验证配置
    ConfigResult validationResult = validateConfig();
    if (!validationResult.success) {
        logError("Configuration validation failed: " + validationResult.message);
        return validationResult;
    }
    
    // 添加文件监控
    if (!m_fileWatcher->files().contains(m_configFilePath)) {
        m_fileWatcher->addPath(m_configFilePath);
    }
    
    logInfo("Facula configuration loaded successfully");
    logInfo(QString("  Channels: %1, Peak threshold: %2, Handle type: %3")
                .arg(m_config.facula_center_channels)
                .arg(m_config.facula_center_peak_threshold)
                .arg(m_config.facula_handle_type));
    
    Q_EMIT configChanged();
    return ConfigResult(true);
}

ConfigResult FaculaConfigManager::saveConfig() {
    logInfo("Saving facula configuration to: " + m_configFilePath);
    
    // 确保目录存在
    QFileInfo fileInfo(m_configFilePath);
    if (!ensureDirectoryExists(fileInfo.absolutePath())) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to create config directory");
    }
    
    QSettings settings(m_configFilePath, QSettings::IniFormat);
    
    // 写入光斑中心配置
    settings.beginGroup("FACULA_CENTER");
    settings.setValue("facula_center_channels", m_config.facula_center_channels);
    settings.setValue("facula_center_peak_threshold", m_config.facula_center_peak_threshold);
    settings.setValue("facula_center_loc_x", m_config.facula_center_loc_x);
    settings.setValue("facula_center_loc_y", m_config.facula_center_loc_y);
    settings.endGroup();
    
    // 写入光斑处理类型
    settings.beginGroup("FACULA_HANDLE");
    settings.setValue("facula_handle_type", m_config.facula_handle_type);
    settings.endGroup();
    
    settings.sync();
    
    logInfo("Facula configuration saved successfully");
    return ConfigResult(true);
}

bool FaculaConfigManager::isConfigValid() const {
    return validateConfig().success;
}

QString FaculaConfigManager::getConfigFilePath() const {
    return m_configFilePath;
}

void FaculaConfigManager::setConfig(const FaculaConfig &config) {
    m_config = config;
    Q_EMIT configChanged();
}

void FaculaConfigManager::setHandleType(uint8_t handleType) {
    if (handleType <= 2) { // 0-基础，1-增强，2-高精度
        m_config.facula_handle_type = handleType;
        Q_EMIT configChanged();
    }
}

void FaculaConfigManager::updateChannels(const QString &channels) {
    m_config.facula_center_channels = channels;
    m_config.facula_center_points = parseChannels(channels);
    
    // 更新兼容性坐标
    if (!m_config.facula_center_points.isEmpty()) {
        m_config.facula_center_loc_x = static_cast<uint8_t>(m_config.facula_center_points.first().x());
        m_config.facula_center_loc_y = static_cast<uint8_t>(m_config.facula_center_points.first().y());
    }
    
    Q_EMIT configChanged();
}

void FaculaConfigManager::onFileChanged(const QString &path) {
    if (path == m_configFilePath) {
        logInfo("Configuration file changed, reloading...");
        loadConfig();
    }
}

QVector<QPoint> FaculaConfigManager::parseChannels(const QString &channels) const {
    QVector<QPoint> points;
    QStringList channelList = channels.split(';', Qt::SkipEmptyParts);
    
    for (const QString &channel : channelList) {
        QStringList coords = channel.split(',', Qt::SkipEmptyParts);
        if (coords.size() == 2) {
            bool okX, okY;
            int x = coords[0].trimmed().toInt(&okX);
            int y = coords[1].trimmed().toInt(&okY);
            if (okX && okY) {
                points.append(QPoint(x, y));
            }
        }
    }
    
    return points;
}

ConfigResult FaculaConfigManager::validateConfig() const {
    // 验证峰值阈值范围
    if (m_config.facula_center_peak_threshold < 100 || m_config.facula_center_peak_threshold > 2000) {
        return ConfigResult(false, ErrorType::ValidationError, 
                          QString("Peak threshold out of range: %1 (valid: 100-2000)")
                          .arg(m_config.facula_center_peak_threshold));
    }
    
    // 验证处理类型
    if (m_config.facula_handle_type > 2) {
        return ConfigResult(false, ErrorType::ValidationError,
                          QString("Handle type out of range: %1 (valid: 0-2)")
                          .arg(m_config.facula_handle_type));
    }
    
    // 验证通道配置
    if (m_config.facula_center_channels.isEmpty()) {
        return ConfigResult(false, ErrorType::ValidationError, "Facula center channels cannot be empty");
    }
    
    return ConfigResult(true);
}

void FaculaConfigManager::generateDefaultConfig() {
    // 使用默认构造函数的值
    m_config = FaculaConfig();
    logInfo("Generated default facula configuration");
}

} // namespace Config
