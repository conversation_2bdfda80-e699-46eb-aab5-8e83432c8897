#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include "components/lensAdjust/config/AdjustProcessConfigData.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 测试 AdjustProcessConfigData 配置系统 ===";
    
    // 1. 创建配置数据实例
    LensAdjust::AdjustProcessConfigData config;
    qDebug() << "1. 创建配置数据实例完成";
    
    // 2. 测试默认值
    qDebug() << "2. 测试默认值:";
    qDebug() << "   光斑判定次数:" << config.getFaculaOkTimes();
    qDebug() << "   固化时间:" << config.getSolidTime() << "ms";
    qDebug() << "   异常处理方式:" << config.getFaculaNgHandle() << "(" << config.getFaculaNgHandleDescription() << ")";
    
    // 3. 测试设置值
    qDebug() << "3. 测试设置新值:";
    config.setFaculaOkTimes(5);
    config.setSolidTime(1000);
    config.setFaculaNgHandle(2);
    qDebug() << "   光斑判定次数:" << config.getFaculaOkTimes();
    qDebug() << "   固化时间:" << config.getSolidTime() << "ms";
    qDebug() << "   异常处理方式:" << config.getFaculaNgHandle() << "(" << config.getFaculaNgHandleDescription() << ")";
    
    // 4. 测试序列化
    qDebug() << "4. 测试序列化:";
    QVariantMap data = config.toVariantMap();
    QJsonDocument doc = QJsonDocument::fromVariant(data);
    QString jsonString = doc.toJson(QJsonDocument::Compact);
    qDebug() << "   JSON:" << jsonString;
    
    // 5. 测试反序列化
    qDebug() << "5. 测试反序列化:";
    LensAdjust::AdjustProcessConfigData config2;
    config2.fromVariantMap(data);
    qDebug() << "   反序列化后光斑判定次数:" << config2.getFaculaOkTimes();
    qDebug() << "   反序列化后固化时间:" << config2.getSolidTime() << "ms";
    qDebug() << "   反序列化后异常处理方式:" << config2.getFaculaNgHandle();
    
    // 6. 测试验证
    qDebug() << "6. 测试验证:";
    qDebug() << "   配置是否有效:" << (config.validate() ? "是" : "否");
    
    // 7. 测试配置摘要
    qDebug() << "7. 配置摘要:" << config.getConfigSummary();
    
    // 8. 测试字段操作
    qDebug() << "8. 测试字段操作:";
    QStringList fields = config.getFieldNames();
    qDebug() << "   字段列表:" << fields;
    for (const QString& field : fields) {
        qDebug() << "   字段" << field << ":" 
                 << "类型=" << config.getFieldType(field)
                 << ", 描述=" << config.getFieldDescription(field)
                 << ", 值=" << config.getFieldValue(field);
    }
    
    qDebug() << "=== 测试完成 ===";
    
    return 0;
}
