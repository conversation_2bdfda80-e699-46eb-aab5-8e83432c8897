#ifndef IDATAHANDLER_H
#define IDATAHANDLER_H

#include <QString>
#include <QStringList>
#include <QVariant>
#include <memory>

namespace SaveLoad {

// 前向声明
class DataContainer;
class DataResult;
class ValidationResult;

/**
 * @brief 数据错误类型枚举
 */
enum class DataErrorType {
    None,                   // 无错误
    FileNotFound,          // 文件未找到
    ParseError,            // 解析错误
    ValidationError,       // 验证错误
    PermissionError,       // 权限错误
    FormatNotSupported,    // 格式不支持
    SchemaError,           // Schema错误
    TypeConversionError,   // 类型转换错误
    UnknownError          // 未知错误
};

/**
 * @brief 数据操作结果类
 */
class DataResult {
public:
    bool success;
    DataErrorType errorType;
    QString message;
    QString details;
    
    DataResult(bool success = true) 
        : success(success), errorType(DataErrorType::None) {}
    
    DataResult(DataErrorType error, const QString& msg, const QString& detail = QString()) 
        : success(false), errorType(error), message(msg), details(detail) {}
    
    static DataResult Success() { return DataResult(true); }
    static DataResult Error(DataErrorType type, const QString& message, const QString& details = QString());
    
    // 便利方法
    bool isSuccess() const { return success; }
    bool isError() const { return !success; }
    QString getErrorString() const;
};

/**
 * @brief 验证结果类
 */
class ValidationResult {
public:
    bool valid;
    QStringList errors;
    QStringList warnings;
    
    ValidationResult(bool valid = true) : valid(valid) {}
    
    void addError(const QString& error) { 
        errors.append(error); 
        valid = false; 
    }
    
    void addWarning(const QString& warning) { 
        warnings.append(warning); 
    }
    
    bool isValid() const { return valid; }
    bool hasErrors() const { return !errors.isEmpty(); }
    bool hasWarnings() const { return !warnings.isEmpty(); }
};

/**
 * @brief 统一数据处理接口
 * 
 * 提供统一的数据加载、保存、验证接口
 * 支持多种数据格式的统一访问
 */
class IDataHandler {
public:
    virtual ~IDataHandler() = default;
    
    // 基本文件操作
    virtual DataResult load(const QString& filePath) = 0;
    virtual DataResult save(const QString& filePath, const DataContainer& data) = 0;
    
    // 字符串操作（可选实现）
    virtual DataResult loadFromString(const QString& content) { 
        Q_UNUSED(content);
        return DataResult(DataErrorType::FormatNotSupported, "String loading not supported"); 
    }
    
    virtual QString saveToString(const DataContainer& data) const { 
        Q_UNUSED(data);
        return QString(); 
    }
    
    // 格式支持查询
    virtual QStringList supportedFormats() const = 0;
    virtual bool canHandle(const QString& filePath) const = 0;
    virtual QString getFormatName() const = 0;
    
    // 数据访问（基于当前加载的数据）
    virtual QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const = 0;
    virtual DataResult setValue(const QString& key, const QVariant& value) = 0;
    virtual bool hasKey(const QString& key) const = 0;
    virtual QStringList keys() const = 0;
    
    // 数据容器访问
    virtual DataContainer getDataContainer() const = 0;
    virtual DataResult setDataContainer(const DataContainer& data) = 0;
    
    // 验证功能
    virtual ValidationResult validate() const = 0;
    virtual ValidationResult validateWithSchema(const QString& schemaPath) const {
        Q_UNUSED(schemaPath);
        return ValidationResult(true); // 默认实现：总是通过
    }
    
    // 配置选项
    virtual void setOption(const QString& key, const QVariant& value) {
        Q_UNUSED(key);
        Q_UNUSED(value);
    }
    
    virtual QVariant getOption(const QString& key, const QVariant& defaultValue = QVariant()) const {
        Q_UNUSED(key);
        return defaultValue;
    }
    
    // 错误处理
    virtual QString getLastError() const = 0;
    virtual void clearError() = 0;
    
protected:
    // 工具方法
    virtual DataResult setLastError(DataErrorType type, const QString& message, const QString& details = QString()) = 0;
};

/**
 * @brief 数据处理器工厂接口
 */
class IDataHandlerFactory {
public:
    virtual ~IDataHandlerFactory() = default;
    
    virtual std::unique_ptr<IDataHandler> createHandler() const = 0;
    virtual QStringList supportedFormats() const = 0;
    virtual QString getFormatName() const = 0;
    virtual bool canHandle(const QString& filePath) const = 0;
};

} // namespace SaveLoad

#endif // IDATAHANDLER_H
