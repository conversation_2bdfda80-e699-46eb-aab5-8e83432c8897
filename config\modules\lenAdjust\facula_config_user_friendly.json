{"//": "以下信息由系统自动管理，通常不需要修改", "_version": "1.0", "_description": "[系统信息] 光斑检测和处理相关参数配置", "_last_updated": "2024-01-01T00:00:00Z", "facula_center_channels": "2,2;1,2;3,2", "facula_center_peak_threshold": 800, "facula_handle_type": 1, "facula_center_loc_x": 2, "facula_center_loc_y": 2, "_field_descriptions": {"facula_center_channels": "[系统信息] 多通道配置字符串，格式：x1,y1;x2,y2;x3,y3。每个坐标对应一个检测通道的位置", "facula_center_peak_threshold": "[系统信息] 多通道模式下的峰值阈值，用于判断光斑是否有效", "facula_handle_type": "[系统信息] 光斑处理算法选择：0-基础，1-增强，2-高精度", "facula_center_loc_x": "[系统信息] 兼容性参数，主光斑X坐标", "facula_center_loc_y": "[系统信息] 兼容性参数，主光斑Y坐标"}, "_compatibility": {"_comment": "[系统信息] 兼容性配置", "auto_extract_from_channels": true, "legacy_xml_support": true, "migration_date": "auto-generated"}, "_tuning_guidelines": {"_comment": "[系统信息] 参数调优指南", "facula_center_channels": "根据实际光斑分布调整坐标，建议使用图像查看工具确定准确位置", "peak_threshold": "阈值过低会产生误检，过高会漏检。建议从默认值开始，观察检测效果后微调", "handle_type": "算法类型影响处理速度和精度，根据应用需求选择合适的算法", "coordinate_system": "坐标系以图像左上角为原点(0,0)，X轴向右，Y轴向下"}, "_validation_rules": {"_comment": "[系统信息] 参数验证规则", "facula_center_peak_threshold": {"min": 100, "max": 2000, "default": 800}, "facula_handle_type": {"min": 0, "max": 2, "default": 1}, "coordinate_range": {"x_min": 0, "x_max": 1000, "y_min": 0, "y_max": 1000}}}