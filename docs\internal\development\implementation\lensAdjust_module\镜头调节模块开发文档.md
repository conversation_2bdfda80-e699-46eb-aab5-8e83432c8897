# 镜头调节模块开发文档

## 模块概述

镜头调节模块是激光工具系统的核心控制模块，负责光路调节、镜头定位、流程控制等关键功能。

### 模块职责

1. **光路调节控制** - 控制镜头的精确调节和定位
2. **流程管理** - 管理调节流程的执行和状态控制
3. **异常处理** - 处理调节过程中的异常情况
4. **配置管理** - 管理调节流程相关的配置参数
5. **状态监控** - 监控调节过程的状态和进度

### 设计原则

1. **精确控制** - 确保镜头调节的精确性和稳定性
2. **流程驱动** - 基于配置的流程控制机制
3. **异常安全** - 完善的异常处理和恢复机制
4. **状态可控** - 清晰的状态管理和监控
5. **配置灵活** - 支持灵活的参数配置

## 架构设计

### 模块结构

```
components/lensAdjust/
├── config/
│   ├── AdjustProcessConfigData.h/.cpp  # 调节流程配置数据类
│   └── LensAdjustModule.h/.cpp         # 模块初始化
├── clensadjust.h/.cpp                  # 主调节控制类
├── clenAdjustOperation.h/.cpp          # 调节操作类
├── lensReadIni.h/.cpp                  # 配置读取类（兼容性）
└── clensadjust.ui                      # 用户界面
```

### 配置管理架构

```mermaid
graph TB
    subgraph "LensAdjust模块"
        ACD[AdjustProcessConfigData<br/>调节流程配置]
        LAM[LensAdjustModule<br/>模块管理器]
        CA[CLensAdjust<br/>主控制类]
        CAO[CLenAdjustOperation<br/>操作类]
        LRI[LensReadIni<br/>兼容性配置读取]
    end
    
    subgraph "Config服务"
        CS[ConfigService<br/>配置服务]
        DCM[DynamicConfigManager<br/>动态管理器]
    end
    
    ACD --> CS
    LAM --> ACD
    CA --> ACD
    CAO --> ACD
    LRI -.-> ACD
    
    CS --> DCM
    
    style ACD fill:#e1f5fe
    style LAM fill:#f3e5f5
    style LRI fill:#ffcccb
```

## 配置数据设计

### AdjustProcessConfigData类

```cpp
namespace LensAdjust {
    class AdjustProcessConfigData : public Config::BaseConfigData<AdjustProcessConfigData> {
    public:
        // 流程控制参数
        uint8_t  facula_ok_times;   // 光斑判定次数 (1-10)
        uint32_t solid_time;        // 固化时间(毫秒) (0-10000)
        uint8_t  facula_ng_handle;  // 异常处理方式：0-停止，1-继续，2-重试
        
        // 接口方法
        static QString staticTypeName() { return "AdjustProcess"; }
        QString getTypeName() const override;
        QVariantMap toVariantMap() const override;
        bool fromVariantMap(const QVariantMap &data) override;
        bool validate() const override;
        void setDefaults() override;
        
        // 业务方法
        uint8_t getFaculaOkTimes() const;
        void setFaculaOkTimes(uint8_t times);
        uint32_t getSolidTime() const;
        void setSolidTime(uint32_t time);
        uint8_t getFaculaNgHandle() const;
        void setFaculaNgHandle(uint8_t handle);
        QString getFaculaNgHandleDescription() const;
    };
}
```

### 配置参数详解

#### 1. 光斑判定次数 (facula_ok_times)
- **范围**: 1-10次
- **默认值**: 3次
- **作用**: 确定光斑位置需要连续成功的次数
- **影响**: 值越大越稳定，但调节时间越长

#### 2. 固化时间 (solid_time)
- **范围**: 0-10000毫秒
- **默认值**: 0毫秒
- **作用**: 调节完成后的稳定等待时间
- **影响**: 确保机械结构稳定

#### 3. 异常处理方式 (facula_ng_handle)
- **0 - 停止**: 遇到异常立即停止调节流程
- **1 - 继续**: 忽略异常继续执行（默认）
- **2 - 重试**: 遇到异常重新尝试调节

## 模块初始化

### 配置注册

```cpp
// components/lensAdjust/config/LensAdjustModule.cpp
namespace LensAdjust {
    void LensAdjustModule::initialize() {
        // 配置会通过REGISTER_CONFIG_TYPE宏自动注册
        qDebug() << "LensAdjust module initialized with config registration";
        
        // 验证配置注册
        auto &registry = Config::ConfigTypeRegistry::getInstance();
        if (registry.isRegistered("AdjustProcess")) {
            qDebug() << "✅ AdjustProcess config registered successfully";
        } else {
            qCritical() << "❌ AdjustProcess config registration failed";
        }
        
        // 初始化兼容性配置读取
        initializeCompatibilityConfig();
    }
    
    void LensAdjustModule::initializeCompatibilityConfig() {
        // 从旧的lensReadIni迁移配置到新系统
        LensReadIni oldConfig;
        auto* newConfig = CONFIG_SERVICE().getConfig<AdjustProcessConfigData>("AdjustProcess");
        
        if (newConfig) {
            // 迁移配置数据
            migrateFromOldConfig(oldConfig, newConfig);
        }
    }
}
```

### 自动注册机制

```cpp
// 在AdjustProcessConfigData.h文件末尾
REGISTER_CONFIG_TYPE(LensAdjust::AdjustProcessConfigData, "AdjustProcess", "1.0.0", 
                    "镜头调节流程配置，控制光路调节的流程参数");
```

## 使用指南

### 获取调节流程配置

```cpp
// 在镜头调节相关代码中
auto* adjustConfig = CONFIG_SERVICE().getConfig<LensAdjust::AdjustProcessConfigData>("AdjustProcess");

if (adjustConfig) {
    // 读取流程参数
    uint8_t okTimes = adjustConfig->getFaculaOkTimes();
    uint32_t solidTime = adjustConfig->getSolidTime();
    uint8_t ngHandle = adjustConfig->getFaculaNgHandle();
    
    qDebug() << "调节流程配置:";
    qDebug() << "  光斑判定次数:" << okTimes;
    qDebug() << "  固化时间:" << solidTime << "ms";
    qDebug() << "  异常处理:" << adjustConfig->getFaculaNgHandleDescription();
}
```

### 修改调节流程配置

```cpp
// 修改流程参数
adjustConfig->setFaculaOkTimes(5);        // 设置判定次数为5次
adjustConfig->setSolidTime(1000);         // 设置固化时间为1秒
adjustConfig->setFaculaNgHandle(2);       // 设置异常处理为重试

// 验证配置
if (adjustConfig->validate()) {
    // 保存配置
    CONFIG_SERVICE().saveConfig("AdjustProcess");
    qDebug() << "调节流程配置已更新并保存";
} else {
    qWarning() << "调节流程配置验证失败";
}
```

### 在调节流程中使用配置

```cpp
// CLensAdjust类中的调节流程
void CLensAdjust::executeAdjustProcess() {
    auto* config = CONFIG_SERVICE().getConfig<LensAdjust::AdjustProcessConfigData>("AdjustProcess");
    if (!config) {
        qCritical() << "无法获取调节流程配置";
        return;
    }
    
    uint8_t okTimes = config->getFaculaOkTimes();
    uint32_t solidTime = config->getSolidTime();
    uint8_t ngHandle = config->getFaculaNgHandle();
    
    int successCount = 0;
    
    // 执行调节循环
    for (int attempt = 0; attempt < 10; ++attempt) {
        if (adjustStep()) {
            successCount++;
            if (successCount >= okTimes) {
                // 达到成功次数要求
                qDebug() << "调节成功，开始固化";
                if (solidTime > 0) {
                    QThread::msleep(solidTime);
                }
                break;
            }
        } else {
            // 调节失败，根据配置处理
            successCount = 0;
            handleAdjustFailure(ngHandle);
        }
    }
}

void CLensAdjust::handleAdjustFailure(uint8_t ngHandle) {
    switch (ngHandle) {
        case 0: // 停止
            qWarning() << "调节失败，停止流程";
            stopAdjustProcess();
            break;
        case 1: // 继续
            qDebug() << "调节失败，继续尝试";
            break;
        case 2: // 重试
            qDebug() << "调节失败，重新开始";
            resetAdjustProcess();
            break;
        default:
            qWarning() << "未知的异常处理方式:" << ngHandle;
            break;
    }
}
```

## 配置文件格式

### JSON配置文件结构

```json
{
    "_type": "AdjustProcess",
    "_version": "1.0.0",
    "facula_ok_times": 3,
    "solid_time": 0,
    "facula_ng_handle": 1
}
```

### 配置文件位置

- **默认路径**: `config/modules/adjust_process/adjust_process_config.json`
- **备份路径**: `config/backup/adjust_process/`
- **兼容性路径**: `config/lens_adjust.ini` (旧格式)

## 兼容性处理

### 与旧配置系统的兼容

```cpp
// 兼容性配置迁移
void LensAdjustModule::migrateFromOldConfig(const LensReadIni &oldConfig, 
                                           AdjustProcessConfigData *newConfig) {
    // 从旧的INI文件读取配置
    uint8_t oldOkTimes = oldConfig.getFaculaOkTimes();
    uint32_t oldSolidTime = oldConfig.getSolidTime();
    uint8_t oldNgHandle = oldConfig.getFaculaNgHandle();
    
    // 迁移到新配置
    newConfig->setFaculaOkTimes(oldOkTimes);
    newConfig->setSolidTime(oldSolidTime);
    newConfig->setFaculaNgHandle(oldNgHandle);
    
    // 保存新配置
    CONFIG_SERVICE().saveConfig("AdjustProcess");
    
    qDebug() << "配置迁移完成: 旧INI -> 新JSON";
}
```

## 测试方案

### 配置测试

```cpp
#ifdef ENABLE_CONFIG_TESTS
void testAdjustProcessConfig() {
    auto* config = CONFIG_SERVICE().getConfig<LensAdjust::AdjustProcessConfigData>("AdjustProcess");
    
    // 测试参数设置
    config->setFaculaOkTimes(5);
    QCOMPARE(config->getFaculaOkTimes(), 5);
    
    config->setSolidTime(2000);
    QCOMPARE(config->getSolidTime(), 2000u);
    
    config->setFaculaNgHandle(2);
    QCOMPARE(config->getFaculaNgHandle(), 2);
    
    // 测试配置验证
    QVERIFY(config->validate());
    
    // 测试异常处理描述
    QString desc = config->getFaculaNgHandleDescription();
    QVERIFY(!desc.isEmpty());
}
#endif
```

### 流程测试

```cpp
#ifdef ENABLE_CONFIG_MOCK_SIGNALS
void testAdjustProcessFlow() {
    // 模拟调节流程
    auto* config = CONFIG_SERVICE().getConfig<LensAdjust::AdjustProcessConfigData>("AdjustProcess");
    
    // 设置测试参数
    config->setFaculaOkTimes(2);
    config->setSolidTime(100);
    config->setFaculaNgHandle(1);
    
    // 模拟调节流程执行
    CLensAdjust adjustController;
    
    // 使用QTimer模拟异步调节过程
    QTimer::singleShot(100, [&]() {
        adjustController.executeAdjustProcess();
    });
}
#endif
```

## 性能考虑

1. **配置缓存** - 缓存频繁访问的配置参数
2. **异步处理** - 配置加载和保存使用异步机制
3. **内存优化** - 合理管理配置对象生命周期
4. **响应时间** - 确保配置访问的快速响应

## 维护指南

1. **定期检查配置文件完整性**
2. **监控调节流程的执行状态**
3. **备份重要的配置参数**
4. **更新配置文档和注释**
5. **性能监控和优化**

镜头调节模块通过模块化的配置管理，为系统提供了精确可控的光路调节能力。
