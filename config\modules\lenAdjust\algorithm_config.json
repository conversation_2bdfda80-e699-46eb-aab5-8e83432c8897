{"_comment": "算法配置文件 - 光路调节模块", "_version": "1.0", "_description": "传统算法参数和图像处理算法配置", "_last_updated": "2024-01-01T00:00:00Z", "parameters": {"_comment": "传统算法参数（从XML迁移）", "initial_x_dist": 0, "initial_y_dist": 0, "initial_z_dist": 0, "find_origin_raduis": 140, "find_angle_step": 20, "find_radius_step": 140, "find_times": 4, "discard_pack_num": 1, "default_z_direct": 1, "z_move_step": 3, "peak_ok_threshold": 550, "Amp_select": 30, "ALR_mp_peak": 20, "ALR_mp_peak_threshold": 100, "AUD_mp_peak": 0, "AUD_mp_peak_threshold": 100, "Aedge_peak_threshold": 180, "ACR_peak_delta": 120, "ARR_peak_delta": 50, "AMax_peak": 650, "edge_peak_threshold": 50, "peak_threshold": 600, "peak_max_threshold": 1000, "CR_peak_delta": 150, "FT_LRmp_adjust_peak": 25, "FT_LRmp_adjust_peak_threshold": 5, "FT_UDmp_adjust_peak": 0, "FT_UDmp_adjust_peak_threshold": 100, "FT_LRmp_solid_peak": 25, "FT_LRmp_solid_peak_threshold": 8, "FT_UDmp_solid_peak": 0, "FT_UDmp_solid_peak_threshold": 100, "FT_LRmp_deflate_peak": 25, "FT_LRmp_deflate_peak_threshold": 8, "FT_UDmp_deflate_peak": 0, "FT_UDmp_deflate_peak_threshold": 100, "_parameter_descriptions": {"initial_x_dist": "X轴初始移动距离（微米）", "initial_y_dist": "Y轴初始移动距离（微米）", "initial_z_dist": "Z轴初始移动距离（微米）", "find_origin_raduis": "寻找原点的搜索半径（微米）", "find_angle_step": "角度搜索步长（度）", "find_radius_step": "半径搜索步长（微米）", "find_times": "最大搜索次数", "discard_pack_num": "丢弃数据包数量", "default_z_direct": "Z轴默认方向：1-向上，-1-向下", "z_move_step": "Z轴移动步长", "peak_ok_threshold": "峰值OK阈值", "Amp_select": "幅度选择参数", "ALR_mp_peak": "左右多点峰值", "ALR_mp_peak_threshold": "左右多点峰值阈值", "AUD_mp_peak": "上下多点峰值", "AUD_mp_peak_threshold": "上下多点峰值阈值", "Aedge_peak_threshold": "边缘峰值阈值", "ACR_peak_delta": "中心峰值差值", "ARR_peak_delta": "径向峰值差值", "AMax_peak": "最大峰值", "edge_peak_threshold": "边缘峰值阈值", "peak_threshold": "峰值阈值", "peak_max_threshold": "峰值最大阈值", "CR_peak_delta": "中心径向峰值差值"}}, "image_processing": {"_comment": "图像处理算法参数（从FaculaConfig分离）", "interpolation_type": 0, "filter_types": "6", "interpolation_offset": 0.5, "kalman_strength": 1.0, "filter_strength": 1.0, "convolution": {"_comment": "卷积滤波器参数", "kernel_size": 3, "preset": "sharpen", "_presets": ["sharpen", "edge_detect", "emboss", "custom"], "_description": "卷积滤波用于图像锐化和边缘检测"}, "median": {"_comment": "中值滤波器参数", "kernel_size": 3, "preset": "noise_reduction", "_presets": ["noise_reduction", "salt_pepper", "custom"], "_description": "中值滤波用于去除椒盐噪声"}, "gaussian": {"_comment": "高斯滤波器参数", "sigma": 1.0, "kernel_size": 5, "preset": "medium_blur", "_presets": ["light_blur", "medium_blur", "heavy_blur", "custom"], "_description": "高斯滤波用于图像平滑和噪声抑制"}, "bilateral": {"_comment": "双边滤波器参数", "sigma_color": 75.0, "sigma_space": 75.0, "kernel_size": 5, "preset": "smooth", "_presets": ["smooth", "edge_preserve", "custom"], "_description": "双边滤波在平滑的同时保持边缘"}, "weighted_avg": {"_comment": "加权平均滤波器参数", "kernel_size": 3, "preset": "center_weighted", "_presets": ["center_weighted", "gaussian_weighted", "custom"], "_description": "加权平均滤波，中心权重更高"}, "_processing_descriptions": {"interpolation_type": "插值类型：0-双线性插值，1-双三次插值", "filter_types": "滤波器类型组合，格式：1,2,3（可组合使用）", "interpolation_offset": "插值偏移量，影响插值精度", "kalman_strength": "卡尔曼滤波强度，用于时序数据平滑", "filter_strength": "全局滤波强度，控制所有滤波器的整体效果"}}, "_migration_info": {"_comment": "配置迁移信息", "migrated_from": ["clen_config.xml", "FaculaConfig"], "migration_date": "auto-generated", "separated_from": "facula_config for better organization", "algorithm_separation": "将算法参数从光斑配置中分离，实现更好的职责划分"}, "_tuning_guidelines": {"peak_thresholds": "峰值阈值需要根据实际光斑强度调整，建议从默认值开始微调", "search_parameters": "搜索参数影响调节速度和精度，半径过大影响速度，过小可能找不到光斑", "filter_selection": "滤波器选择应根据图像噪声特性，一般噪声用高斯，椒盐噪声用中值", "interpolation": "插值参数影响亚像素精度，双三次插值精度更高但计算量大", "performance_vs_quality": "参数调整需要在处理速度和质量之间平衡"}}