#include "IModuleConfigManager.h"
#include <QDir>
#include <QDebug>

namespace Config {

bool IModuleConfigManager::ensureDirectoryExists(const QString &dirPath) const {
    QDir dir;
    if (!dir.exists(dirPath)) {
        if (!dir.mkpath(dirPath)) {
            logError("Failed to create directory: " + dirPath);
            return false;
        }
        logInfo("Created directory: " + dirPath);
    }
    return true;
}

void IModuleConfigManager::logInfo(const QString &message) const {
    qDebug() << "[" << getModuleName() << "ConfigManager]" << message;
}

void IModuleConfigManager::logError(const QString &message) const {
    qCritical() << "[" << getModuleName() << "ConfigManager]" << message;
}

} // namespace Config
