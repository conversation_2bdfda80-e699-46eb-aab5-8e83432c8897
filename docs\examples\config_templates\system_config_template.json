{"_comment": "System configuration for LA-T5 project", "_version": "1.0", "_description": "System-level configuration including version, MES, and device information", "sensor_board": {"_comment": "Sensor board version information", "version": "*******"}, "mes": {"_comment": "Manufacturing Execution System configuration", "userid": "admin", "op": "105", "work_number": "10001", "work_domain": "001", "_descriptions": {"userid": "User ID, super admin: admin; other examples: 001", "op": "Operation number, default 105", "work_number": "Work order number", "work_domain": "Work domain identifier"}}, "device": {"_comment": "Device configuration", "sensor_device": 4, "sensor_device_baud": 230400, "station_number": 1, "clens_machine_brand": 3, "_descriptions": {"sensor_device": "Device type: 1-D4/2-T4/3-D6/4-T5", "sensor_device_baud": "Device baud rate", "station_number": "Station number", "clens_machine_brand": "Lens adjustment device: 1-shunTuo; 2-qingHe; 3-qingHeShiJue"}, "_device_types": {"1": "D4", "2": "T4", "3": "D6", "4": "T5"}, "_machine_brands": {"1": "shun<PERSON>uo", "2": "qingHe", "3": "qingHeShiJue"}}, "_migration_info": {"migrated_from": "clen_config.ini", "migration_date": "auto-generated", "original_sections": ["SENSOR_BOARD", "MES", "DEVICE"]}}