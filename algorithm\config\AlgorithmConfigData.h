#pragma once

#include "../../components/configModule/ConfigTypeRegistry.h"
#include "../../components/configModule/IConfigData.h"
#include <QMap>
#include <QPoint>
#include <QString>
#include <QStringList>
#include <QVariantMap>
#include <QVector>

namespace Algorithm {

/**
 * @brief 算法模块配置数据
 *
 * 管理算法模块的所有配置参数：
 * - 传统算法参数映射
 * - 图像处理算法参数
 * - 插值和滤波器配置
 * - 光斑检测参数
 *
 * 设计特点：
 * - 继承IConfigData接口，支持动态注册
 * - 在algorithm模块中定义和管理
 * - 提供类型安全的参数访问
 * - 支持参数验证和默认值设置
 */
class AlgorithmConfigData : public Config::BaseConfigData<AlgorithmConfigData> {
  public:
    AlgorithmConfigData();
    ~AlgorithmConfigData() override = default;

    // 静态类型名称（用于注册）
    static QString staticTypeName() {
        return "Algorithm";
    }

    // IConfigData接口实现
    QString getTypeName() const override {
        return staticTypeName();
    }
    QString getVersion() const override {
        return "2.1.0";
    }
    QString getDescription() const override {
        return "算法模块配置参数，包含传统算法、图像处理和光斑检测参数";
    }

    QVariantMap toVariantMap() const override;
    bool        fromVariantMap(const QVariantMap &data) override;
    bool        validate() const override;
    void        setDefaults() override;

    QStringList getFieldNames() const override;
    QString     getFieldType(const QString &fieldName) const override;
    QString     getFieldDescription(const QString &fieldName) const override;
    bool        hasField(const QString &fieldName) const override;
    QVariant    getFieldValue(const QString &fieldName, const QVariant &defaultValue = QVariant()) const override;
    bool        setFieldValue(const QString &fieldName, const QVariant &value) override;
    bool        resetField(const QString &fieldName) override;

    // 算法参数访问接口
    int                       getParameter(const QString &key, int defaultValue = 0) const;
    void                      setParameter(const QString &key, int value);
    const QMap<QString, int> &getAllParameters() const {
        return parameters;
    }
    void setAllParameters(const QMap<QString, int> &params) {
        parameters = params;
    }

    // 图像处理参数访问接口
    uint8_t getInterpolationType() const {
        return interpolation_type;
    }
    void setInterpolationType(uint8_t type) {
        interpolation_type = type;
    }

    const QString &getFilterTypes() const {
        return filter_types;
    }
    void setFilterTypes(const QString &types) {
        filter_types = types;
    }

    float getFilterStrength() const {
        return filter_strength;
    }
    void setFilterStrength(float strength) {
        filter_strength = strength;
    }

    // 光斑检测参数访问接口
    const QString &getFaculaCenterChannels() const {
        return facula_center_channels;
    }
    void setFaculaCenterChannels(const QString &channels) {
        facula_center_channels = channels;
    }

    const QVector<QPoint> &getFaculaCenterPoints() const {
        return facula_center_points;
    }
    void setFaculaCenterPoints(const QVector<QPoint> &points) {
        facula_center_points = points;
    }

    uint32_t getFaculaCenterPeakThreshold() const {
        return facula_center_peak_threshold;
    }
    void setFaculaCenterPeakThreshold(uint32_t threshold) {
        facula_center_peak_threshold = threshold;
    }

    uint8_t getFaculaHandleType() const {
        return facula_handle_type;
    }
    void setFaculaHandleType(uint8_t type) {
        facula_handle_type = type;
    }

    // 便利方法
    bool hasParameter(const QString &key) const;
    bool removeParameter(const QString &key);
    int  getParameterCount() const {
        return parameters.size();
    }
    void clearParameters() {
        parameters.clear();
    }
    QStringList getParameterNames() const;
    void        importParameters(const AlgorithmConfigData &other, bool overwrite = true);

  public:
    // 传统算法参数
    QMap<QString, int> parameters;  // 传统算法参数映射 (60+个参数)

    // 图像处理算法参数
    uint8_t interpolation_type;        // 插值类型 (0-3: 最近邻、双线性、双三次、Lanczos)
    QString filter_types;              // 滤波器类型列表 (逗号分隔)
    float   interpolation_offset;      // 插值偏移量
    float   kalman_strength;           // 卡尔曼滤波强度
    uint8_t convolution_kernel_size;   // 卷积核大小
    QString convolution_preset;        // 卷积预设
    uint8_t median_kernel_size;        // 中值滤波核大小
    QString median_preset;             // 中值滤波预设
    float   gaussian_sigma;            // 高斯滤波标准差
    uint8_t gaussian_kernel_size;      // 高斯滤波核大小
    QString gaussian_preset;           // 高斯滤波预设
    float   bilateral_sigma_color;     // 双边滤波颜色标准差
    float   bilateral_sigma_space;     // 双边滤波空间标准差
    uint8_t bilateral_kernel_size;     // 双边滤波核大小
    QString bilateral_preset;          // 双边滤波预设
    uint8_t weighted_avg_kernel_size;  // 加权均值滤波核大小
    QString weighted_avg_preset;       // 加权均值滤波预设
    float   filter_strength;           // 全局滤波强度

    // 光斑检测参数（从FaculaConfig迁移过来）
    QString         facula_center_channels;        // 多通道配置字符串
    QVector<QPoint> facula_center_points;          // 解析后的通道坐标列表
    uint32_t        facula_center_peak_threshold;  // 多通道模式下的peak阈值
    uint8_t         facula_center_loc_x;           // 单点X坐标（兼容性）
    uint8_t         facula_center_loc_y;           // 单点Y坐标（兼容性）
    uint8_t         facula_handle_type;            // 处理类型：0-基础，1-增强，2-高精度

  private:
    void            loadDefaultParameters();
    bool            validateParameterValue(const QString &key, int value) const;
    QPair<int, int> getParameterRange(const QString &key) const;
    int             getParameterDefaultValue(const QString &key) const;

    // 字段信息映射表
    static const QMap<QString, QString>         s_fieldTypes;
    static const QMap<QString, QString>         s_fieldDescriptions;
    static const QMap<QString, QPair<int, int>> s_parameterRanges;
    static const QMap<QString, int>             s_defaultParameters;
};

}  // namespace Algorithm
